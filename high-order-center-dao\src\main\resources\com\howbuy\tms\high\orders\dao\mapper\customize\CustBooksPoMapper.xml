<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.tms.high.orders.dao.mapper.customize.CustBooksPoMapper">
    <resultMap id="BaseResultMap" type="com.howbuy.tms.high.orders.dao.po.CustBooksPo"
               extends="com.howbuy.tms.high.orders.dao.mapper.CustBooksPoAutoMapper.BaseResultMap">
    </resultMap>

    <!--自定义sql-->
    <!-- 查询客户基金在中台的所有份额 -->
    <select id="selectCustAllBooksByTxAcctNoAndFundCode" parameterType="map" resultMap="BaseResultMap">
        select tt.* from(
        select T3.tx_acct_no,
        T3.dis_code,
        T3.product_code,
        sum(T3.Balance_Vol) as Balance_Vol,
        abs(sum(T3.Unconfirmed_Vol)) as Unconfirmed_Vol,
        sum(T3.just_frzn_vol) as just_frzn_vol,
        sum(T3.Unconfirmed_Amt) as Unconfirmed_Amt
        from (select T2.tx_acct_no,
        T2.dis_code,
        T2.product_code,
        ifnull(T2.Balance_Vol, 0) as balance_vol,
        ifnull(T2.Unconfirmed_Vol, 0) as Unconfirmed_Vol,
        ifnull(T2.just_frzn_vol, 0) as just_frzn_vol,
        ifnull(T2.Unconfirmed_Amt, 0) as Unconfirmed_Amt
        from cust_books T2
        where T2.TX_ACCT_NO = #{txAcctNo,jdbcType=VARCHAR}
        AND T2.PRODUCT_CODE = #{productCode,jdbcType=VARCHAR}
        AND T2.PROTOCOL_TYPE = '4'
        <if test="disCodeList != null and disCodeList.size() > 0 ">
            and T2.dis_code in
            <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                #{disCode}
            </foreach>
        </if>
        union all
        select T1.tx_acct_no,
        T1.dis_code,
        T1.product_code,
        sum(ifnull(T1.Ack_Vol, 0)) as balance_vol,
        sum(ifnull(T1.App_Vol, 0)) as Unconfirmed_Vol,
        null as just_frzn_vol,
        sum(ifnull(T1.App_Amt, 0)) as Unconfirmed_Amt
        from cust_books_dtl T1
        where T1.TX_ACCT_NO = #{txAcctNo,jdbcType=VARCHAR}
        AND T1.PRODUCT_CODE = #{productCode,jdbcType=VARCHAR}
        AND T1.PROTOCOL_TYPE = '4'
        <if test="disCodeList != null and disCodeList.size() > 0 ">
            and T1.dis_code in
            <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                #{disCode}
            </foreach>
        </if>
        group by T1.tx_acct_no,
        T1.dis_code,
        T1.product_code) T3
        group by T3.tx_acct_no,
        T3.dis_code,
        T3.product_code) tt
        where tt.Balance_Vol &gt; 0
    </select>

    <!-- 查询客户基金在中台的存量份额 -->
    <select id="selectStockBooksByTxAcctNoAndFundCode" parameterType="map" resultMap="BaseResultMap">
        select T2.tx_acct_no,
               T2.product_code,
               T2.protocol_no,
               T2.cp_acct_no,
               ifnull(sum(T2.Balance_Vol), 0)     as BALANCE_VOL,
               ifnull(sum(T2.Unconfirmed_Vol), 0) as UNCONFIRMED_VOL,
               ifnull(sum(T2.Unconfirmed_Amt), 0) as UNCONFIRMED_AMT
        from cust_books T2
        where T2.TX_ACCT_NO = #{txAcctNo,jdbcType=VARCHAR}
          AND T2.PRODUCT_CODE = #{productCode,jdbcType=VARCHAR}
          AND T2.PROTOCOL_TYPE = '4'
        group by T2.tx_acct_no, T2.product_code, T2.protocol_no, T2.cp_acct_no
    </select>

    <!-- 查询客户资金账号、协议号、分销机构 -->
    <select id="selectThreeElementsByTxAcctNoAndFundCode" parameterType="map" resultMap="BaseResultMap">
        select T2.tx_acct_no,
               T2.product_code,
               T2.protocol_no,
               T2.cp_acct_no,
               T2.dis_code
        from cust_books T2
        where T2.TX_ACCT_NO = #{txAcctNo,jdbcType=VARCHAR}
          AND T2.PRODUCT_CODE = #{productCode,jdbcType=VARCHAR}
          AND T2.PROTOCOL_TYPE = '4'
        group by T2.tx_acct_no, T2.product_code, T2.protocol_no, T2.cp_acct_no, T2.dis_code
    </select>

    <!-- 查询某只基金在指定协议下是否有持仓 -->
    <select id="selectCustBooksForModifyDiv" resultMap="BaseResultMap" parameterType="map">
        select sum(BALANCE_VOL) as BALANCE_VOL
        from CUST_BOOKS
        where TX_ACCT_NO = #{txAcctNo,jdbcType=VARCHAR}
          AND PROTOCOL_NO = #{protocolNo,jdbcType=VARCHAR}
          AND PRODUCT_CODE = #{fundCode,jdbcType=VARCHAR}
          AND PROTOCOL_TYPE = '2'
    </select>

    <select id="selectCustBooksByProtocolNo" resultMap="BaseResultMap" parameterType="map">
        select
        <include refid="com.howbuy.tms.high.orders.dao.mapper.CustBooksPoAutoMapper.Base_Column_List"/>
        from CUST_BOOKS
        where TX_ACCT_NO = #{txAcctNo,jdbcType=VARCHAR}
        AND PROTOCOL_NO = #{protocolNo,jdbcType=VARCHAR}
        AND DIS_CODE = #{disCode,jdbcType=VARCHAR}
    </select>

    <!-- 查询在途交易 -->
    <select id="selectInTransit" resultType="int" parameterType="com.howbuy.tms.high.orders.dao.po.CustBooksPo">
        select count(1)
        from (
                 select T3.tx_acct_no,
                        T3.dis_code,
                        T3.cp_acct_no,
                        T3.product_code,
                        T3.fund_share_class,
                        T3.protocol_no,
                        sum(T3.Balance_Vol)     as Balance_Vol,
                        sum(T3.Unconfirmed_Vol) as Unconfirmed_Vol,
                        sum(T3.Unconfirmed_Amt) as Unconfirmed_Amt
                 from (select T2.tx_acct_no,
                              T2.dis_code,
                              T2.cp_acct_no,
                              T2.product_code,
                              T2.fund_share_class,
                              T2.protocol_no,
                              T2.Balance_Vol,
                              T2.Unconfirmed_Vol,
                              T2.Unconfirmed_Amt
                       from cust_books T2
                       where t2.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
                         and t2.dis_code = #{disCode,jdbcType=VARCHAR}
                         and t2.protocol_no = #{protocolNo,jdbcType=VARCHAR}
                       union all
                       select T1.tx_acct_no,
                              T1.dis_code,
                              T1.cp_acct_no,
                              T1.product_code,
                              T1.fund_share_class,
                              T1.protocol_no,
                              sum(ifnull(T1.Ack_Vol, 0)) as balance_vol,
                              sum(ifnull(T1.App_Vol, 0)) as Unconfirmed_Vol,
                              sum(ifnull(T1.App_Amt, 0)) as Unconfirmed_Amt
                       from cust_books_dtl T1
                       where t1.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
                         and t1.dis_code = #{disCode,jdbcType=VARCHAR}
                         and t1.protocol_no = #{protocolNo,jdbcType=VARCHAR}

                       group by T1.tx_acct_no,
                                T1.dis_code,
                                T1.cp_acct_no,
                                T1.product_code,
                                T1.fund_share_class,
                                T1.protocol_no) T3
                 group by T3.tx_acct_no,
                          T3.dis_code,
                          T3.cp_acct_no,
                          T3.product_code,
                          T3.fund_share_class,
                          T3.protocol_no
             ) T4
        where T4.Unconfirmed_Vol <![CDATA[<]]> 0
           Or Unconfirmed_Amt <![CDATA[>]]> 0
    </select>


    <!-- 查询客户代销产品持仓列表: 持仓大于0 -->
    <select id="selectBalanceList" resultMap="BaseResultMap" parameterType="map">
        select product_code,
        DIS_CODE,
        balance_vol
        from (
        select t.product_code ,t.DIS_CODE ,sum(t.balance_vol) as balance_vol
        from cust_books t
        where t.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
        <if test="disCodeList != null and disCodeList.size() > 0 ">
            and t.dis_code in
            <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                #{disCode}
            </foreach>
        </if>
        <if test="productCode != null and productCode != '' ">
            and t.product_code = #{productCode, jdbcType=VARCHAR}
        </if>
        and t.protocol_type = #{protocolType,jdbcType=VARCHAR}
        group by t.product_code,t.DIS_CODE ) tt
        where balance_vol &gt; 0
    </select>

    <resultMap id="BalanceResultMap" type="com.howbuy.tms.high.orders.dao.vo.BalanceVo">
        <result column="PRODUCT_CODE" jdbcType="VARCHAR" property="productCode"/>
        <result column="FUND_SHARE_CLASS" jdbcType="CHAR" property="fundShareClass"/>
        <result column="PROTOCOL_NO" jdbcType="VARCHAR" property="protocolNo"/>
        <result column="CP_ACCT_NO" jdbcType="VARCHAR" property="cpAcctNo"/>
        <result column="DIS_CODE" jdbcType="VARCHAR" property="disCode"/>
        <result column="BALANCE_VOL" jdbcType="DECIMAL" property="balanceVol"/>
        <result column="UNCONFIRMED_VOL" jdbcType="DECIMAL" property="unconfirmedVol"/>
        <result column="LOCKING_PERIOD_VOL" jdbcType="DECIMAL" property="lockingPeriodVol"/>
        <result column="JUST_FRZN_VOL" jdbcType="DECIMAL" property="justFrznVol"/>
        <result column="UNCONFIRMED_AMT" jdbcType="DECIMAL" property="unconfirmedAmt"/>
        <result column="NET_BUY_AMOUNT" jdbcType="DECIMAL" property="netBuyAmount"/>
        <result column="open_rede_dt" jdbcType="VARCHAR" property="openRedeDt"/>
        <result column="PRODUCT_CHANNEL" jdbcType="VARCHAR" property="productChannel"/>
        <result column="PROTOCOL_TYPE" jdbcType="VARCHAR" property="protocolType"/>
    </resultMap>
    <!-- 查询客户代销产品持仓信息 -->
    <select id="selectBalanceWithLockPeriod" resultMap="BalanceResultMap" parameterType="map">
        select * from (
        select t.product_code as product_code,
        t.fund_share_class as fund_share_class,
        t.DIS_CODE as dis_code,
        sum(t.balance_vol) as balance_vol,
        abs(sum(t.unconfirmed_vol)) as unconfirmed_vol,
        abs(sum(t.just_frzn_vol)) as just_frzn_vol,
        sum(t.unconfirmed_amt) as unconfirmed_amt
        from (select t2.product_code as product_code,
        t2.fund_share_class as fund_share_class,
        t2.DIS_CODE ,
        ifnull(t2.ack_vol, 0) as balance_vol,
        ifnull(t2.app_vol, 0) as unconfirmed_vol,
        0 as just_frzn_vol,
        ifnull(t2.app_amt, 0) as unconfirmed_amt
        from cust_books_dtl t2
        where t2.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
        <if test="list != null and list.size() > 0 ">
            and t2.dis_code in
            <foreach collection="list" index="index" item="disCode" open="(" separator="," close=")">
                #{disCode}
            </foreach>
        </if>
        <if test="fundCodeList != null and fundCodeList.size() > 0 ">
            and t2.product_code in
            <foreach collection="fundCodeList" index="index" item="fundCode" open="(" separator="," close=")">
                #{fundCode}
            </foreach>
        </if>
        and t2.protocol_type = '4'
        union all
        select t3.product_code as product_code,
        t3.fund_share_class as fund_share_class,
        t3.DIS_CODE,
        ifnull(t3.balance_vol, 0) as balance_vol,
        ifnull(t3.unconfirmed_vol, 0) as unconfirmed_vol,
        ifnull(t3.just_frzn_vol, 0) as just_frzn_vol,
        ifnull(t3.unconfirmed_amt, 0) as unconfirmed_amt
        from cust_books t3
        where t3.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
        <if test="list != null and list.size() > 0 ">
            and t3.dis_code in
            <foreach collection="list" index="index" item="disCode" open="(" separator="," close=")">
                #{disCode}
            </foreach>
        </if>
        <if test="fundCodeList != null and fundCodeList.size() > 0 ">
            and t3.product_code in
            <foreach collection="fundCodeList" index="index" item="fundCode" open="(" separator="," close=")">
                #{fundCode}
            </foreach>
        </if>
        and t3.protocol_type = '4') t
        group by t.product_code, t.fund_share_class,t.DIS_CODE
        ) tt
        <where>
            <bind name="status" value="balanceStatus.toString()"/>
            <if test='status == "0"'>
                balance_vol = 0
            </if>
            <if test='status == "1"'>
                balance_vol > 0
            </if>
            <if test='status == "2"'>
                1=1
            </if>
        </where>
    </select>

    <resultMap id="FundAcctMap" type="com.howbuy.tms.high.orders.dao.vo.FundAcctVo">
        <result column="PRODUCT_CODE" jdbcType="VARCHAR" property="fundCode"/>
        <result column="TX_ACCT_NO" jdbcType="VARCHAR" property="txAcctNo"/>
    </resultMap>

    <select id="selectHzBalanceByFundCode" resultMap="FundAcctMap" parameterType="map">
        select PRODUCT_CODE, TX_ACCT_NO
        from CUST_BOOKS
        where BALANCE_VOL > 0
        and DIS_CODE = 'HZ000N001'
        <if test="fundCode != null and fundCode != '' ">
            and PRODUCT_CODE =#{fundCode,jdbcType=VARCHAR}
        </if>
        <if test="txAcctNo != null and txAcctNo != '' ">
            and TX_ACCT_NO =#{txAcctNo,jdbcType=VARCHAR}
        </if>
        group by PRODUCT_CODE, TX_ACCT_NO
    </select>

    <!-- 查询累计购买净金额代销 -->
    <select id="selectNetBuyAmountDX" parameterType="map" resultMap="BalanceResultMap">
        select t.fund_code as product_code, sum(t.net_buy_amount) as net_buy_amount
        from (select t.fund_code,
        ifnull(t.ack_amt, 0) as net_buy_amount
        from high_deal_order_dtl t
        where t.tx_ack_flag in ('3','4')
        and t.m_busi_code in ('1120','1122')
        and t.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
        and t.dis_code = #{disCode,jdbcType=VARCHAR}
        <if test="list != null and list.size() > 0 ">
            and t.fund_code in
            <foreach collection="list" index="index" item="fundCode" open="(" separator="," close=")">
                #{fundCode}
            </foreach>
        </if>
        ) t
        group by t.fund_code
    </select>

    <!-- 查询累计购买净金额直销 -->
    <select id="selectNetBuyAmountZX" parameterType="map" resultMap="BalanceResultMap">
        select t.product_code, sum(t.net_buy_amount) as net_buy_amount
        from (select t.fundcode as product_code,
        ifnull(CASE
        WHEN t.busicode = '134' THEN t.ackvol
        WHEN t.busicode = '144' THEN t.ackvol
        ELSE IFNULL(t.ackamt, 0) - IFNULL(t.fee, 0)
        END, 0) as net_buy_amount
        from cm_custtrade_direct t
        where t.hboneno = #{hbOneNo,jdbcType=VARCHAR}
        and t.busicode in ('120','122','134','144','12B')
        and t.recstat = '0'
        <if test="list != null and list.size() > 0 ">
            and t.fundcode in
            <foreach collection="list" index="index" item="fundCode" open="(" separator="," close=")">
                #{fundCode}
            </foreach>
        </if>
        ) t
        group by t.product_code
    </select>


    <!-- 查询累计购买净金额-->
    <select id="selectNetBuyAmount" parameterType="map" resultMap="BalanceResultMap">
        select t.fund_code as product_code, sum(t.net_buy_amount) as net_buy_amount
        from (select t.fund_code,
        IFNULL(
        CASE
        WHEN t.m_busi_code = '1134' THEN t.ack_vol
        WHEN t.m_busi_code = '1144' THEN t.ack_vol
        ELSE IFNULL(t.ack_amt, 0) - IFNULL(t.fee, 0)
        END,
        0
        ) as net_buy_amount
        from high_deal_order_dtl t
        where t.tx_ack_flag in ('3','4')
        and t.m_busi_code in ('1120','1122', '1134', '1144')
        and t.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
        <if test="disCodeList != null and disCodeList.size() > 0 ">
            and t.dis_code in
            <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                #{disCode}
            </foreach>
        </if>
        <if test="list != null and list.size() > 0 ">
            and t.fund_code in
            <foreach collection="list" index="index" item="fundCode" open="(" separator="," close=")">
                #{fundCode}
            </foreach>
        </if>
        union all
        select t.fundcode as fund_code,
        IFNULL(
        CASE
        WHEN t.busicode = '134' THEN t.ackvol
        WHEN t.busicode = '144' THEN t.ackvol
        ELSE IFNULL(t.ackamt, 0) - IFNULL(t.fee, 0)
        END,
        0
        ) AS net_buy_amount
        from cm_custtrade_direct t
        where t.hboneno = #{hbOneNo,jdbcType=VARCHAR}
        and t.busicode in ('120','122','134','144','12B')
        <if test="list != null and list.size() > 0 ">
            and t.fundcode in
            <foreach collection="list" index="index" item="fundCode" open="(" separator="," close=")">
                #{fundCode}
            </foreach>
        </if>
        ) t
        group by t.fund_code
    </select>

    <select id="queryConfirmBalanceBaseInfo" resultMap="BalanceResultMap"
            parameterType="com.howbuy.tms.high.orders.dao.vo.QueryAcctBalanceBaseInfoParamVo">
        select * from CUST_BOOKS
        where
        BALANCE_VOL>0
        <if test="paramVo.disCodeList != null and paramVo.disCodeList.size() > 0 ">
            and DIS_CODE in
            <foreach collection="paramVo.disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                #{disCode}
            </foreach>
        </if>
        <if test="paramVo.fundCodeList != null and paramVo.fundCodeList.size() > 0 ">
            and PRODUCT_CODE in
            <foreach collection="paramVo.fundCodeList" index="index" item="fundCode" open="(" separator="," close=")">
                #{fundCode}
            </foreach>
        </if>
        <if test="paramVo.txAcctNo != null and paramVo.txAcctNo != ''">
            and TX_ACCT_NO = #{paramVo.txAcctNo,jdbcType=VARCHAR}
        </if>
    </select>

    <!-- 查询客户产品持仓明细信息 -->
    <select id="selectBalanceDtl" resultMap="BalanceResultMap" parameterType="map">
        select * from (
        select t.protocol_no as protocol_no,
        t.product_code as product_code,
        t.fund_share_class as fund_share_class,
        t.cp_acct_no as cp_acct_no,
        sum(t.balance_vol) as balance_vol,
        abs(sum(t.unconfirmed_vol)) as unconfirmed_vol,
        sum(t.locking_period_vol) as locking_period_vol,
        sum(t.just_frzn_vol) as just_frzn_vol,
        min(t.open_rede_dt) as open_rede_dt
        from (select t1.fund_code as product_code,
        t1.fund_share_class as fund_share_class,
        t1.protocol_no as protocol_no,
        t1.cp_acct_no as cp_acct_no,
        0 as balance_vol,
        0 as unconfirmed_vol,
        ifnull(t1.balance_vol, 0) as locking_period_vol,
        0 as just_frzn_vol,
        t1.open_rede_dt
        from sub_cust_books t1
        <where>
            t1.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
            and t1.dis_code = #{disCode,jdbcType=VARCHAR}
            and t1.protocol_type = '4'
            <if test=" productCode != null and productCode != ''">
                and t1.fund_code = #{productCode,jdbcType=VARCHAR}
            </if>
            <if test=" protocolNo != null and protocolNo != ''">
                and t1.protocol_no = #{protocolNo,jdbcType=VARCHAR}
            </if>
            <if test=" cpAcctNo != null and cpAcctNo != ''">
                and t1.cp_acct_no = #{cpAcctNo,jdbcType=VARCHAR}
            </if>
            and t1.open_rede_dt is not null
            and t1.open_rede_dt &gt; #{taTradeDt,jdbcType=VARCHAR}
        </where>
        union all
        select t2.product_code as product_code,
        t2.fund_share_class as fund_share_class,
        t2.protocol_no as protocol_no,
        t2.cp_acct_no as cp_acct_no,
        ifnull(t2.ack_vol, 0) as balance_vol,
        ifnull(t2.app_vol, 0) as unconfirmed_vol,
        0 as locking_period_vol,
        0 as just_frzn_vol,
        null as open_rede_dt
        from cust_books_dtl t2
        <where>
            t2.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
            and t2.dis_code = #{disCode,jdbcType=VARCHAR}
            <if test=" productCode != null and productCode != ''">
                and t2.product_code = #{productCode,jdbcType=VARCHAR}
            </if>
            <if test=" protocolNo != null and protocolNo != ''">
                and t2.protocol_no = #{protocolNo,jdbcType=VARCHAR}
            </if>
            <if test=" cpAcctNo != null and cpAcctNo != ''">
                and t2.cp_acct_no = #{cpAcctNo,jdbcType=VARCHAR}
            </if>
            and t2.protocol_type = '4'
        </where>
        union all
        select t3.product_code as product_code,
        t3.fund_share_class as fund_share_class,
        t3.protocol_no as protocol_no,
        t3.cp_acct_no as cp_acct_no,
        ifnull(t3.balance_vol, 0) as balance_vol,
        ifnull(t3.unconfirmed_vol, 0) as unconfirmed_vol,
        0 as locking_period_vol,
        ifnull(t3.just_frzn_vol, 0) as just_frzn_vol,
        null as open_rede_dt
        from cust_books t3
        where t3.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
        and t3.dis_code = #{disCode,jdbcType=VARCHAR}
        <if test=" productCode != null and productCode != ''">
            and t3.product_code = #{productCode,jdbcType=VARCHAR}
        </if>
        <if test=" protocolNo != null and protocolNo != ''">
            and t3.protocol_no = #{protocolNo,jdbcType=VARCHAR}
        </if>
        <if test=" cpAcctNo != null and cpAcctNo != ''">
            and t3.cp_acct_no = #{cpAcctNo,jdbcType=VARCHAR}
        </if>
        and t3.protocol_type = '4') t
        group by t.protocol_no, t.product_code, t.fund_share_class, t.cp_acct_no
        ) tt
        where balance_vol &gt; 0
    </select>

    <!-- 查询历史购买资金账号 -->
    <select id="selectHisCpAcctNo" parameterType="map" resultType="string">
        select distinct b.cp_acct_no from cust_books b
        where b.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
        and b.dis_code = #{disCode,jdbcType=VARCHAR}
        <if test=" fundCode != null and fundCode != ''">
            and b.product_code = #{fundCode,jdbcType=VARCHAR}
        </if>
        <if test=" taCode != null and taCode != ''">
            and b.ta_code = #{taCode,jdbcType=VARCHAR}
        </if>
        and b.protocol_type = '4'
    </select>

    <!-- 查询买入在途总金额 -->
    <select id="selectBuyOnWayAmt" parameterType="map" resultType="DECIMAL">
        select ABS(sum(t.unconfirmed_amt))
        from (select ifnull(t1.unconfirmed_amt, 0) as unconfirmed_amt
        from cust_books t1
        where t1.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
        <if test="disCodeList != null and disCodeList.size() > 0 ">
            and t1.dis_code in
            <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                #{disCode}
            </foreach>
        </if>
        and t1.protocol_type = '4'
        union all
        select ifnull(t2.app_amt, 0) as unconfirmed_amt
        from cust_books_dtl t2
        where t2.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
        <if test="disCodeList != null and disCodeList.size() > 0 ">
            and t2.dis_code in
            <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                #{disCode}
            </foreach>
        </if>
        and t2.protocol_type = '4'
        union all
        select ifnull(t3.appamt, 0) as unconfirmed_amt
        from CM_CUSTTRADE_DIRECT t3
        where t3.txacctno = #{txAcctNo,jdbcType=VARCHAR}
        <if test="disCodeList != null and disCodeList.size() > 0 ">
            and t3.discode in
            <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                #{disCode}
            </foreach>
        </if>
        and t3.ORDERSTATE = '1'
        and t3.recstat = '0'
        and t3.busicode in ('120','122','12B')
        ) t
    </select>

    <select id="selectRdmOnWayBalance" parameterType="map" resultMap="BaseResultMap">
        select TT.PRODUCT_CODE,
               ABS(TT.UNCONFIRMED_VOL) as UNCONFIRMED_VOL
        from (
                 select t.product_code,
                        sum(t.unconfirmed_vol) as unconfirmed_vol
                 from (select t1.product_code,
                              ifnull(t1.unconfirmed_vol, 0) as unconfirmed_vol
                       from cust_books t1
                       where t1.dis_code = #{disCode,jdbcType=VARCHAR}
                         and t1.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
                         and t1.protocol_type = '4'
                       union all
                       select t2.product_code,
                              ifnull(t2.app_vol, 0) as unconfirmed_vol
                       from cust_books_dtl t2
                       where t2.dis_code = #{disCode,jdbcType=VARCHAR}
                         and t2.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
                         and t2.protocol_type = '4'
                      ) t
                 group by t.product_code
             ) TT
        where ABS(TT.UNCONFIRMED_VOL) > 0
    </select>

    <!-- 查询用户指定产品所有分红金额的累计 -->
    <select id="selectAllBonusAmt" parameterType="map" resultType="DECIMAL">
        SELECT SUM(T.ACK_AMT)
        FROM HIGH_DEAL_ORDER_DTL T
        WHERE T.TX_ACCT_NO = #{txAcctNo,jdbcType=VARCHAR}
          AND T.DIS_CODE = #{disCode,jdbcType=VARCHAR}
          AND T.FUND_CODE = #{productCode,jdbcType=VARCHAR}
          AND T.M_BUSI_CODE = '1143'
    </select>

    <!-- 根据交易账号、协议号、资金账号、产品代码、分销机构号、份额类型查询持仓信息 -->
    <select id="selectByProtocolNoAndCpAcctNo" resultMap="BaseResultMap" parameterType="map">
        select tt.* from(
        select T3.tx_acct_no,
        T3.dis_code,
        T3.cp_acct_no,
        T3.product_code,
        T3.fund_share_class,
        T3.protocol_no,
        sum(T3.Balance_Vol) as Balance_Vol,
        sum(T3.Unconfirmed_Vol) as Unconfirmed_Vol,
        sum(T3.Unconfirmed_Amt) as Unconfirmed_Amt
        from (select T2.tx_acct_no,
        T2.dis_code,
        T2.cp_acct_no,
        T2.product_code,
        T2.fund_share_class,
        T2.protocol_no,
        T2.Balance_Vol,
        T2.Unconfirmed_Vol,
        T2.Unconfirmed_Amt
        from cust_books T2
        where t2.TX_ACCT_NO = #{txAcctNo,jdbcType=VARCHAR}
        AND t2.DIS_CODE = #{disCode,jdbcType=VARCHAR}
        <if test="protocolNo != null">
            AND t2.PROTOCOL_NO = #{protocolNo,jdbcType=VARCHAR}
        </if>
        <if test="protocolType != null">
            AND t2.PROTOCOL_TYPE = #{protocolType,jdbcType=VARCHAR}
        </if>
        AND T2.PRODUCT_CODE = #{productCode,jdbcType=VARCHAR}
        AND T2.FUND_SHARE_CLASS = #{fundShareClass,jdbcType=VARCHAR}
        AND T2.CP_ACCT_NO = #{cpAcctNo,jdbcType=VARCHAR}
        union all
        select T1.tx_acct_no,
        T1.dis_code,
        T1.cp_acct_no,
        T1.product_code,
        T1.fund_share_class,
        T1.protocol_no,
        sum(ifnull(T1.Ack_Vol, 0)) as balance_vol,
        sum(ifnull(T1.App_Vol,0)) as Unconfirmed_Vol,
        sum(ifnull(T1.App_Amt,0)) as Unconfirmed_Amt
        from cust_books_dtl T1
        where t1.TX_ACCT_NO = #{txAcctNo,jdbcType=VARCHAR}
        AND t1.DIS_CODE = #{disCode,jdbcType=VARCHAR}
        <if test="protocolNo != null">
            AND t1.PROTOCOL_NO = #{protocolNo,jdbcType=VARCHAR}
        </if>
        <if test="protocolType != null">
            AND t1.PROTOCOL_TYPE = #{protocolType,jdbcType=VARCHAR}
        </if>
        AND T1.PRODUCT_CODE = #{productCode,jdbcType=VARCHAR}
        AND T1.FUND_SHARE_CLASS = #{fundShareClass,jdbcType=VARCHAR}
        AND T1.CP_ACCT_NO = #{cpAcctNo,jdbcType=VARCHAR}
        group by T1.tx_acct_no,
        T1.dis_code,
        T1.cp_acct_no,
        T1.product_code,
        T1.fund_share_class,
        T1.protocol_no) T3
        group by T3.tx_acct_no,
        T3.dis_code,
        T3.cp_acct_no,
        T3.product_code,
        T3.fund_share_class,
        T3.protocol_no) tt
        where tt.Balance_Vol+tt.Unconfirmed_Vol > 0
    </select>

    <!-- 汇总持仓份额(银行卡维度) -->
    <select id="countCustTotalBalance" resultType="DECIMAL" parameterType="java.lang.String">
        SELECT SUM(ifnull(BALANCE_VOL, 0))
        FROM (SELECT SUM(T2.BALANCE_VOL) AS BALANCE_VOL
              FROM CUST_BOOKS T2
              WHERE T2.TX_ACCT_NO = #{txAcctNo,jdbcType=VARCHAR}
                AND T2.DIS_CODE = #{disCode,jdbcType=VARCHAR}
                AND T2.CP_ACCT_NO = #{cpAcctNo,jdbcType=VARCHAR}
                AND T2.PRODUCT_CHANNEL = #{productChannel,jdbcType=VARCHAR}
              UNION ALL
              SELECT SUM(ifnull(T1.ACK_VOL, 0)) AS BALANCE_VOL
              FROM CUST_BOOKS_DTL T1
              WHERE T1.TX_ACCT_NO = #{txAcctNo,jdbcType=VARCHAR}
                AND T1.DIS_CODE = #{disCode,jdbcType=VARCHAR}
                AND T1.CP_ACCT_NO = #{cpAcctNo,jdbcType=VARCHAR}
                AND T1.PRODUCT_CHANNEL = #{productChannel,jdbcType=VARCHAR}
             ) tt
    </select>

    <select id="getCustBalanceDetail" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT * FROM (
        SELECT T3.TX_ACCT_NO,
        T3.DIS_CODE,
        T3.PRODUCT_CODE,
        T3.PROTOCOL_NO,
        T3.PRODUCT_CHANNEL,
        T3.PROTOCOL_TYPE,
        T3.CP_ACCT_NO,
        SUM(T3.BALANCE_VOL) AS BALANCE_VOL,
        SUM(T3.UNCONFIRMED_VOL) AS UNCONFIRMED_VOL,
        SUM(T3.JUST_FRZN_VOL) AS JUST_FRZN_VOL
        FROM (SELECT T2.TX_ACCT_NO,
        T2.DIS_CODE,
        T2.PRODUCT_CODE,
        T2.PRODUCT_CHANNEL,
        T2.PROTOCOL_NO,
        T2.PROTOCOL_TYPE,
        T2.CP_ACCT_NO,
        T2.BALANCE_VOL,
        T2.UNCONFIRMED_VOL,
        ifnull(T2.JUST_FRZN_VOL,0) JUST_FRZN_VOL
        FROM CUST_BOOKS T2
        WHERE T2.TX_ACCT_NO = #{txAcctNo,jdbcType=VARCHAR}
        AND T2.DIS_CODE = #{disCode,jdbcType=VARCHAR}
        <if test="list != null and list.size() > 0 ">
            and t2.PRODUCT_CODE in
            <foreach collection="list" index="index" item="fundCode" open="(" separator="," close=")">
                #{fundCode}
            </foreach>
        </if>
        <if test="cpAcctNo != null and cpAcctNo != ''">
            AND T2.CP_ACCT_NO = #{cpAcctNo,jdbcType=VARCHAR}
        </if>
        UNION ALL
        SELECT T1.TX_ACCT_NO,
        T1.DIS_CODE,
        T1.PRODUCT_CODE,
        T1.PRODUCT_CHANNEL,
        T1.PROTOCOL_NO,
        T1.PROTOCOL_TYPE,
        T1.CP_ACCT_NO,
        SUM(ifnull(T1.ACK_VOL, 0)) AS BALANCE_VOL,
        SUM(ifnull(T1.APP_VOL, 0)) AS UNCONFIRMED_VOL,
        0
        FROM CUST_BOOKS_DTL T1
        WHERE T1.TX_ACCT_NO = #{txAcctNo,jdbcType=VARCHAR}
        AND T1.DIS_CODE = #{disCode,jdbcType=VARCHAR}
        <if test="list != null and list.size() > 0 ">
            and t1.PRODUCT_CODE in
            <foreach collection="list" index="index" item="fundCode" open="(" separator="," close=")">
                #{fundCode}
            </foreach>
        </if>
        <if test="cpAcctNo != null and cpAcctNo != ''">
            AND T1.CP_ACCT_NO = #{cpAcctNo,jdbcType=VARCHAR}
        </if>
        GROUP BY T1.TX_ACCT_NO,
        T1.DIS_CODE,
        T1.PRODUCT_CODE,
        T1.PRODUCT_CHANNEL,
        T1.PROTOCOL_TYPE,
        T1.PROTOCOL_NO,
        T1.CP_ACCT_NO) T3
        GROUP BY T3.TX_ACCT_NO,
        T3.DIS_CODE,
        T3.PRODUCT_CODE,
        T3.PRODUCT_CHANNEL,
        T3.PROTOCOL_TYPE,
        T3.PROTOCOL_NO,
        T3.CP_ACCT_NO) tt
        WHERE BALANCE_VOL > 0
        <if test="productChannel != null and productChannel != ''">
            AND PRODUCT_CHANNEL = #{productChannel,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="selectUnconfirmedVol" parameterType="map" resultMap="BaseResultMap">
        select TT.PRODUCT_CODE,
        ABS(TT.UNCONFIRMED_VOL) as UNCONFIRMED_VOL
        from (
        select t.product_code,
        sum(t.unconfirmed_vol) as unconfirmed_vol
        from (select t1.product_code,
        ifnull(t1.unconfirmed_vol, 0) as unconfirmed_vol
        from cust_books t1
        where t1.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
        <if test="disCodeList != null and disCodeList.size() > 0 ">
            and t1.dis_code in
            <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                #{disCode}
            </foreach>
        </if>
        <if test="fundCode != null and fundCode != ''">
            and t1.product_code = #{fundCode,jdbcType=VARCHAR}
        </if>
        and t1.protocol_type = '4'
        union all
        select t2.product_code,
        ifnull(t2.app_vol, 0) as unconfirmed_vol
        from cust_books_dtl t2
        where t2.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
        <if test="disCodeList != null and disCodeList.size() > 0 ">
            and t2.dis_code in
            <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                #{disCode}
            </foreach>
        </if>
        <if test="fundCode != null and fundCode != ''">
            and t2.product_code = #{fundCode,jdbcType=VARCHAR}
        </if>
        and t2.protocol_type = '4'
        ) t
        group by t.product_code
        )TT
        where ABS(TT.UNCONFIRMED_VOL) >0
    </select>

    <select id="selectUnconfirmedVolByCpAcctNo" parameterType="map" resultMap="BaseResultMap">
        select TT.PRODUCT_CODE,
        TT.CP_ACCT_NO,
        ABS(TT.UNCONFIRMED_VOL) as UNCONFIRMED_VOL
        from (
        select t.product_code,
        t.cp_acct_no,
        sum(t.unconfirmed_vol) as unconfirmed_vol
        from (select t1.product_code,
        t1.cp_acct_no,
        ifnull(t1.unconfirmed_vol, 0) as unconfirmed_vol
        from cust_books t1
        where t1.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
        <if test="disCodeList != null and disCodeList.size() > 0 ">
            and t1.dis_code in
            <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                #{disCode}
            </foreach>
        </if>
        <if test="cpAcctNo != null and cpAcctNo != ''">
            and t1.cp_acct_no = #{cpAcctNo,jdbcType=VARCHAR}
        </if>
        <if test="fundCode != null and fundCode != ''">
            and t1.product_code = #{fundCode,jdbcType=VARCHAR}
        </if>
        and t1.protocol_type = '4'
        and t1.product_channel in ('3','7')
        union all
        select t2.product_code,
        t2.cp_acct_no,
        ifnull(t2.app_vol, 0) as unconfirmed_vol
        from cust_books_dtl t2
        where t2.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
        <if test="disCodeList != null and disCodeList.size() > 0 ">
            and t2.dis_code in
            <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                #{disCode}
            </foreach>
        </if>
        <if test="fundCode != null and fundCode != ''">
            and t2.product_code = #{fundCode,jdbcType=VARCHAR}
        </if>
        <if test="cpAcctNo != null and cpAcctNo != ''">
            and t2.cp_acct_no = #{cpAcctNo,jdbcType=VARCHAR}
        </if>
        and t2.protocol_type = '4'
        and t2.product_channel in ('3','7')
        ) t
        group by t.product_code, t.cp_acct_no
        )TT
        where ABS(TT.UNCONFIRMED_VOL) >0
    </select>

    <select id="selectCustBooksByTxAcctNoOrFundCodes" parameterType="map" resultMap="BaseResultMap">
        select * from (
        select t.PRODUCT_CODE as product_code,
        t.tx_acct_no as tx_acct_no,
        t.dis_code as dis_code,
        t.fund_share_class as fund_share_class,
        sum(t.balance_vol) as balance_vol,
        abs(sum(t.unconfirmed_vol)) as unconfirmed_vol,
        abs(sum(t.just_frzn_vol)) as just_frzn_vol
        from (select t2.product_code as product_code,
        t2.tx_acct_no as tx_acct_no,
        t2.dis_code as dis_code,
        t2.fund_share_class as fund_share_class,
        ifnull(t2.ack_vol, 0) as balance_vol,
        ifnull(t2.app_vol, 0) as unconfirmed_vol,
        0 as just_frzn_vol
        from cust_books_dtl t2
        where t2.PROTOCOL_TYPE = '4'
        <if test="txAcctNo != null and txAcctNo != ''">
            and t2.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
        </if>
        union all
        select t3.product_code as product_code,
        t3.tx_acct_no as tx_acct_no,
        t3.dis_code as dis_code,
        t3.fund_share_class as fund_share_class,
        ifnull(t3.balance_vol, 0) as balance_vol,
        ifnull(t3.unconfirmed_vol, 0) as unconfirmed_vol,
        ifnull(t3.just_frzn_vol, 0) as just_frzn_vol
        from cust_books t3
        where t3.PROTOCOL_TYPE = '4'
        <if test="txAcctNo != null and txAcctNo != ''">
            and t3.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
        </if>
        ) t
        group by t.product_code, t.fund_share_class,t.tx_acct_no,t.dis_code
        ) tt
        where balance_vol &gt; 0
        <if test="txAcctNo != null and txAcctNo != ''">
            and tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
        </if>

        <if test="fundCodes != null and fundCodes.size() > 0">
            and product_code in
            <foreach collection="fundCodes" item="item" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>

    <select id="selectBalanceDtlWithOpenDt" parameterType="map" resultMap="BalanceResultMap">
        select protocol_no,
        product_code,
        fund_share_class,
        cp_acct_no,
        sum(balance_vol) as balance_vol,
        open_rede_dt
        from (select *
        from (select t2.product_code as product_code,
        t2.fund_share_class as fund_share_class,
        t2.protocol_no as protocol_no,
        t2.cp_acct_no as cp_acct_no,
        ifnull(t2.ack_vol, 0) as balance_vol,
        null as open_rede_dt
        from cust_books_dtl t2
        where t2.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
        <if test="disCodeList != null and disCodeList.size() > 0 ">
            and t2.dis_code in
            <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                #{disCode}
            </foreach>
        </if>
        and t2.product_code = #{productCode,jdbcType=VARCHAR}
        and t2.protocol_type = '4'
        union all
        select t3.product_code as product_code,
        t3.fund_share_class as fund_share_class,
        t3.protocol_no as protocol_no,
        t3.cp_acct_no as cp_acct_no,
        ifnull(t3.balance_vol, 0) as balance_vol,
        null as open_rede_dt
        from cust_books t3
        where t3.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
        <if test="disCodeList != null and disCodeList.size() > 0 ">
            and t3.dis_code in
            <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                #{disCode}
            </foreach>
        </if>
        and t3.product_code = #{productCode,jdbcType=VARCHAR}
        and t3.protocol_type = '4') tt
        where balance_vol > 0
        and not exists (select 1
        from SUB_CUST_BOOKS t
        where t.FUND_CODE = tt.product_code
        and t.CP_ACCT_NO = tt.CP_ACCT_NO)
        union all
        select t1.fund_code as product_code,
        fund_share_class,
        protocol_no,
        cp_acct_no,
        balance_vol,
        open_rede_dt
        from sub_cust_books t1
        where t1.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
        <if test="disCodeList != null and disCodeList.size() > 0 ">
            and t1.dis_code in
            <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                #{disCode}
            </foreach>
        </if>
        and t1.protocol_type = '4'
        and t1.fund_code = #{productCode,jdbcType=VARCHAR}
        and t1.balance_vol > 0) t
        group by t.protocol_no,
        t.product_code,
        t.fund_share_class,
        t.cp_acct_no,
        t.open_rede_dt
    </select>
    <select id="selectBalanceDtlByDisCodeList" resultMap="BalanceResultMap" parameterType="map">
        select * from (
        select t.protocol_no as protocol_no,
        t.product_code as product_code,
        t.fund_share_class as fund_share_class,
        t.cp_acct_no as cp_acct_no,
        sum(t.balance_vol) as balance_vol,
        abs(sum(t.unconfirmed_vol)) as unconfirmed_vol,
        sum(t.locking_period_vol) as locking_period_vol,
        sum(t.just_frzn_vol) as just_frzn_vol,
        min(t.open_rede_dt) as open_rede_dt
        from (select t1.fund_code as product_code,
        t1.fund_share_class as fund_share_class,
        t1.protocol_no as protocol_no,
        t1.cp_acct_no as cp_acct_no,
        0 as balance_vol,
        0 as unconfirmed_vol,
        ifnull(t1.balance_vol, 0) as locking_period_vol,
        0 as just_frzn_vol,
        t1.open_rede_dt
        from sub_cust_books t1
        <where>
            t1.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
            <if test="disCodeList != null and disCodeList.size() > 0 ">
                and t1.dis_code in
                <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                    #{disCode}
                </foreach>
            </if>
            and t1.protocol_type = '4'
            <if test=" productCode != null and productCode != ''">
                and t1.fund_code = #{productCode,jdbcType=VARCHAR}
            </if>
            <if test=" protocolNo != null and protocolNo != ''">
                and t1.protocol_no = #{protocolNo,jdbcType=VARCHAR}
            </if>
            <if test=" cpAcctNo != null and cpAcctNo != ''">
                and t1.cp_acct_no = #{cpAcctNo,jdbcType=VARCHAR}
            </if>
            and t1.open_rede_dt is not null
            and t1.open_rede_dt &gt; #{taTradeDt,jdbcType=VARCHAR}
        </where>
        union all
        select t2.product_code as product_code,
        t2.fund_share_class as fund_share_class,
        t2.protocol_no as protocol_no,
        t2.cp_acct_no as cp_acct_no,
        ifnull(t2.ack_vol, 0) as balance_vol,
        ifnull(t2.app_vol, 0) as unconfirmed_vol,
        0 as locking_period_vol,
        0 as just_frzn_vol,
        null as open_rede_dt
        from cust_books_dtl t2
        <where>
            t2.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
            <if test="disCodeList != null and disCodeList.size() > 0 ">
                and t2.dis_code in
                <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                    #{disCode}
                </foreach>
            </if>
            <if test=" productCode != null and productCode != ''">
                and t2.product_code = #{productCode,jdbcType=VARCHAR}
            </if>
            <if test=" protocolNo != null and protocolNo != ''">
                and t2.protocol_no = #{protocolNo,jdbcType=VARCHAR}
            </if>
            <if test=" cpAcctNo != null and cpAcctNo != ''">
                and t2.cp_acct_no = #{cpAcctNo,jdbcType=VARCHAR}
            </if>
            and t2.protocol_type = '4'
        </where>
        union all
        select t3.product_code as product_code,
        t3.fund_share_class as fund_share_class,
        t3.protocol_no as protocol_no,
        t3.cp_acct_no as cp_acct_no,
        ifnull(t3.balance_vol, 0) as balance_vol,
        ifnull(t3.unconfirmed_vol, 0) as unconfirmed_vol,
        0 as locking_period_vol,
        ifnull(t3.just_frzn_vol, 0) as just_frzn_vol,
        null as open_rede_dt
        from cust_books t3
        where t3.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
        <if test="disCodeList != null and disCodeList.size() > 0 ">
            and t3.dis_code in
            <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                #{disCode}
            </foreach>
        </if>
        <if test=" productCode != null and productCode != ''">
            and t3.product_code = #{productCode,jdbcType=VARCHAR}
        </if>
        <if test=" protocolNo != null and protocolNo != ''">
            and t3.protocol_no = #{protocolNo,jdbcType=VARCHAR}
        </if>
        <if test=" cpAcctNo != null and cpAcctNo != ''">
            and t3.cp_acct_no = #{cpAcctNo,jdbcType=VARCHAR}
        </if>
        and t3.protocol_type = '4') t
        group by t.protocol_no, t.product_code, t.fund_share_class, t.cp_acct_no
        ) tt
        where balance_vol &gt; 0
    </select>

    <!-- 查询代销在途金额 -->
    <select id="selectConsignmentOnWayAmt" parameterType="map" resultMap="BaseResultMap">
        select
        product_code,DIS_CODE,
        sum(ifnull(t1.unconfirmed_amt, 0)) as unconfirmed_amt
        from cust_books t1
        where t1.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
        <if test="disCodeList != null and disCodeList.size() > 0 ">
            and t1.dis_code in
            <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                #{disCode}
            </foreach>
        </if>
        and t1.protocol_type = '4'
        group by product_code,DIS_CODE
        union all
        select
        product_code,DIS_CODE,
        sum(ifnull(t2.app_amt, 0)) as unconfirmed_amt
        from cust_books_dtl t2
        where t2.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
        <if test="disCodeList != null and disCodeList.size() > 0 ">
            and t2.dis_code in
            <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                #{disCode}
            </foreach>
        </if>
        and t2.protocol_type = '4'
        group by product_code,DIS_CODE
    </select>


    <select id="getUnAckNum" resultType="int" parameterType="map">
        select count(*)
        from high_deal_order_dtl a
                 inner join deal_order b on a.DEAL_NO = b.DEAL_NO
        where a.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
          and a.fund_code = #{productCode,jdbcType=VARCHAR}
          and b.ORDER_STATUS = '1'
          and a.tx_app_flag = '0'
    </select>


    <!-- 千禧年产品查询累计购买净金额直销，仅查询120、130、122 -->
    <select id="selectNetBuyAmountQX" parameterType="map" resultMap="BalanceResultMap">
        select t.product_code, sum(t.net_buy_amount) as net_buy_amount
        from (select t.fundcode as product_code,
        ifnull(t.ackamt,0) - ifnull(t.fee,0) as net_buy_amount
        from cm_custtrade_direct t
        where t.hboneno = #{hbOneNo,jdbcType=VARCHAR}
        and t.busicode in ('120','122','130','12B')
        and t.recstat = '0'
        <if test="list != null and list.size() > 0 ">
            and t.fundcode in
            <foreach collection="list" index="index" item="fundCode" open="(" separator="," close=")">
                #{fundCode}
            </foreach>
        </if>
        ) t
        group by t.product_code
    </select>

    <select id="selectStockBooksByTxAcctNoAndFundCodeBatch" resultType="java.lang.String" parameterType="map">
        select T2.product_code
        from cust_books T2
        where T2.TX_ACCT_NO = #{txAcctNo,jdbcType=VARCHAR}
        and T2.PRODUCT_CODE in
        <foreach collection="productCodes" index="index" item="fundCode" open="(" separator="," close=")">
            #{fundCode}
        </foreach>
        AND T2.PROTOCOL_TYPE = '4'
        AND T2.Balance_Vol <![CDATA[>]]> 0
        group by T2.product_code
    </select>

    <select id="selectStockBooksByTxAcctNoAndDisCode" resultType="java.lang.String" parameterType="map">
        select T2.product_code
        from cust_books T2
        where T2.TX_ACCT_NO = #{txAcctNo,jdbcType=VARCHAR}
          and T2.dis_code = #{disCode,jdbcType=VARCHAR}
          AND T2.PROTOCOL_TYPE = '4'
          AND T2.Balance_Vol <![CDATA[>]]> 0
        group by T2.product_code
    </select>
    <select id="queryBalanceTxAcctNoByFundCode" resultType="java.lang.String" parameterType="map">
        select distinct (T2.tx_acct_no)
        from cust_books T2
        where BALANCE_VOL > 0
          and T2.PRODUCT_CODE = #{fundCode,jdbcType=VARCHAR}
    </select>
    <select id="queryBalanceTxAcctNoByPage" resultType="java.lang.String" parameterType="map">
        select distinct (T1.tx_acct_no)
        from cust_books T1
        where BALANCE_VOL > 0 limit #{offset}
            , #{pageSize}
    </select>
    <select id="queryUnConfirmedOrderFundCode" resultType="java.lang.String" parameterType="map">
        SELECT t1.product_code
        FROM cust_books t1
        WHERE t1.balance_vol=0
        and t1.tx_acct_no= #{txAcctNo,jdbcType=VARCHAR}
        and t1.product_code in
        <foreach collection="unBalanceFundCodeSet" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        AND NOT EXISTS (
        SELECT 1
        FROM high_deal_order_dtl t2
        WHERE t1.tx_acct_no = t2.tx_acct_no
        AND t1.product_code = t2.fund_code
        AND t2.ack_dt IS NOT NULL
        )
    </select>
    <select id="isIntransitForCloseAct" resultType="int" parameterType="map">
        select
        count(1)
        from
        (
        select T3.tx_acct_no,
        T3.dis_code,
        T3.cp_acct_no,
        T3.product_code,
        T3.fund_share_class,
        T3.protocol_no,
        sum(T3.Balance_Vol) as Balance_Vol,
        sum(T3.Unconfirmed_Vol) as Unconfirmed_Vol,
        sum(T3.Unconfirmed_Amt) as Unconfirmed_Amt
        from (select T2.tx_acct_no,
        T2.dis_code,
        T2.cp_acct_no,
        T2.product_code,
        T2.fund_share_class,
        T2.protocol_no,
        T2.Balance_Vol,
        T2.Unconfirmed_Vol,
        T2.Unconfirmed_Amt
        from cust_books T2
        where t2.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
        and t2.PRODUCT_CLASS = '3'
        <if test="disCode != null and disCode != ''">
            and t2.dis_code = #{disCode,jdbcType=VARCHAR}
        </if>
        <if test="protocolNo != null and protocolNo != ''">
            and t2.protocol_no = #{protocolNo,jdbcType=VARCHAR}
        </if>
        <if test="cpAcctNos!=null and cpAcctNos.size()>0">
            and t2.cp_acct_no in
            <foreach collection="cpAcctNos" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        union all
        select T1.tx_acct_no,
        T1.dis_code,
        T1.cp_acct_no,
        T1.product_code,
        T1.fund_share_class,
        T1.protocol_no,
        sum(ifnull(T1.Ack_Vol, 0)) as balance_vol,
        sum(ifnull(T1.App_Vol,0)) as Unconfirmed_Vol,
        sum(ifnull(T1.App_Amt,0)) as Unconfirmed_Amt
        from cust_books_dtl T1
        where t1.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
        and t1.PRODUCT_CLASS = '3'
        <if test="disCode != null and disCode != ''">
            and t1.dis_code = #{disCode,jdbcType=VARCHAR}
        </if>
        <if test="protocolNo != null and protocolNo != ''">
            and t1.protocol_no = #{protocolNo,jdbcType=VARCHAR}
        </if>
        <if test="cpAcctNos!=null and cpAcctNos.size()>0">
            and t1.cp_acct_no in
            <foreach collection="cpAcctNos" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        group by T1.tx_acct_no,
        T1.dis_code,
        T1.cp_acct_no,
        T1.product_code,
        T1.fund_share_class,
        T1.protocol_no) T3
        group by T3.tx_acct_no,
        T3.dis_code,
        T3.cp_acct_no,
        T3.product_code,
        T3.fund_share_class,
        T3.protocol_no
        ) T4
        where T4.Unconfirmed_Vol <![CDATA[<]]> 0 Or Unconfirmed_Amt <![CDATA[>]]> 0
    </select>

    <select id="getHighCustBalanceDetail" resultMap="BalanceResultMap">
        SELECT * FROM (
        SELECT T3.TX_ACCT_NO,
        T3.DIS_CODE,
        T3.PRODUCT_CODE AS PRODUCT_CODE,
        T3.PROTOCOL_NO,
        T3.PRODUCT_CHANNEL,
        T3.PROTOCOL_TYPE,
        T3.CP_ACCT_NO,
        SUM(T3.BALANCE_VOL) AS BALANCE_VOL,
        SUM(T3.UNCONFIRMED_VOL) AS UNCONFIRMED_VOL,
        SUM(T3.JUST_FRZN_VOL) AS JUST_FRZN_VOL
        FROM (SELECT T2.TX_ACCT_NO,
        T2.DIS_CODE,
        T2.PRODUCT_CODE,
        T2.PRODUCT_CHANNEL,
        T2.PROTOCOL_NO,
        T2.PROTOCOL_TYPE,
        T2.CP_ACCT_NO,
        T2.BALANCE_VOL,
        T2.UNCONFIRMED_VOL,
        ifnull(T2.JUST_FRZN_VOL,0) JUST_FRZN_VOL
        FROM CUST_BOOKS T2
        WHERE T2.TX_ACCT_NO = #{txAcctNo,jdbcType=VARCHAR}
        and T2.PRODUCT_CLASS = '3'
        <if test="disCode != null and disCode != '' and disCode != 'ALL'">
            AND T2.DIS_CODE = #{disCode,jdbcType=VARCHAR}
        </if>
        <if test="fundCode != null and fundCode != ''">
            AND T2.PRODUCT_CODE = #{fundCode,jdbcType=VARCHAR}
        </if>
        <if test="cpAcctNos!=null and cpAcctNos.size()>0">
            AND T2.CP_ACCT_NO IN
            <foreach collection="cpAcctNos" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        UNION ALL
        SELECT T1.TX_ACCT_NO,
        T1.DIS_CODE,
        T1.PRODUCT_CODE,
        T1.PRODUCT_CHANNEL,
        T1.PROTOCOL_NO,
        T1.PROTOCOL_TYPE,
        T1.CP_ACCT_NO,
        SUM(ifnull(T1.ACK_VOL, 0)) AS BALANCE_VOL,
        SUM(ifnull(T1.APP_VOL, 0)) AS UNCONFIRMED_VOL,
        0
        FROM CUST_BOOKS_DTL T1
        WHERE T1.TX_ACCT_NO = #{txAcctNo,jdbcType=VARCHAR}
        and T1.PRODUCT_CLASS = '3'
        <if test="disCode != null and disCode != ''">
            AND T1.DIS_CODE = #{disCode,jdbcType=VARCHAR}
        </if>
        <if test="fundCode != null and fundCode != ''">
            AND T1.PRODUCT_CODE = #{fundCode,jdbcType=VARCHAR}
        </if>
        <if test="cpAcctNos!=null and cpAcctNos.size()>0">
            AND T1.CP_ACCT_NO IN
            <foreach collection="cpAcctNos" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY T1.TX_ACCT_NO,
        T1.DIS_CODE,
        T1.PRODUCT_CODE,
        T1.PRODUCT_CHANNEL,
        T1.PROTOCOL_TYPE,
        T1.PROTOCOL_NO,
        T1.CP_ACCT_NO) T3
        GROUP BY T3.TX_ACCT_NO,
        T3.DIS_CODE,
        T3.PRODUCT_CODE,
        T3.PRODUCT_CHANNEL,
        T3.PROTOCOL_TYPE,
        T3.PROTOCOL_NO,
        T3.CP_ACCT_NO) tt
        WHERE BALANCE_VOL > 0
    </select>

    <resultMap id="confirmBalanceVoResultMap" type="com.howbuy.tms.high.orders.dao.vo.ConfirmBalanceVo">
        <result column="fund_code" property="fundCode"/>
        <result column="tx_acct_no" property="txAcctNo"/>
        <result column="balance_vol" property="balanceVol"/>
        <result column="dis_code" property="disCode"/>
    </resultMap>

    <select id="queryAgentConfirmBalance" resultMap="confirmBalanceVoResultMap">
        select tx_acct_no,
        product_code as fund_code,
        dis_code,
        '156' as currency,
        balance_vol
        from CUST_BOOKS
        where
        BALANCE_VOL>0
        <if test="fundCode != null and fundCode != ''">
            and product_code = #{fundCode,jdbcType = VARCHAR}
        </if>
        <if test="disCodeList != null and disCodeList.size() > 0 ">
            and d.DIS_CODE in
            <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                #{disCode}
            </foreach>
        </if>
        and TX_ACCT_NO = #{txAcctNo,jdbcType=VARCHAR}
    </select>
</mapper>