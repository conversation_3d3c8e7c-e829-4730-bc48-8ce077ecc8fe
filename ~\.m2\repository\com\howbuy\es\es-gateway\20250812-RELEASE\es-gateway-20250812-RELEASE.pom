<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.howbuy.es</groupId>
	<artifactId>es-gateway</artifactId>
	<version>20250812-RELEASE</version>
	<packaging>pom</packaging>
	<name>es-gateway</name>

	<properties>
		<java.version>1.8</java.version>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<dubbo.version>2.5.3</dubbo.version>
		<junit.version>4.12</junit.version>
		<pa.framework.version>1.0.3-SNAPSHOT</pa.framework.version>
		<h2.version>1.3.176</h2.version>
		<commons.pool.version>1.6</commons.pool.version>

		
		<com.howbuy.notifyUtil.version>1.0.0</com.howbuy.notifyUtil.version>
		<activemq.version>5.10.0</activemq.version>
		<spring.version>4.3.0.RELEASE</spring.version>
		
		<lombok.version>1.14.4</lombok.version>
		<pagehelper.version>3.6.3</pagehelper.version>
		<howbuy.cache.version>2.0.3-RELEASE</howbuy.cache.version>
		<commons.lang.version>2.4</commons.lang.version>
		<commons.beanutils.version>1.8.3</commons.beanutils.version>
		<nacos-spring-context.version>1.1.1</nacos-spring-context.version>

		<pa.framework.version>1.0.3-SNAPSHOT</pa.framework.version>
		<com.howbuy.es-gateway.version>20250812-RELEASE</com.howbuy.es-gateway.version>
		<com.howbuy.es-common.version>3.5.0-RELEASE</com.howbuy.es-common.version>
		<com.howbuy.es-web.version>20250812-RELEASE</com.howbuy.es-web.version>
	</properties>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>com.howbuy.pa.framework</groupId>
				<artifactId>howbuy-framework-rpc</artifactId>
				<version>${pa.framework.version}</version>
			</dependency>

			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>dubbo</artifactId>
				<version>${dubbo.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>spring</artifactId>
						<groupId>org.springframework</groupId>
					</exclusion>
				</exclusions>
			</dependency>

			<dependency>
				<groupId>junit</groupId>
				<artifactId>junit</artifactId>
				<version>${junit.version}</version>
				<scope>test</scope>
			</dependency>

			<dependency>
				<groupId>commons-pool</groupId>
				<artifactId>commons-pool</artifactId>
				<version>${commons.pool.version}</version>
			</dependency>

			
			<dependency>
				<groupId>com.howbuy.es</groupId>
				<artifactId>es-gateway-facade</artifactId>
				<version>${com.howbuy.es-gateway.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.es</groupId>
				<artifactId>es-gateway-service</artifactId>
				<version>${com.howbuy.es-gateway.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.es</groupId>
				<artifactId>es-gateway-dao</artifactId>
				<version>${com.howbuy.es-gateway.version}</version>
			</dependency>
			
			<dependency>
				<groupId>com.howbuy.es</groupId>
				<artifactId>es-common-facade</artifactId>
				<version>${com.howbuy.es-common.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.es</groupId>
				<artifactId>es-common-utils</artifactId>
				<version>${com.howbuy.es-common.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.es</groupId>
				<artifactId>es-common-file</artifactId>
				<version>${com.howbuy.es-common.version}</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy.es</groupId>
				<artifactId>es-web-facade</artifactId>
				<version>${com.howbuy.es-web.version}</version>
			</dependency>
			<dependency>
				<groupId>org.projectlombok</groupId>
				<artifactId>lombok</artifactId>
				<version>${lombok.version}</version>
				<scope>provided</scope>
			</dependency>
			
			<dependency>
				<groupId>com.alibaba.nacos</groupId>
				<artifactId>nacos-spring-context</artifactId>
				<version>${nacos-spring-context.version}</version>
				<exclusions>
					<exclusion>
						<groupId>com.google.guava</groupId>
						<artifactId>guava</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.springframework</groupId>
						<artifactId>spring-context</artifactId>
					</exclusion>
					<exclusion>
						<groupId>commons-io</groupId>
						<artifactId>commons-io</artifactId>
					</exclusion>
				</exclusions>
			</dependency>

		</dependencies>
	</dependencyManagement>

	<distributionManagement>
		<repository>
			<id>howbuy-release</id>
			<name>howbuy-release</name>
			<url>http://nx-java.howbuy.pa/repository/howbuy-release/</url>
		</repository>
		<snapshotRepository>
			<id>howbuy-snapshot</id>
			<name>howbuy-snapshot</name>
			<url>http://nx-java.howbuy.pa/repository/howbuy-snapshot/</url>
		</snapshotRepository>
	</distributionManagement>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>2.3.2</version>
				<configuration>
					<source>${java.version}</source>
					<target>${java.version}</target>
					<encoding>UTF-8</encoding>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<version>2.5</version>
				<configuration>
					<skipTests>true</skipTests>
				</configuration>
			</plugin>
		</plugins>
	</build>
	</project>