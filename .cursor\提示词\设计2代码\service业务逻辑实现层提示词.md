# ✅ 高端订单中心Service层业务逻辑实现代码生成提示词

你是一名资深后端开发工程师，请根据我提供的**业务需求文档**，生成符合高端订单中心项目标准的**Service层业务逻辑代码实现**，包括接口和实现类，输出要求如下：

## 📌 Service层定位与职责：
- **包路径**: `com.howbuy.tms.high.orders.service.service`
- **核心职责**: 作为业务逻辑实现的核心，负责编排和组合`Business`层、`Repository`层、`OuterService`层提供的原子业务能力，形成完整的业务流程。它是业务逻辑的主要入口。
- **调用关系**:
    - 👆 **被调用层**: `Facade`层、`Job`层、其他`Service`层
    - 👇 **可调用层**: `Repository`层、`OuterService`层、`Business`层

## 📋 输出要求：

### 🎯 代码结构规范
```
com.howbuy.tms.high.orders.service.service
└── [业务域]/                        # 按业务域划分目录
    ├── [具体业务]Service.java        # 业务接口定义
    ├── [具体业务]ServiceImpl.java    # 业务逻辑实现类
    └── bean/                      # 业务相关的Bean类目录
        └── [相关Bean].java
```

### 🔧 技术实现要求
- **接口**：定义清晰的业务方法，提供完整的JavaDoc注释。
- **实现类**：
    - 使用`@Service`注册到Spring容器。
    - 使用`@Slf4j`注解（或`LogManager.getLogger()`）获取日志对象。
    - 通过`@Autowired`注入`Repository`、`OuterService`和`Business`层的依赖。
    - 实现接口定义的所有方法，并包含具体的业务逻辑。
- **事务控制**: Service层不直接控制事务。事务管理由`Repository`层负责，通过`@Transactional`注解在`Repository`方法上实现。
- **异常处理**: 统一处理异常，对可预期的业务异常抛出`BusinessException`，并记录详细日志。

### 📝 注释规范
严格遵循以下格式，特别是`author`和`date`需要动态生成。

**类注释示例:**
```java
/**
 * @Description: [精准的业务功能描述]
 * @Author: [作者名]
 * @Date: [YYYY/MM/DD HH:mm:ss]
 */
@Service
@Slf4j
public class [业务名]ServiceImpl implements [业务名]Service {
    // ...
}
```

**方法注释示例:**
```java
/**
   * @description: [清晰的方法功能描述]
   * @param request 请求对象
   * @param highDealOrderDtlPo 订单明细对象
   * @return void
   * @author: [作者名]
   * @date: [YYYY/MM/DD HH:mm:ss]
   * @since JDK 1.8
   */
  public void businessMethod(ModifyRefundRequest request, HighDealOrderDtlPo highDealOrderDtlPo) {
      // 业务处理逻辑
  }
```

## 🏗️ 代码生成模板：

### 1. Service接口模板
```java
package com.howbuy.tms.high.orders.service.service.[业务域];

// import ...

/**
 * @Description: [接口功能描述]
 * @Author: [作者名]
 * @Date: [YYYY/MM/DD HH:mm:ss]
 */
public interface [业务名]Service {

    /**
     * @description: [方法功能描述]
     * @param [参数名] [参数说明]
     * @return [返回值说明]
     * @author: [作者名]
     * @date: [YYYY/MM/DD HH:mm:ss]
     * @since JDK 1.8
     */
    [返回类型] [方法名]([参数列表]);
}
```

### 2. Service实现类模板
```java
package com.howbuy.tms.high.orders.service.service.[业务域];

// import ...
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Description: [接口功能描述]
 * @Author: [作者名]
 * @Date: [YYYY/MM/DD HH:mm:ss]
 */
@Service
@Slf4j
public class [业务名]ServiceImpl implements [业务名]Service {

    @Autowired
    private [相关]Repository [相关变量名]Repository;

    @Autowired
    private [外部服务]OuterService [外部服务变量名]OuterService;
    
    @Autowired
    private [公共业务]Service [公共业务变量名]Service;

    @Override
    public [返回类型] [方法名]([参数列表]) {
        log.info("[方法名]-开始，参数：{}", [参数名]);
        // 1. 参数校验
        
        // 2. 调用Repository/OuterService/Business层进行业务编排
        
        // 3. 数据转换与处理（严禁使用BeanUtils.copyProperties）
        
        // 4. 返回结果
        log.info("[方法名]-结束，结果：{}", [返回结果]);
        return [返回结果];
    }
}
```

## 🚫 禁止事项：
| 类型 | 说明 |
|------|------|
| ❌ **严禁使用Bean拷贝** | **严禁使用 `BeanUtils.copyProperties()` 或任何其他形式的Bean拷贝工具。所有对象属性赋值必须显式调用`set`方法完成。** |
| ❌ 直接操作数据库 | 必须通过`Repository`层操作数据库。 |
| ❌ 直接调用外部接口 | 必须通过`OuterService`层调用外部接口。 |
| ❌ 包含HTTP接口逻辑 | `Service`层不处理HTTP请求和响应。 |
| ❌ 直接控制事务 | `Service`层禁止使用`@Transactional`注解，事务由`Repository`层控制。 |
| ❌ 硬编码业务规则 | 业务规则应尽可能配置化或通过`Business`层实现。 |
| ❌ 禁止使用魔法值 | 任何未经定义的字面量都应定义为常量或枚举。 |
| ❌ 缺少枚举或常量 | 对于状态、类型等字段，必须创建对应的枚举类或常量。 |
| ❌ 禁止使用@Data | POJO或DTO类必须使用`@Getter`和`@Setter`，禁止使用`@Data`。 |

## 📎 项目依赖的已有组件：
- **序列号生成**: `KeyGenerator`, `TableBusiCode`
- **消息发送**: `MessageService`, `SimpleMessage`
- **工具类**: `MathUtils`, `JSON`, `CollectionUtils`, `DateUtils`
- **枚举类**: `BusinessCodeEnum`, `OrderStatusEnum`等
- **异常类**: `BusinessException`, `ValidateException`, `SystemException`

## 📎 使用方式：
在我提供业务需求描述后，立即生成符合上述规范的完整`Service`层代码实现（包括接口和实现类），按照业务逻辑流程结构清晰输出，确保代码可以直接在项目中使用。

**注意**：生成的代码必须严格遵循高端订单中心项目规范，确保与现有架构完美融合！