package com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.howbuy.cachemanagement.service.CacheService;
import com.howbuy.cachemanagement.service.CacheServiceImpl;
import com.howbuy.common.utils.DateUtil;
import com.howbuy.interlayer.product.enums.ProductChannelNewEnum;
import com.howbuy.interlayer.product.enums.RedeemTypeEnum;
import com.howbuy.interlayer.product.enums.SupportAdvanceFlagEnum;
import com.howbuy.interlayer.product.model.JjxswConfigModel;
import com.howbuy.interlayer.product.model.TaInfoModel;
import com.howbuy.interlayer.product.service.TaInfoService;
import com.howbuy.interlayer.product.service.TradeDayService;
import com.howbuy.paramcenter.serverfacade.dto.FundManDto;
import com.howbuy.tms.common.constant.CacheKeyPrefix;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.*;
import com.howbuy.tms.common.enums.database.HighDbNavDivTypeEnum;
import com.howbuy.tms.common.enums.database.HighNavDivTypeEnum;
import com.howbuy.tms.common.enums.database.ProductTypeEnum;
import com.howbuy.tms.common.exception.BusinessException;
import com.howbuy.tms.common.outerservice.acccenter.querycustbankcard.QueryCustBankCardOuterService;
import com.howbuy.tms.common.outerservice.acccenter.querycustbankcard.QueryCustBankCardResult;
import com.howbuy.tms.common.outerservice.cc.center.queryhboneno.QueryHbOneNoOuterService;
import com.howbuy.tms.common.outerservice.common.Constants;
import com.howbuy.tms.common.outerservice.crm.nt.balancefactor.QueryBalnaceFactotResult;
import com.howbuy.tms.common.outerservice.dtms.QueryBalanceFactorDtmsService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.*;
import com.howbuy.tms.common.outerservice.interlayer.queryproductinfo.bean.ProductAppointmentInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.querytradeday.QueryTradeDayOuterService;
import com.howbuy.tms.common.outerservice.paramcenter.queryfundman.QueryFundManService;
import com.howbuy.tms.common.threadpool.CommonThreadPool;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.common.utils.MathUtils;
import com.howbuy.tms.common.utils.MoneyUtil;
import com.howbuy.tms.high.orders.dao.po.CmCustFundDirectPo;
import com.howbuy.tms.high.orders.dao.po.CmCusttradeDirectPo;
import com.howbuy.tms.high.orders.dao.po.HighDealOrderDtlPo;
import com.howbuy.tms.high.orders.dao.vo.*;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceResponse;
import com.howbuy.tms.high.orders.facade.search.queryasset.HighFundAssetIncomeDomain;
import com.howbuy.tms.high.orders.service.business.calvaluedate.ProductValueDateCalService;
import com.howbuy.tms.high.orders.service.business.supplesubs.SuppleSubsService;
import com.howbuy.tms.high.orders.service.business.task.QueryCustBankCardTask;
import com.howbuy.tms.high.orders.service.cacheservice.querytatradedt.QueryTaTradeDtCacheService;
import com.howbuy.tms.high.orders.service.facade.search.queryacctbalance.bean.OwnershipOrderDto;
import com.howbuy.tms.high.orders.service.facade.search.querycommon.BigUtil;
import com.howbuy.tms.high.orders.service.repository.*;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.*;
import com.howbuy.tms.high.orders.service.service.ownershiprightorder.ConsignmentOrderInfo;
import com.howbuy.tms.high.orders.service.service.ownershiprightorder.DirectOrderInfo;
import com.howbuy.tms.high.orders.service.service.ownershiprightorder.OwnershipOrderInfo;
import com.howbuy.tms.high.orders.service.service.ownershiprightorder.ProductOrderInfo;
import com.howbuy.tms.high.orders.service.service.queryasset.QueryAssetService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * @Description:用户持仓基础信息接口
 * @Author: yun.lu
 * Date: 2023/8/16 14:06
 */
@Service
@Slf4j
public class AcctBalanceBaseInfoServiceImpl implements AcctBalanceBaseInfoService {
    @Autowired
    private HighDealOrderDtlRepository highDealOrderDtlRepository;
    @Autowired
    private CmCusttradeDirectRepository cmCusttradeDirectRepository;
    @Autowired
    private QueryHbOneNoOuterService queryHbOneNoOuterService;
    @Autowired
    private CmCustFundDirectRepository cmCustFundDirectRepository;
    @Autowired
    private CustBooksRepository custBooksRepository;
    @Autowired
    private SubCustBooksRepository subCustBooksRepository;
    @Autowired
    private QueryCustBankCardOuterService queryCustBankCardOuterService;
    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;
    @Autowired
    private QueryTaTradeDtCacheService queryTaTradeDtCacheService;
    @Autowired
    private CustBooksDtlRepository custBooksDtlRepository;
    @Autowired
    public SuppleSubsService suppleSubsService;
    @Autowired
    private TradeDayService tradeDayService;
    @Autowired
    private DealOrderRepository dealOrderRepository;
    @Autowired
    private QueryBalanceFactorDtmsService queryBalanceFactorDtmsService;
    @Autowired
    private QueryTradeDayOuterService queryTradeDayOuterService;
    @Autowired
    private QueryAssetService queryAssetService;
    @Autowired
    private QueryFundManService queryFundManService;
    @Autowired
    private ProductValueDateCalService productValueDateCalService;
    @Autowired
    private TaInfoService taInfoService;
    private static Set<String> ONTHER_FIXEDINCODE_PRODUCT_SET = new HashSet<>();

    static {
        ONTHER_FIXEDINCODE_PRODUCT_SET.add(StandardFixedIncomeFlagEnum.CASH_MANAGER.getCode());
        ONTHER_FIXEDINCODE_PRODUCT_SET.add(StandardFixedIncomeFlagEnum.BOND_GS.getCode());
        ONTHER_FIXEDINCODE_PRODUCT_SET.add(StandardFixedIncomeFlagEnum.BROKERAGE_COLLECTION_GS.getCode());
    }
    /**
     * 缓存服务
     */
    private static final CacheService CACHE_SERVICE = CacheServiceImpl.getInstance();

    /**
     * 查询股权订单信息集合
     */
    @Override
    public List<OwnershipOrderInfo> queryAcctOwnershipOrderInfo(QueryAcctBalanceBaseParam queryAcctBalanceBaseParam) {
        // 0.参数校验与赋值
        checkAndBuildParam(queryAcctBalanceBaseParam);
        // 1.查询订单信息
        // 1.1.代销
        List<BalanceOrderVo> balanceConsignmentOrderVoList = new ArrayList<>();
        if (StringUtils.isNotBlank(queryAcctBalanceBaseParam.getTxAcctNo())) {
            balanceConsignmentOrderVoList = highDealOrderDtlRepository.selectBalanceConsignmentOrderVo(queryAcctBalanceBaseParam.getTxAcctNo(), queryAcctBalanceBaseParam.getFundCodeList(), queryAcctBalanceBaseParam.getDisCodeList());
        }
        // 1.2.直销
        List<BalanceOrderVo> balanceDirectOrderVoList = new ArrayList<>();
        if (StringUtils.isNotBlank(queryAcctBalanceBaseParam.getHbOneNo())) {
            balanceDirectOrderVoList = cmCusttradeDirectRepository.selectBalanceDirectOrderVo(queryAcctBalanceBaseParam.getHbOneNo(), queryAcctBalanceBaseParam.getFundCodeList(), queryAcctBalanceBaseParam.getDisCodeList());
        }
        // 将直销的业务.类型转换为中台,方便后面统一处理逻辑
        if (CollectionUtils.isNotEmpty(balanceDirectOrderVoList)) {
            balanceDirectOrderVoList.forEach(order -> {
                // 类型转换
                String mBusinessCode = "1" + order.getBusinessCode();
                order.setMBusinessCode(mBusinessCode);
            });
        }
        // 1.3.合并订单信息
        List<BalanceOrderVo> balanceOrderVoList = new ArrayList<>();
        balanceOrderVoList.addAll(balanceConsignmentOrderVoList);
        balanceOrderVoList.addAll(balanceDirectOrderVoList);
        if (CollectionUtils.isEmpty(balanceOrderVoList)) {
            log.info("queryAcctOwnershipOrderInfo，根据条件没有查询出任何订单,queryAcctTradeInfoParam={}", queryAcctBalanceBaseParam);
            return new ArrayList<>();
        }
        // 2.判断首单
        // 2.1.按照交易日期排序
        balanceOrderVoList = balanceOrderVoList.stream().sorted(Comparator.comparing(BalanceOrderVo::getTradeDt)).collect(Collectors.toList());
        // 2.2.判断首单
        Set<String> valueSet = new HashSet<>();
        balanceOrderVoList.forEach(order -> {
            // 判断首单
            String joinKey = String.join(Constants.UNDERLINE, order.getFundCode(), order.getMBusinessCode());
            if (valueSet.contains(joinKey)) {
                order.setFirstOrder(false);
            } else {
                valueSet.add(joinKey);
                order.setFirstOrder(true);
            }
        });
        // 4.构建订单信息实体
        List<OwnershipOrderInfo> orderList = new ArrayList<>();
        // 4.1.如果是直销
        balanceDirectOrderVoList.forEach(balanceOrderVo -> {
            // 获取转义后的业务code
            String transferMiddleBusinessCode = getBusinessTransferCode(balanceOrderVo.getOrderNo(), balanceOrderVo.getFundCode(), balanceOrderVo.getMBusinessCode(), balanceOrderVo.isFirstOrder(), null);
            orderList.add(new DirectOrderInfo(balanceOrderVo, transferMiddleBusinessCode));
        });
        // 4.2.如果是代销
        balanceConsignmentOrderVoList.forEach(balanceOrderVo -> {
            // 获取转义后的业务code
            String transferMiddleBusinessCode = getBusinessTransferCode(balanceOrderVo.getOrderNo(), balanceOrderVo.getFundCode(), balanceOrderVo.getMBusinessCode(), balanceOrderVo.isFirstOrder(), null);
            orderList.add(new ConsignmentOrderInfo(balanceOrderVo, transferMiddleBusinessCode));
        });
        return orderList;
    }

    @Override
    public List<AcctBalanceBaseInfo> queryAcctBalanceBaseInfo(QueryAcctBalanceBaseParam param) {

        List<AcctBalanceBaseInfo> balanceBaseInfoList = new ArrayList<>();
        // 1.查询代销持仓信息
        if (StringUtils.isNotBlank(param.getTxAcctNo())) {
            List<BalanceVo> proxyBalanceVoList = custBooksRepository.selectBalanceWithLockPeriod(param.getDisCodeList(), param.getTxAcctNo(), param.getFundCodeList(), YesOrNoEnum.YES.getCode());
            for (BalanceVo balanceVo : proxyBalanceVoList) {
                AcctBalanceBaseInfo acctBalanceBaseInfo = new AcctBalanceBaseInfo();
                acctBalanceBaseInfo.setBalanceVol(balanceVo.getBalanceVol());
                acctBalanceBaseInfo.setDisCode(balanceVo.getDisCode());
                acctBalanceBaseInfo.setFundCode(balanceVo.getProductCode());
                acctBalanceBaseInfo.setIsHkProduct(YesOrNoEnum.NO.getCode());
                acctBalanceBaseInfo.setTxAcctNo(param.getTxAcctNo());
                acctBalanceBaseInfo.setHbOneNo(param.getHbOneNo());
                balanceBaseInfoList.add(acctBalanceBaseInfo);
            }
        }
        // 2.查询出直销的持仓信息
        if (StringUtils.isNotBlank(param.getHbOneNo()) && param.isIncludeDirect()) {
            List<CmCustFundDirectPo> cmBalanceList = cmCustFundDirectRepository.selectDirectBalance(param.getHbOneNo(), YesOrNoEnum.YES.getCode(), param.getDisCodeList());
            for (CmCustFundDirectPo po : cmBalanceList) {
                AcctBalanceBaseInfo directAcctBalanceBaseInfo = getDirectAcctBalanceBaseInfo(param, po);
                if (CollectionUtil.isNotEmpty(param.getFundCodeList()) && !param.getFundCodeList().contains(directAcctBalanceBaseInfo.getFundCode())) {
                    continue;
                }
                balanceBaseInfoList.add(directAcctBalanceBaseInfo);
            }
        }
        return balanceBaseInfoList;
    }


    @Override
    public List<AcctSubBalanceDetailInfo> queryAcctSubBalanceDetailInfo(QueryAcctSubBalanceDetailParam param) {
        log.info("queryAcctSubBalanceDetailInfo-查询持仓明细,param={}", param);
        List<AcctSubBalanceDetailInfo> subBalanceDetailBaseInfoList = new ArrayList<>();
        // 1.查询产品基本信息
        HighProductBaseInfoBean highProductBaseInfoBean = queryHighProductOuterService.getHighProductBaseInfo(param.getFundCode());
        if (highProductBaseInfoBean == null) {
            log.info("queryAcctSubBalanceDetailInfo-查询持仓明细,根据产品Code查不到产品信息,fundCode:{}", param.getFundCode());
            return subBalanceDetailBaseInfoList;
        }
        // 2.查询用户持仓
        List<SubCustBooksVo> subCustBooksPoList = subCustBooksRepository.selectAcctSubCustBooks(param.getTxAcctNo(), param.getFundCode());
        if (CollectionUtils.isEmpty(subCustBooksPoList)) {
            log.info("queryAcctSubBalanceDetailInfo-查询持仓明细,根据入参查不到持仓,queryAcctBalanceDetailBaseInfoParam:{} ", param);
            return subBalanceDetailBaseInfoList;
        }
        subCustBooksPoList = subCustBooksPoList.stream().sorted(Comparator.comparing(SubCustBooksVo::getAckDt, Comparator.naturalOrder())).collect(Collectors.toList());
        // 2.2.查询赎回在途持仓
        List<CustBooksDtlVo> unConfirmRedeemBalanceVolList = custBooksDtlRepository.getUnConfirmRedeemBalanceVol(param.getTxAcctNo(), param.getFundCode());
        Map<String, CustBooksDtlVo> custBooksDtlMap = unConfirmRedeemBalanceVolList.stream().collect(Collectors.toMap(x -> String.join(Constants.UNDERLINE, x.getTxAcctNo(), x.getCpAcctNo(), x.getFundCode()), x -> x));
        // 3.计算可用份额,总份额
        String appDt = DateUtils.formatToString(param.getAppDtm(), DateUtils.YYYYMMDD);
        String appTm = DateUtils.formatToString(param.getAppDtm(), DateUtils.HHMMSS);
        String taTradeDt = queryTaTradeDtCacheService.getTaTradeDt(appDt, appTm);
        String redeemDt = getSubmitDt(highProductBaseInfoBean, param.getDisCode(), taTradeDt, param.getAppDtm());

        subCustBooksPoList.forEach(subCustBooksVo -> {
            AcctSubBalanceDetailInfo subBalanceDetailInfo = new AcctSubBalanceDetailInfo();
            subBalanceDetailInfo.setTxAcctNo(subCustBooksVo.getTxAcctNo());
            subBalanceDetailInfo.setFundCode(subCustBooksVo.getFundCode());
            subBalanceDetailInfo.setAckDt(subCustBooksVo.getAckDt());
            subBalanceDetailInfo.setCpAcctNo(subCustBooksVo.getCpAcctNo());
            subBalanceDetailInfo.setOpenRedeemDt(subCustBooksVo.getOpenRedeemDt());
            // 持仓份额
            subBalanceDetailInfo.setBalanceVol(subCustBooksVo.getBalanceVol());
            // 设置在途赎回
            setRedeemUnConfirmedVol(custBooksDtlMap, subCustBooksVo);
            // 锁定份额->如果没有到赎回开放日,可用份额就是0
            if (redeemDt != null && subCustBooksVo.getOpenRedeemDt() != null && redeemDt.compareTo(subCustBooksVo.getOpenRedeemDt()) < 0) {
                subBalanceDetailInfo.setAvailVol(BigDecimal.ZERO);
            } else {
                // 可用份额=份额-在途-冻结
                BigDecimal availVol = subCustBooksVo.getBalanceVol().subtract(subCustBooksVo.getRedeemUnconfirmedVol()).subtract(subCustBooksVo.getFrznVol()).subtract(subCustBooksVo.getJustFrznVol());
                subBalanceDetailInfo.setAvailVol(BigDecimal.ZERO.compareTo(availVol) < 0 ? availVol : BigDecimal.ZERO);
            }
            subBalanceDetailBaseInfoList.add(subBalanceDetailInfo);
        });

        // 4.获取银行卡信息
        Map<String, QueryCustBankCardResult> bankCardMap = new HashMap<String, QueryCustBankCardResult>();
        CountDownLatch latch = new CountDownLatch(subBalanceDetailBaseInfoList.size());
        for (AcctSubBalanceDetailInfo info : subBalanceDetailBaseInfoList) {
            String cpAcct = info.getCpAcctNo();
            QueryCustBankCardResult bankCardInfo = new QueryCustBankCardResult();
            bankCardMap.put(cpAcct, bankCardInfo);
            CommonThreadPool.submit(new QueryCustBankCardTask(queryCustBankCardOuterService, latch, param.getTxAcctNo(), param.getDisCode(), cpAcct, bankCardInfo, param.getOutletCode()));
        }
        try {
            latch.await();
        } catch (InterruptedException e) {
            log.error("queryAcctSubBalanceDetailInfo-查询持仓明细,获取银行卡信息异常:" + e);
            Thread.currentThread().interrupt();
        }
        // 5.银行卡信息返回
        for (AcctSubBalanceDetailInfo book : subBalanceDetailBaseInfoList) {
            // 银行卡信息
            QueryCustBankCardResult result = bankCardMap.get(book.getCpAcctNo());
            if (result != null) {
                book.setBankAcct(result.getBankAcct());
                book.setBankName(result.getBankName());
                book.setBankAcctMask(result.getBankAcctMask());
                book.setBankCode(result.getBankCode());
            }
        }
        log.info("queryAcctSubBalanceDetailInfo-获取持仓明细,subBalanceDetailBaseInfoList={}", subBalanceDetailBaseInfoList);
        return subBalanceDetailBaseInfoList;
    }

    @Override
    public List<AcctBalanceBaseInfo> queryOnWayBalanceBaseInfo(QueryAcctBalanceBaseParam queryAcctBalanceBaseInfoParam) {
        checkAndBuildParam(queryAcctBalanceBaseInfoParam);
        List<AcctBalanceBaseInfo> acctBalanceBaseInfoList = new ArrayList<>();
        // 1.查询代销在途
        List<CustBooksDtlVo> onWayBuyBalanceVoList = custBooksDtlRepository.getOnWayBuyBalanceVoList(queryAcctBalanceBaseInfoParam.getTxAcctNo(), queryAcctBalanceBaseInfoParam.getFundCodeList(), queryAcctBalanceBaseInfoParam.getDisCodeList());
        if (CollectionUtils.isNotEmpty(onWayBuyBalanceVoList)) {
            for (CustBooksDtlVo custBooksDtlVo : onWayBuyBalanceVoList) {
                AcctBalanceBaseInfo acctBalanceBaseInfo = new AcctBalanceBaseInfo();
                acctBalanceBaseInfo.setBalanceVol(custBooksDtlVo.getUnconfirmedVol());
                acctBalanceBaseInfo.setTxAcctNo(custBooksDtlVo.getTxAcctNo());
                acctBalanceBaseInfo.setFundCode(custBooksDtlVo.getFundCode());
                acctBalanceBaseInfo.setDisCode(custBooksDtlVo.getDisCode());
                acctBalanceBaseInfoList.add(acctBalanceBaseInfo);
            }
        }
        // 2.查询直销在途
        if (queryAcctBalanceBaseInfoParam.isIncludeDirect()) {
            QueryAcctBalanceBaseInfoParamVo paramVo = new QueryAcctBalanceBaseInfoParamVo();
            BeanUtils.copyProperties(queryAcctBalanceBaseInfoParam, paramVo);
            List<CmCusttradeDirectPo> onWayDirectBalanceList = cmCusttradeDirectRepository.getOnWayDirectBalance(paramVo);
            if (CollectionUtils.isNotEmpty(onWayDirectBalanceList)) {
                Map<String, List<CmCusttradeDirectPo>> cmCustTradeDirectPoMap = onWayDirectBalanceList.stream().collect(Collectors.groupingBy(x -> x.getFundcode() + Constants.UNDERLINE + x.getDiscode() + x.getTxAcctNo()));
                cmCustTradeDirectPoMap.forEach((k, subList) -> {
                    BigDecimal totalVol = subList.stream().map(CmCusttradeDirectPo::getAppvol).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                    AcctBalanceBaseInfo acctBalanceBaseInfo = new AcctBalanceBaseInfo();
                    acctBalanceBaseInfo.setBalanceVol(totalVol);
                    acctBalanceBaseInfo.setTxAcctNo(subList.get(0).getTxAcctNo());
                    acctBalanceBaseInfo.setIsHkProduct(subList.get(0).getIsHkProduct());
                    acctBalanceBaseInfo.setFundCode(subList.get(0).getFundcode());
                    acctBalanceBaseInfo.setDisCode(subList.get(0).getDiscode());
                    acctBalanceBaseInfoList.add(acctBalanceBaseInfo);
                });
            }
        }
        return acctBalanceBaseInfoList;
    }

    @Override
    public List<AcctBalanceBaseInfo> queryConfirmBalanceBaseInfo(QueryAcctBalanceBaseParam param) {
        log.info("queryConfirmBalanceBaseInfo-查询确认持仓,param={}", JSON.toJSONString(param));
        // 1.参数校验
        checkAndBuildParam(param);
        List<AcctBalanceBaseInfo> balanceList = new ArrayList<>();
        // 2.查询代销确认持仓
        QueryAcctBalanceBaseInfoParamVo paramVo = new QueryAcctBalanceBaseInfoParamVo();
        BeanUtils.copyProperties(param, paramVo);
        List<BalanceVo> agentBalanceVoList = custBooksRepository.queryConfirmBalanceBaseInfo(paramVo);
        if (CollectionUtils.isNotEmpty(agentBalanceVoList)) {
            List<AcctBalanceBaseInfo> agentBalanceList = agentBalanceVoList.stream().map(balanceVo -> {
                AcctBalanceBaseInfo acctBalanceBaseInfo = new AcctBalanceBaseInfo();
                acctBalanceBaseInfo.setBalanceVol(balanceVo.getBalanceVol());
                acctBalanceBaseInfo.setDisCode(balanceVo.getDisCode());
                acctBalanceBaseInfo.setFundCode(balanceVo.getProductCode());
                acctBalanceBaseInfo.setIsHkProduct(YesOrNoEnum.NO.getCode());
                acctBalanceBaseInfo.setTxAcctNo(param.getTxAcctNo());
                acctBalanceBaseInfo.setHbOneNo(param.getHbOneNo());
                return acctBalanceBaseInfo;

            }).collect(Collectors.toList());
            balanceList.addAll(agentBalanceList);
        }
        // 2.查询直销确认持仓
        if (param.isIncludeDirect()) {
            QueryAcctBalanceBaseInfoParamVo queryAcctBalanceBaseInfoParamVo = new QueryAcctBalanceBaseInfoParamVo();
            BeanUtils.copyProperties(param, queryAcctBalanceBaseInfoParamVo);
            List<CmCustFundDirectPo> cmBalanceList = cmCustFundDirectRepository.queryConfirmBalanceBaseInfo(queryAcctBalanceBaseInfoParamVo);
            if (CollectionUtils.isNotEmpty(cmBalanceList)) {
                List<AcctBalanceBaseInfo> directBalanceInfoList = cmBalanceList.stream().map(po -> getDirectAcctBalanceBaseInfo(param, po)).collect(Collectors.toList());
                balanceList.addAll(directBalanceInfoList);
            }
        }
        return balanceList;
    }

    /**
     * 直销份额转基础信息实体
     */
    private AcctBalanceBaseInfo getDirectAcctBalanceBaseInfo(QueryAcctBalanceBaseParam param, CmCustFundDirectPo po) {
        AcctBalanceBaseInfo acctBalanceBaseInfo = new AcctBalanceBaseInfo();
        acctBalanceBaseInfo.setBalanceVol(po.getBalanceVol());
        acctBalanceBaseInfo.setDisCode(po.getDisCode());
        // 分期成立的产品,直销mjjdm记录的是主产品代码,fundCode是子产品
        if (StringUtils.isNotEmpty(po.getMjjDm())) {
            acctBalanceBaseInfo.setFundCode(po.getMjjDm());
        } else {
            acctBalanceBaseInfo.setFundCode(po.getFundCode());
        }
        acctBalanceBaseInfo.setIsHkProduct(po.getIsHkProduct());
        acctBalanceBaseInfo.setTxAcctNo(param.getTxAcctNo());
        acctBalanceBaseInfo.setHbOneNo(param.getHbOneNo());
        return acctBalanceBaseInfo;
    }

    /**
     * 设置在途份额
     */
    private void setRedeemUnConfirmedVol(Map<String, CustBooksDtlVo> custBooksDtlMap, SubCustBooksVo subCustBooksVo) {
        //在途份额
        CustBooksDtlVo custBooksDtlVo = custBooksDtlMap.get(String.join(Constants.UNDERLINE, subCustBooksVo.getTxAcctNo(), subCustBooksVo.getCpAcctNo(), subCustBooksVo.getFundCode()));
        // 1.没有在途,就直接是0
        if (custBooksDtlVo == null) {
            subCustBooksVo.setRedeemUnconfirmedVol(BigDecimal.ZERO);
            return;
        }
        BigDecimal unconfirmedVol = custBooksDtlVo.getUnconfirmedVol();
        if (unconfirmedVol == null || BigDecimal.ZERO.compareTo(unconfirmedVol) >= 0) {
            subCustBooksVo.setRedeemUnconfirmedVol(BigDecimal.ZERO);
            return;
        }

        BigDecimal unFrznVol = subCustBooksVo.getBalanceVol().subtract(subCustBooksVo.getFrznVol()).subtract(subCustBooksVo.getJustFrznVol());
        // 2.如果在途的大于未冻结的份额,当前未冻结持仓全部赎回在途
        if (unconfirmedVol.compareTo(unFrznVol) >= 0) {
            subCustBooksVo.setRedeemUnconfirmedVol(unFrznVol);
            custBooksDtlVo.setUnconfirmedVol(unconfirmedVol.subtract(unFrznVol));
        } else {
            // 3.小于未冻结持仓,在途的就是所有在途赎回
            subCustBooksVo.setRedeemUnconfirmedVol(unconfirmedVol);
            custBooksDtlVo.setUnconfirmedVol(BigDecimal.ZERO);
        }
    }

    @Override
    public List<AcctBalanceDetailBaseInfo> queryAcctBalanceDetailBaseInfo(QueryAcctBalanceDetailBaseInfoParam param) {
        log.info("queryAcctBalanceDetailBaseInfo-查询持仓明细,queryAcctBalanceDetailBaseInfoParam={}", param);
        List<AcctBalanceDetailBaseInfo> balanceDetailBaseInfoList = new ArrayList<>();
        // 1.查询产品基本信息
        HighProductBaseInfoBean highProductBaseInfoBean = queryHighProductOuterService.getHighProductBaseInfo(param.getFundCode());
        if (highProductBaseInfoBean == null) {
            log.info("queryAcctBalanceDetailBaseInfo-查询持仓明细,根据产品Code查不到产品信息,fundCode:{}", param.getFundCode());
            return balanceDetailBaseInfoList;
        }
        // 2.获取上报日
        String submitDt = getSubmitDt(highProductBaseInfoBean, param.getDisCodeList().get(0), param.getTradeDt(), param.getAppDate());
        log.info("queryAcctBalanceDetailBaseInfo-查询持仓明细,submitDt:{},queryAcctBalanceDetailBaseInfoParam:{} ", submitDt, param);

        // 3.查询用户持仓
        List<BalanceVo> booklist = custBooksRepository.selectBalanceDtlByDisCodeList(param.getDisCodeList(), param.getTxAcctNo(), null, param.getFundCode(), param.getCpAcctNo(), submitDt);
        if (CollectionUtils.isEmpty(booklist)) {
            log.info("queryAcctBalanceDetailBaseInfo-查询持仓明细,根据入参查不到持仓,submitDt={},queryAcctBalanceDetailBaseInfoParam:{} ", submitDt, param);
            return balanceDetailBaseInfoList;
        }
        // 4.获取可用份额
        Set<String> cpAcctNoSet = new HashSet<>();
        Iterator<BalanceVo> bookListIterator = booklist.iterator();
        while (bookListIterator.hasNext()) {
            BalanceVo book = bookListIterator.next();
            // 可用份额
            BigDecimal availVol = book.getBalanceVol().subtract(book.getUnconfirmedVol()).subtract(book.getJustFrznVol());
            if (RedeemTypeEnum.YES.getCode().equals(highProductBaseInfoBean.getHasLockPeriod())) {
                availVol = availVol.subtract(book.getLockingPeriodVol());
            }
            if (availVol.compareTo(BigDecimal.ZERO) <= 0) {
                bookListIterator.remove();
            } else {
                cpAcctNoSet.add(book.getCpAcctNo());
            }
        }

        // 5.获取银行卡信息
        Map<String, QueryCustBankCardResult> bankCardMap = new HashMap<String, QueryCustBankCardResult>();
        CountDownLatch latch = new CountDownLatch(cpAcctNoSet.size());
        for (Object value : cpAcctNoSet.toArray()) {
            String cpAcct = (String) value;
            QueryCustBankCardResult bankCardInfo = new QueryCustBankCardResult();
            bankCardMap.put(cpAcct, bankCardInfo);
            CommonThreadPool.submit(new QueryCustBankCardTask(queryCustBankCardOuterService, latch, param.getTxAcctNo(), param.getDisCodeList().get(0), cpAcct, bankCardInfo, param.getTxId()));
        }
        try {
            latch.await();
        } catch (InterruptedException e) {
            log.error("queryAcctBalanceDetailBaseInfo-查询持仓明细,获取银行卡信息异常:" + e);
            Thread.currentThread().interrupt();
        }

        List<AcctBalanceDetailBaseInfo> baseInfoList = new ArrayList<>();
        setBalanceInfo(param, highProductBaseInfoBean, booklist, bankCardMap, baseInfoList);
        return baseInfoList;
    }

    /**
     * 设置持仓信息
     */
    private void setBalanceInfo(QueryAcctBalanceDetailBaseInfoParam param, HighProductBaseInfoBean highProductBaseInfoBean,
                                List<BalanceVo> booklist, Map<String, QueryCustBankCardResult> bankCardMap, List<AcctBalanceDetailBaseInfo> baseInfoList) {
        BigDecimal totalVol = BigDecimal.ZERO;
        BigDecimal totalUnConfirmVol = BigDecimal.ZERO;
        BigDecimal totalAvailVol = BigDecimal.ZERO;
        BigDecimal totalRedeemAllSurplusVol = BigDecimal.ZERO;
        for (BalanceVo book : booklist) {
            AcctBalanceDetailBaseInfo baseInfo = new AcctBalanceDetailBaseInfo();
            baseInfo.setTxAcctNo(param.getTxAcctNo());
            baseInfo.setDisCode(param.getDisCodeList().get(0));
            baseInfo.setProductCode(book.getProductCode());
            baseInfo.setCpAcctNo(book.getCpAcctNo());
            baseInfo.setProtocolNo(book.getProtocolNo());
            totalVol = MathUtils.add(book.getBalanceVol(), totalVol);
            totalUnConfirmVol = MathUtils.add(book.getUnconfirmedVol(), totalUnConfirmVol);
            totalUnConfirmVol = MathUtils.add(totalUnConfirmVol, book.getJustFrznVol());
            // 可用份额
            BigDecimal availVol = book.getBalanceVol().subtract(book.getUnconfirmedVol()).subtract(book.getJustFrznVol());
            if (RedeemTypeEnum.YES.getCode().equals(highProductBaseInfoBean.getHasLockPeriod())) {
                availVol = availVol.subtract(book.getLockingPeriodVol());
            }
            baseInfo.setAvailVol(MoneyUtil.formatMoney(availVol, 2));
            totalAvailVol = MathUtils.add(totalAvailVol, availVol);
            // 全赎剩余份额 = 未到锁定期 + 司法冻结
            if (RedeemTypeEnum.YES.getCode().equals(highProductBaseInfoBean.getHasLockPeriod())) {
                baseInfo.setRedeemAllSurplusVol(MathUtils.add(book.getLockingPeriodVol(), book.getJustFrznVol()));
            } else {
                baseInfo.setRedeemAllSurplusVol(book.getJustFrznVol());
            }
            totalRedeemAllSurplusVol = MathUtils.add(totalRedeemAllSurplusVol, baseInfo.getRedeemAllSurplusVol());
            // 基金信息
            baseInfo.setProductName(highProductBaseInfoBean.getFundAttr());
            baseInfo.setProductType(highProductBaseInfoBean.getFundType());
            baseInfo.setFundShareClass(highProductBaseInfoBean.getShareClass());
            baseInfo.setProductChannel(highProductBaseInfoBean.getProductChannel());
            baseInfo.setBalanceVol(MoneyUtil.formatMoney(book.getBalanceVol(), 2));
            // 银行卡信息
            QueryCustBankCardResult result = bankCardMap.get(book.getCpAcctNo());
            if (result != null) {
                baseInfo.setBankCode(result.getBankCode());
                baseInfo.setBankName(result.getBankRegionName());
                baseInfo.setBankAcctNo(result.getBankAcct());
            }
            baseInfoList.add(baseInfo);
        }
        for (AcctBalanceDetailBaseInfo baseInfo : baseInfoList) {
            baseInfo.setTotalVol(totalVol);
            baseInfo.setTotalUnConfirmVol(totalUnConfirmVol);
            baseInfo.setTotalAvailVol(totalAvailVol);
            baseInfo.setTotalRedeemAllSurplusVol(totalRedeemAllSurplusVol);
        }
    }

    /**
     * getSubmitDt:(获取上报日期)
     *
     * @param highProductBaseInfoBean 产品基本信息
     * @param disCode                 分销机构代码
     * @param taTradeDt               当前日期所属交易日
     * @param appDate                 申请日期
     * @return
     * <AUTHOR>
     * @date 2018年6月15日 下午2:19:11
     */
    private String getSubmitDt(HighProductBaseInfoBean highProductBaseInfoBean, String disCode, String taTradeDt, Date appDate) {
        log.info("QueryAcctBalanceDtlFacadeService|getTaTradeDt|highProductBaseInfoBean:{}, disCode:{},  taTradeDt:{}, appDate:{}", JSON.toJSONString(highProductBaseInfoBean), disCode, taTradeDt, appDate);
        if (SupportAdvanceFlagEnum.SupportBuyAndRedeemAdvance.getCode().equals(highProductBaseInfoBean.getIsScheduledTrade())
                || SupportAdvanceFlagEnum.SupportRedeemAdvance.getCode().equals(highProductBaseInfoBean.getIsScheduledTrade())) {

            ProductAppointmentInfoBean productAppointmentInfoBean = queryHighProductOuterService.queryAppointmentInfoByAppointDate(highProductBaseInfoBean.getFundCode(), "1", highProductBaseInfoBean.getShareClass(), disCode, appDate);
            if (productAppointmentInfoBean != null) {
                if (taTradeDt.compareTo(productAppointmentInfoBean.getOpenStartDt()) < 0) {
                    return productAppointmentInfoBean.getOpenStartDt();
                }
            }
        }

        return taTradeDt;

    }

    @Override
    public Map<String, OwnershipOrderDto> getOwnershipOrderInfoMap(QueryAcctBalanceBaseParam param) {
        log.info("getOwnershipOrderInfoMap-param={}", JSON.toJSONString(param));
        if (CollectionUtils.isEmpty(param.getFundCodeList())) {
            return new HashMap<>();
        }
        QueryAcctBalanceBaseParam queryAcctBalanceBaseParam = new QueryAcctBalanceBaseParam();
        queryAcctBalanceBaseParam.setTxAcctNo(param.getTxAcctNo());
        queryAcctBalanceBaseParam.setHbOneNo(param.getHbOneNo());
        queryAcctBalanceBaseParam.setDisCodeList(param.getDisCodeList());
        queryAcctBalanceBaseParam.setFundCodeList(param.getFundCodeList());
        List<OwnershipOrderInfo> ownershipOrderInfos = queryAcctOwnershipOrderInfo(queryAcctBalanceBaseParam);
        // 按照商品维度,计算成本
        Map<String, List<OwnershipOrderInfo>> productMap = ownershipOrderInfos.stream().collect(Collectors.groupingBy(OwnershipOrderInfo::getFundCode));
        HashMap<String, OwnershipOrderDto> netBuyAmtMap = new HashMap<>(productMap.size());
        productMap.forEach((fundCode, orders) -> {
            ProductOrderInfo productOrderInfo = new ProductOrderInfo();
            productOrderInfo.setProductCode(fundCode);
            productOrderInfo.setOrderInfoList(orders);
            netBuyAmtMap.put(fundCode, new OwnershipOrderDto(productOrderInfo.getNetBuyAmount(), productOrderInfo.getOwnershipTransferIdentity()));
        });
        return netBuyAmtMap;
    }

    @Override
    public String getIsFirstBuy(String fundCode, String txAcctNo, String disCode) {
        log.info("getFirstBuyFlag-获取是否首次购买-fundCode={},txAcctNo={},disCode={}", fundCode, txAcctNo, disCode);
        // 1.查询产品基础信息
        HighProductInfoBean highProductBaseInfo = queryHighProductOuterService.getHighProductInfo(fundCode);
        if (DisCodeEnum.HZ.getCode().equals(disCode)) {
            if (!YesOrNoEnum.YES.getCode().equals(highProductBaseInfo.getPeDivideCallFlag())) {
                boolean isSuppleSubs = suppleSubsService.isSuppleSubs(txAcctNo, fundCode);
                log.info("getFirstBuyFlag-好臻非分次call产品,isSuppleSubs={}", isSuppleSubs);
                return isSuppleSubs ? YesOrNoEnum.NO.getCode() : YesOrNoEnum.YES.getCode();
            } else {
                // 好臻的,分次call,如果没有确认持仓就是首单
                QueryAcctBalanceBaseParam param = new QueryAcctBalanceBaseParam();
                param.setTxAcctNo(txAcctNo);
                param.setFundCodeList(Collections.singletonList(fundCode));
                param.setDisCodeList(Collections.singletonList(disCode));
                List<AcctBalanceBaseInfo> confirmBalanceBaseInfoList = queryConfirmBalanceBaseInfo(param);
                log.info("getFirstBuyFlag-好臻分次call产品,confirmBalanceBaseInfoList={}", confirmBalanceBaseInfoList);
                return CollectionUtils.isEmpty(confirmBalanceBaseInfoList) ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode();
            }
        } else {
            boolean isSuppleSubs = suppleSubsService.isSuppleSubs(txAcctNo, fundCode);
            log.info("getFirstBuyFlag-非好臻产品,isSuppleSubs={}", isSuppleSubs);
            return isSuppleSubs ? YesOrNoEnum.NO.getCode() : YesOrNoEnum.YES.getCode();
        }
    }

    @Override
    public List<RefundDealOrderInfo> queryRefundAmtDealOrderInfo(QueryBalanceParam queryBalanceParam) {
        log.info("queryRefundAmtDealOrderInfo-获取待资金到账订单-queryBalanceParam={}", JSON.toJSONString(queryBalanceParam));
        // 获取当前自然日
        String naturalDt = DateUtil.date2String(new Date(), DateUtil.SHORT_DATEPATTERN);
        // 获取当前工作日
        String tradeDt = queryTradeDayOuterService.getWorkDay(new Date());
        // 获取前3个工作日的日期
        String before3TradeDay = tradeDayService.addTradeDaysByCache(tradeDt, -3);
        // 获取前15个工作日的日期
        String before15TradeDay = tradeDayService.addTradeDaysByCache(tradeDt, -15);
        List<RefundDealOrderInfo> buyRefundList = new ArrayList<>();
        List<RefundDealOrderInfo> redeemReturnList = new ArrayList<>();
        try {
            // 如果只需要查询香港的,那么下单未退款的就不需要查询了,因为这种都是代销,代销没有香港的
            buyRefundList = getBuyRefundList(queryBalanceParam, naturalDt, before3TradeDay);
            // 赎回待回款订单数：取满足以下条件的订单笔数
            redeemReturnList = getRedeemReturnList(queryBalanceParam, naturalDt, before3TradeDay, before15TradeDay);
        } catch (Exception e) {
            log.error("queryRefundAmtDealOrderInfo-获取待资金到账订单,查询待资金到账订单异常:", e);
        }
        List<RefundDealOrderInfo> refundDealOrderList = new ArrayList<>();
        refundDealOrderList.addAll(buyRefundList);
        refundDealOrderList.addAll(redeemReturnList);
        return refundDealOrderList;
    }


    /**
     * 购买待退款订单数
     */

    private List<RefundDealOrderInfo> getBuyRefundList(QueryBalanceParam queryBalanceParam, String naturalDt, String before3TradeDay) {
        // 代销
        List<RefundDealOrderVo> consignmentBuyRefundList = dealOrderRepository.selectConsignmentBuyRefund(queryBalanceParam.getTxAcctNo()
                , queryBalanceParam.getDisCodeList(), queryBalanceParam.getFundCode(), before3TradeDay, naturalDt, before3TradeDay);
        if (CollectionUtil.isEmpty(consignmentBuyRefundList)) {
            log.info("QueryFinReceiptFacadeService|getConsignmentBuyRefund is empty");
            return new ArrayList<>();
        }
        // 非合并上报的订单，按照订单维度统计返回笔数。合并上报的订单，按照主订单维度统计返回笔数
        consignmentBuyRefundList = mergeConsignmentRefund(consignmentBuyRefundList);
        // consignmentBuyRefundList转为buyRefundList
        return buildRefundDealOrderByVo(consignmentBuyRefundList, YesOrNoEnum.YES.getCode());
    }

    /**
     * 合并代销部分交易记录
     * 遍历代销部分交易记录,找出 mergeSubmitFlag ='1' && mainDealOrderNo is not null 的,
     * 然后按照将这部分交易记录,按照mainDealOrderNo 分组 ,每组合并为一条新的交易记录,
     * 替换为分组前的这部分交易记录.
     * dealNo=mainDealOrderNo,
     * refundAmt=每组的refundAmt汇总求和,
     * fundCode 取dealNo=mainDealOrderNo那条记录的fundCode,
     * mBusiCode取dealNo=mainDealOrderNo那条记录的mBusiCode
     *
     * @param consignmentBuyRefundList 原始交易记录
     * @return 合并后的交易记录
     */
    private List<RefundDealOrderVo> mergeConsignmentRefund(List<RefundDealOrderVo> consignmentBuyRefundList) {
        // 找出需要合并的记录, 按 mainDealOrderNo 分组
        Map<String, List<RefundDealOrderVo>> toMergeMap = consignmentBuyRefundList.stream()
                .filter(vo -> "1".equals(vo.getMergeSubmitFlag()) && StringUtils.isNotBlank(vo.getMainDealOrderNo()))
                .collect(Collectors.groupingBy(RefundDealOrderVo::getMainDealOrderNo));

        // 如果没有需要合并的记录，直接返回原列表
        if (toMergeMap.isEmpty()) {
            return consignmentBuyRefundList;
        }

        // 找出不需要合并的记录
        List<RefundDealOrderVo> resultList = consignmentBuyRefundList.stream()
                .filter(vo -> !"1".equals(vo.getMergeSubmitFlag()) || StringUtils.isBlank(vo.getMainDealOrderNo()))
                .collect(Collectors.toList());

        // 遍历分组，合并记录
        toMergeMap.forEach((mainDealOrderNo, subList) -> {
            // 找到主订单记录 (dealNo=mainDealOrderNo)
            RefundDealOrderVo mainOrder = subList.stream()
                    .filter(vo -> mainDealOrderNo.equals(vo.getDealNo()))
                    .findFirst()
                    .orElse(subList.get(0)); // 兜底策略：如果没找到完全匹配的主订单，使用组内第一个

            // 汇总求和 refundAmt
            BigDecimal totalRefundAmt = subList.stream()
                    .map(RefundDealOrderVo::getRefundAmt)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 创建一条新的合并后的交易记录
            RefundDealOrderVo mergedOrder = new RefundDealOrderVo();
            // 属性从主订单记录复制，以保留 fundCode, mBusiCode 等信息
            mergedOrder.setMergeSubmitFlag(mainOrder.getMergeSubmitFlag());
            mergedOrder.setMainDealOrderNo(mainOrder.getMainDealOrderNo());
            mergedOrder.setTxAcctNo(mainOrder.getTxAcctNo());
            mergedOrder.setFundCode(mainOrder.getFundCode());
            mergedOrder.setMBusiCode(mainOrder.getMBusiCode());
            mergedOrder.setDealNo(mainDealOrderNo);
            mergedOrder.setRefundAmt(totalRefundAmt);
            mergedOrder.setDealNo(mainDealOrderNo);
            mergedOrder.setRefundAmt(totalRefundAmt);
            resultList.add(mergedOrder);
        });
        return resultList;
    }

    /**
     * 赎回待回款订单数
     */
    private List<RefundDealOrderInfo> getRedeemReturnList(QueryBalanceParam queryBalanceParam, String naturalDt,
                                                          String before3TradeDay, String before15TradeDay) {
        log.info("getRedeemReturnList-查询参数,queryBalanceParame:{}", JSON.toJSONString(queryBalanceParam));
        // 代销
        List<RefundDealOrderVo> consignmentRedeemReturnList = getConsignmentRedeemReturnList(queryBalanceParam, naturalDt, before15TradeDay, before3TradeDay);
        List<RefundDealOrderVo> redeemReturnList = new ArrayList<>(consignmentRedeemReturnList);
        // 直销
        List<RefundDealOrderVo> dbUnconfirmedList = dealOrderRepository.selectDirectRedeemReturn(queryBalanceParam, before3TradeDay);
        if (dbUnconfirmedList == null) {
            log.info("QueryFinReceiptFacadeService|getDirectRedeemReturn is null,queryBalanceParam:{},before3TradeDay:{}", JSON.toJSONString(queryBalanceParam), before15TradeDay);
            dbUnconfirmedList = new ArrayList<>();
        }
        redeemReturnList.addAll(dbUnconfirmedList);
        log.info("getRedeemReturnList-赎回待回款订单数,redeemReturnList={}", JSON.toJSONString(redeemReturnList));
        return buildRefundDealOrderByVo(redeemReturnList, YesOrNoEnum.NO.getCode());
    }

    /**
     * 将info转为vo
     */
    private List<RefundDealOrderInfo> buildRefundDealOrderByVo(List<RefundDealOrderVo> redeemReturnList, String isBuyRefund) {
        return redeemReturnList.stream().map(vo -> {
            RefundDealOrderInfo info = new RefundDealOrderInfo();
            info.setDealNo(vo.getDealNo());
            info.setRefundAmt(vo.getRefundAmt());
            info.setFundCode(vo.getFundCode());
            info.setMBusiCode(vo.getMBusiCode());
            info.setTxAcctNo(vo.getTxAcctNo());
            info.setIsBuyFund(isBuyRefund);
            info.setRedeemDirection(vo.getRedeemDirection());
            return info;
        }).collect(Collectors.toList());
    }

    /**
     * 赎回待回款订单数-代销
     * 1\【资金状态】不是已出款的、当前工作日-确认日期&lt;=15个工作日的、基金所属的TA不是直销、或者基金所属的TA是直销且基金管理人生成托管指令的、
     * 现金分红、赎回、强赎、清盘订单计入【赎回待回款订单数】， add 确认金额不为0的 20230209
     * add 过滤掉异常赎回的交易、确认状态是终态（确认成功、确认失败、部分确认的） 20230213
     * 2\【资金状态】不是已出款的、当前工作日-确认日期&lt;=3个工作日的、基金所属的TA是直销且基金管理人不生成托管指令的、
     * 现金分红、赎回、强赎、清盘订单计入【赎回待回款订单数】， add 确认金额不为0的 20230209
     * add 过滤掉异常赎回的交易、确认状态是终态（确认成功、确认失败、部分确认的） 20230213
     */
    private List<RefundDealOrderVo> getConsignmentRedeemReturnList(QueryBalanceParam queryBalanceParam, String naturalDt,
                                                                   String before15TradeDay, String before3TradeDay) {
        log.info("getConsignmentRedeemReturnList-代销待回款订单,queryBalanceParam:{}", JSON.toJSONString(queryBalanceParam));
        List<RefundDealOrderVo> consignmentRedeemReturnList = new ArrayList<>();

        List<RefundDealOrderVo> consignmentRedeemReturn15List = new ArrayList<>();
        List<RefundDealOrderVo> consignmentRedeemReturn3ListUnFilter = new ArrayList<>();
        List<String> orders = new ArrayList<>();
        // 查询资金将状态不是已出款的
        notPayOutRefundInfo(queryBalanceParam, before15TradeDay, before3TradeDay, consignmentRedeemReturnList, consignmentRedeemReturn15List, consignmentRedeemReturn3ListUnFilter, orders);

        // 3【资金状态】是已出款的、【出款日期】=当前工作日、当前工作日-确认日期<=15个工作日的、
        //          现金分红、赎回、强赎、清盘订单计入【赎回待回款订单数】， add 确认金额不为0的 20230209
        //          add 过滤掉异常赎回的交易、确认状态是终态（确认成功、确认失败、部分确认的） 20230213
        List<RefundDealOrderVo> consignmentRedeemReturnPayList = dealOrderRepository.selectConsignmentRedeemReturnPay(queryBalanceParam, naturalDt, before15TradeDay);
        if (CollectionUtil.isNotEmpty(consignmentRedeemReturnPayList)) {
            for (RefundDealOrderVo vo : consignmentRedeemReturnPayList) {
                if (orders.contains(vo.getDealNo())) {
                    continue;
                }
                orders.add(vo.getDealNo());
                consignmentRedeemReturnList.add(vo);
            }
        }

        if (CollectionUtil.isEmpty(consignmentRedeemReturnList)) {
            return consignmentRedeemReturnList;
        }
        // 非合并上报的订单，按照订单维度统计返回笔数。合并上报的订单，按照主订单维度统计返回笔数
        consignmentRedeemReturnList = mergeConsignmentRefund(consignmentRedeemReturnList);
        log.info("getConsignmentRedeemReturnList-赎回待回款订单数,结果,consignmentRedeemReturnList={}", JSON.toJSONString(consignmentRedeemReturnList));
        return consignmentRedeemReturnList;
    }

    private void notPayOutRefundInfo(QueryBalanceParam queryBalanceParam, String before15TradeDay, String before3TradeDay,
                                     List<RefundDealOrderVo> consignmentRedeemReturnList, List<RefundDealOrderVo> consignmentRedeemReturn15List,
                                     List<RefundDealOrderVo> consignmentRedeemReturn3ListUnFilter, List<String> orders) {
        List<RefundDealOrderVo> consignmentRedeemReturn3List;
        List<RefundDealOrderVo> consignmentRedeemReturnNotPayList = dealOrderRepository.selectConsignmentRedeemReturnNotPay(queryBalanceParam.getTxAcctNo(), queryBalanceParam.getDisCodeList(), queryBalanceParam.getFundCode(), before15TradeDay);
        if (CollectionUtil.isNotEmpty(consignmentRedeemReturnNotPayList)) {
            List<String> productCodes = consignmentRedeemReturnNotPayList.stream()
                    .map(RefundDealOrderVo::getFundCode)
                    .distinct().collect(Collectors.toList());
            // 查询产品基本信息，获取ta及基金管理人
            List<HighProductBaseInfoBean> highProductBaseInfoList = queryHighProductOuterService.getHighProductBaseInfoList(productCodes);
            if (highProductBaseInfoList == null) {
                log.info("根据基金代码查询不到基金信息,productCodes={}", productCodes);
                return;
            }
            List<String> taCodeList = consignmentRedeemReturnNotPayList.stream()
                    .map(RefundDealOrderVo::getTaCode)
                    .distinct().collect(Collectors.toList());
            // 转map
            Map<String, HighProductBaseInfoBean> highProductBaseInfoMap = highProductBaseInfoList.stream()
                    .collect(Collectors.toMap(HighProductBaseInfoBean::getFundCode, bean -> bean));
            // 查询 isCreEntrustOrder-基金管理人是否生成托管指令 0-否；1-是
            Map<String, FundManDto> fundManDtoMap = queryFundManService.execute();
            // 查询TA
            List<TaInfoModel> taInfoList = taInfoService.queryTaInfoByTaCodes(taCodeList);
            List<String> highChannelList = Arrays.asList(ProductChannelNewEnum.TP_SM.getCode(), ProductChannelNewEnum.HIGH_FUND.getCode());
            Map<String, TaInfoModel> taDtoMap = taInfoList.stream()
                    .filter(x -> highChannelList.contains(x.getProductChannel()))
                    .collect(Collectors.toMap(TaInfoModel::getTaCode, bean -> bean, (existing, replacement) -> existing));

            // 设置产品类型信息
            setFundTypeInfo(consignmentRedeemReturn15List, consignmentRedeemReturn3ListUnFilter, orders, consignmentRedeemReturnNotPayList, highProductBaseInfoMap, fundManDtoMap, taDtoMap);
            // 由于sql查询时，确认日期范围是15天，需求要求3天，需过滤
            consignmentRedeemReturn3List = consignmentRedeemReturn3ListUnFilter.stream().filter(
                    vo -> Integer.parseInt(vo.getAckDt()) >= Integer.parseInt(before3TradeDay)).collect(Collectors.toList());

            consignmentRedeemReturnList.addAll(consignmentRedeemReturn15List);
            consignmentRedeemReturnList.addAll(consignmentRedeemReturn3List);
        }
    }

    /**
     * 设置产品类型信息
     */
    private void setFundTypeInfo(List<RefundDealOrderVo> consignmentRedeemReturn15List,
                                 List<RefundDealOrderVo> consignmentRedeemReturn3ListUnFilter,
                                 List<String> orders, List<RefundDealOrderVo> consignmentRedeemReturnNotPayList,
                                 Map<String, HighProductBaseInfoBean> highProductBaseInfoMap,
                                 Map<String, FundManDto> fundManDtoMap, Map<String, TaInfoModel> taDtoMap) {
        // 遍历分类
        for (RefundDealOrderVo vo : consignmentRedeemReturnNotPayList) {
            // 拆单处理
            if (orders.contains(vo.getDealNo())) {
                log.error("没有订单号,不处理改订单,vo={}", JSON.toJSONString(vo));
                continue;
            }
            orders.add(vo.getDealNo());
            HighProductBaseInfoBean productBaseInfo = highProductBaseInfoMap.get(vo.getFundCode());
            if (productBaseInfo == null) {
                log.error("根据产品代码查不到产品基本信息, productCode:{}", vo.getFundCode());
                continue;
            }
            TaInfoModel taDto = taDtoMap.get(productBaseInfo.getTaCode());
            if (taDto == null) {
                log.error("QueryFinReceiptFacadeService|taDto is null, taCode:{}", productBaseInfo.getTaCode());
                continue;
            }
            FundManDto fundManDto = fundManDtoMap.get(productBaseInfo.getFundManCode());
            if (fundManDto == null) {
                log.error("QueryFinReceiptFacadeService|fundManDto is null, fundManCode:{}", productBaseInfo.getFundManCode());
                continue;
            }
            // TaDto#saleType:0-直销，1-代销
            // FundManDto#isCreEntrustOrder:0-否；1-是
            if ("1".equals(taDto.getSaleType()) || ("0".equals(taDto.getSaleType()) && "1".equals(fundManDto.getIsCreEntrustOrder()))) {
                // 基金所属的TA不是直销、或者基金所属的TA是直销且基金管理人生成托管指令
                consignmentRedeemReturn15List.add(vo);
            } else if ("0".equals(taDto.getSaleType()) && "0".equals(fundManDto.getIsCreEntrustOrder())) {
                //基金所属的TA是直销且基金管理人不生成托管指令
                consignmentRedeemReturn3ListUnFilter.add(vo);
            } else {
                log.error("QueryFinReceiptFacadeService|存在未匹配场景,TaDto#taCode:{},TaDto#saleType:{},FundManDto#fundManCode:{}," +
                                "FundManDto#isCreEntrustOrder{}", productBaseInfo.getTaCode(), taDto.getSaleType(),
                        productBaseInfo.getFundManCode(), fundManDto.getIsCreEntrustOrder());
            }
        }
    }


    /**
     * 参数校验与赋值
     */
    private void checkAndBuildParam(QueryAcctBalanceBaseParam queryAcctBalanceBaseParam) {
        if (StringUtils.isBlank(queryAcctBalanceBaseParam.getTxAcctNo()) && StringUtils.isBlank(queryAcctBalanceBaseParam.getHbOneNo())) {
            throw new BusinessException(ExceptionCodes.PARAMS_ERROR, "一账通/交易账号不能同时为空");
        }
        if (CollectionUtils.isEmpty(queryAcctBalanceBaseParam.getFundCodeList())) {
            throw new BusinessException(ExceptionCodes.PARAMS_ERROR, "产品编码不能为空");
        }

        if (StringUtils.isBlank(queryAcctBalanceBaseParam.getTxAcctNo())) {
            String txAcctNo = queryHbOneNoOuterService.queryTxAcctNoByHbOneNo(queryAcctBalanceBaseParam.getHbOneNo());
            queryAcctBalanceBaseParam.setTxAcctNo(txAcctNo);
        }
        if (StringUtils.isBlank(queryAcctBalanceBaseParam.getHbOneNo())) {
            String hbOneNo = queryHbOneNoOuterService.queryHbOneNoByTxAcctNo(queryAcctBalanceBaseParam.getTxAcctNo());
            queryAcctBalanceBaseParam.setHbOneNo(hbOneNo);
        }
    }

    /**
     * 获取转义的业务编码
     *
     * @param dealNo                 订单号
     * @param fundCode               产品编码
     * @param currBusinessCode       当前订单的业务编码
     * @param isFirstOrder           该产品+业务类型+该用户是否是首次
     * @param businessCodeConfigList 配置信息
     * @return 转义后的业务编码
     */
    public String getBusinessTransferCode(String dealNo, String fundCode, String currBusinessCode, boolean isFirstOrder, List<BusinessCodeTransferConfigInfoBean> businessCodeConfigList) {
        // 1.查询所有配置
        if (CollectionUtils.isEmpty(businessCodeConfigList)) {
            log.info("getBusinessTransferCode-没有任何业务转义配置,dealNo={}", dealNo);
            return currBusinessCode;
        }
        // 2.遍历匹配配置
        for (BusinessCodeTransferConfigInfoBean businessConfBean : businessCodeConfigList) {
            // 2.1.首先按照交易类型+产品去匹配,匹配不上就不需要走后面逻辑
            if (!currBusinessCode.equals(businessConfBean.getCurrMBusinessCode()) || !fundCode.equals(businessConfBean.getFundCode())) {
                continue;
            }
            // 2.2.按照订单号匹配
            if (StringUtils.isNotBlank(businessConfBean.getDealNo())) {
                if (businessConfBean.getDealNo().equals(dealNo)) {
                    return businessConfBean.getTransferMBusinessCode();
                }
            }

            // 2.3.如果没有设置订单号,如果每次都生效,直接返回每次都生效的配置转义
            if (EffectiveType.EVERY_ORDER.getType() == businessConfBean.getEffectiveType()) {
                return businessConfBean.getTransferMBusinessCode();
            }
            // 2.4.仅首次生效
            if (EffectiveType.FIRST_ORDER.getType() == businessConfBean.getEffectiveType() && isFirstOrder) {
                return businessConfBean.getTransferMBusinessCode();
            }
        }
        // 没有匹配上,就不需要转义
        return currBusinessCode;
    }


    /**
     * @param request 查询请求
     * @return java.util.List<com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.CustConfirmBalanceDto>
     * @description: 查询用户确认持仓信息
     * @author: yun.lu
     */
    @Override
    public List<CustConfirmBalanceDto> queryCustConfirmBalance(QueryConfirmBalanceParam request) {
        log.info("queryCustConfirmBalance-开始，参数：{}", JSON.toJSONString(request));
        List<CustConfirmBalanceDto> resultList = new ArrayList<>();
        // 设置代销确认持仓
        setAgentConfirmBalance(request, resultList);
        // 添加直销确认持仓
        setDirectConfirmBalance(request, resultList);
        log.info("queryCustConfirmBalance-结束，结果：{}", JSON.toJSONString(resultList));
        return resultList;
    }

    @Override
    public Map<String, List<AckDealOrderInfo>> getAgentAckDealDtlMap(String txAcctNo, Set<String> fundCodes) {
        Map<String, List<AckDealOrderInfo>> map = new HashMap<>();
        if (CollectionUtils.isEmpty(fundCodes)) {
            log.info("getAgentAckDealDtlMap-没有基金代码,不需要查询确认交易");
            return map;
        }
        List<HighDealOrderDtlLatestAckVo> dealList = highDealOrderDtlRepository.selectAckPurchaseDealDtl(txAcctNo, fundCodes);
        if (CollectionUtils.isEmpty(dealList)) {
            log.info("getAgentAckDealDtlMap-根据基金代码,查询不到确认交易,txAcctNo={},fundCodes={}", txAcctNo, JSON.toJSON(fundCodes));
            return map;
        }
        return dealList.stream().map(x -> {
            AckDealOrderInfo ackDealOrderInfo = new AckDealOrderInfo();
            ackDealOrderInfo.setFundCode(x.getFundCode());
            ackDealOrderInfo.setDealNo(x.getDealNo());
            ackDealOrderInfo.setSubmitDt(x.getSubmitTaDt());
            ackDealOrderInfo.setAckAmt(x.getAckAmt());
            ackDealOrderInfo.setAckDt(x.getAckDt());
            ackDealOrderInfo.setAckVol(x.getAckVol());
            ackDealOrderInfo.setAckAmt(x.getAckAmt());
            ackDealOrderInfo.setFee(x.getFee());
            return ackDealOrderInfo;
        }).collect(Collectors.groupingBy(AckDealOrderInfo::getFundCode));
    }

    @Override
    public Map<String, List<AckDealOrderInfo>> getDirectAckDealDtlMap(String hboneNo, Set<String> fundCodes) {
        Map<String, List<AckDealOrderInfo>> latestAckDealDtlMap = new HashMap<>();
        if (CollectionUtils.isEmpty(fundCodes)) {
            log.info("getLatestDirectAckDealDtlMap-没有基金代码,不需要查询确认交易");
            return latestAckDealDtlMap;
        }
        List<AckDealOrderInfo> ackDealOrderInfoList = cmCusttradeDirectRepository.selectAckDealDtl(hboneNo, new ArrayList<>(fundCodes));
        if (CollectionUtils.isEmpty(ackDealOrderInfoList)) {
            log.info("getLatestDirectAckDealDtlMap-根据基金代码,查询不到确认交易,hboneNo={},fundCodes={}", hboneNo, JSON.toJSON(fundCodes));
            return latestAckDealDtlMap;
        }
        List<AckDealOrderInfo> subList = null;
        for (AckDealOrderInfo vo : ackDealOrderInfoList) {
            String fundCode = vo.getFundCode();
            if (latestAckDealDtlMap.containsKey(fundCode)) {
                latestAckDealDtlMap.get(fundCode).add(vo);
            } else {
                subList = new ArrayList<>();
                subList.add(vo);
                latestAckDealDtlMap.put(fundCode, subList);
            }
        }
        log.info("getLatestDirectAckDealDtlMap-所有确认的直销交易记录：{}", JSON.toJSONString(latestAckDealDtlMap));
        return latestAckDealDtlMap;
    }

    @Override
    public List<String> getCrisisFundList() {
        String key = CacheKeyPrefix.HIGH_CRISIS_FUND;
        List<String> crisisFundList = CACHE_SERVICE.get(key);
        if (CollectionUtils.isEmpty(crisisFundList)) {
            crisisFundList = queryHighProductOuterService.queryAllCrisisFundCodes();
            CACHE_SERVICE.put(CacheKeyPrefix.HIGH_CRISIS_FUND, crisisFundList);
        } else {
            log.info("清盘产品缓存：{}", JSON.toJSONString(crisisFundList));
        }
        return crisisFundList;
    }

    @Override
    public void setAgentMarketValueAndNavInfo(QueryAcctBalanceResponse.BalanceBean balanceBean, HighProductNavBean navBean, List<AckDealOrderInfo> ackDealOrderInfoList) {
        BigDecimal nav = null;
        String navDate = null;
        if (navBean == null || navBean.getNav() == null) {
            // 特殊产品指标控制需求：20221122
            // 产品范围：固定收益类产品，即【产品大类productSubType】=2-固定收益
            // （2）若基金的【净值披露方式】=2-份额收益：【持仓总市值】=【持仓份额累计】* 1；
            if (ProductDBTypeEnum.GUDINGSHOUYI.getCode().equals(balanceBean.getProductSubType())
                    && NavDisclosureTypeEnum.FESY.getCode().equals(balanceBean.getNavDisclosureType())) {
                nav = BigDecimal.ONE;
            } else {
                return;
            }
        } else {
            // 特殊产品指标控制需求：20221122
            // 产品范围：固定收益类产品，即【产品大类productSubType】=2-固定收益
            // （2）若基金的【净值披露方式】=2-份额收益：【持仓总市值】=【持仓份额累计】* 1；
            if (ProductDBTypeEnum.GUDINGSHOUYI.getCode().equals(balanceBean.getProductSubType())
                    && NavDisclosureTypeEnum.FESY.getCode().equals(balanceBean.getNavDisclosureType())) {
                nav = BigDecimal.ONE;
            } else {
                nav = navBean.getNav();
                navDate = navBean.getNavDate();
            }
        }
        // 未查到最新确认订单or净值日期为空
        if (CollectionUtils.isEmpty(ackDealOrderInfoList) || StringUtils.isEmpty(navDate)) {
            balanceBean.setNav(MoneyUtil.formatMoney(nav, 4));
            balanceBean.setNavDt(navDate);
            balanceBean.setMarketValue(MoneyUtil.formatMoney(balanceBean.getBalanceVol().multiply(nav), 2));
            balanceBean.setCurrencyMarketValue(MoneyUtil.formatMoney(balanceBean.getBalanceVol().multiply(nav), 2));
        } else {
            BigDecimal ackNetAmt = new BigDecimal("0.00");
            BigDecimal ackVol = new BigDecimal("0.00");
            for (AckDealOrderInfo vo : ackDealOrderInfoList) {
                // 净值日期小于等于上报日期，市值=一笔或多笔交易的确认金额（不含费）+{持有份额-（一笔或多笔交易合计确认份额）}*DB最新净值
                if (navDate.compareTo(vo.getSubmitDt()) <= 0) {
                    ackNetAmt = ackNetAmt.add(vo.getAckAmt().subtract(vo.getFee()));
                    ackVol = ackVol.add(vo.getAckVol());
                }
            }
            // 市值=一笔或多笔交易的确认金额（不含费）+{持有份额-（一笔或多笔交易合计确认份额）}*DB最新净值
            BigDecimal marketValue = ackNetAmt.add(balanceBean.getBalanceVol().subtract(ackVol).multiply(nav));
            balanceBean.setNav(MoneyUtil.formatMoney(nav, 4));
            balanceBean.setNavDt(navDate);
            balanceBean.setMarketValue(MoneyUtil.formatMoney(marketValue, 2));
            balanceBean.setCurrencyMarketValue(MoneyUtil.formatMoney(marketValue, 2));
        }
    }

    @Override
    public void setDirectMarketValueAndNavInfo(String hbOneNo, QueryAcctBalanceResponse.BalanceBean balanceBean,
                                               BigDecimal rmbZJJ, List<AckDealOrderInfo> ackDealOrderInfoList,
                                               JjxswConfigModel config, HighProductNavBean navBean) {
        BigDecimal nav = null;
        String navDate = null;
        if (navBean == null || navBean.getNav() == null) {
            // 特殊产品指标控制需求：20221122
            // 产品范围：固定收益类产品，即【产品大类productSubType】=2-固定收益
            // （2）若基金的【净值披露方式】=2-份额收益：【持仓总市值】=【持仓份额累计】* 1；
            if (ProductDBTypeEnum.GUDINGSHOUYI.getCode().equals(balanceBean.getProductSubType())
                    && NavDisclosureTypeEnum.FESY.getCode().equals(balanceBean.getNavDisclosureType())) {
                nav = BigDecimal.ONE;
            } else {
                return;
            }
        } else {
            // 特殊产品指标控制需求：20221122
            // 产品范围：固定收益类产品，即【产品大类productSubType】=2-固定收益
            // （2）若基金的【净值披露方式】=2-份额收益：【持仓总市值】=【持仓份额累计】* 1；
            if (ProductDBTypeEnum.GUDINGSHOUYI.getCode().equals(balanceBean.getProductSubType())
                    && NavDisclosureTypeEnum.FESY.getCode().equals(balanceBean.getNavDisclosureType())) {
                nav = BigDecimal.ONE;
            } else {
                nav = navBean.getNav();
                navDate = navBean.getNavDate();
            }
        }
        BigDecimal ackNetAmt = new BigDecimal("0.00");
        BigDecimal ackVol = new BigDecimal("0.00");
        BigDecimal ackBalanceFactor = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(ackDealOrderInfoList)) {
            for (AckDealOrderInfo vo : ackDealOrderInfoList) {
                String submitTaDt = vo.getAckDt();
                // 直销-海外产品(即是否香港专区HKSaleFlag=是,优先取净值日期,在次取新交易日期作为交易日期
                if (YesOrNoEnum.YES.getCode().equals(vo.getIsHkProduct())) {
                    if (StringUtils.isNotBlank(vo.getAckNavDt())) {
                        submitTaDt = vo.getAckNavDt();
                    } else if (StringUtils.isNotBlank(vo.getSubmitDt())) {
                        submitTaDt = vo.getSubmitDt();
                    }
                }

                if (navDate != null && navDate.compareTo(submitTaDt) <= 0) {
                    ackNetAmt = ackNetAmt.add(vo.getAckAmt().subtract(vo.getFee()));
                    ackVol = ackVol.add(vo.getAckVol());
                    // 只有正数的平衡因子才计算入
                    if (vo.getBalanceFactor() != null && vo.getBalanceFactor().compareTo(BigDecimal.ZERO) > 0) {
                        ackBalanceFactor = ackBalanceFactor.add(vo.getBalanceFactor());
                    }
                }
            }
        }
        log.info("ackNetAmt:{}, ackVol:{}", ackNetAmt, ackVol);
        // 净值日期小于上报日期， 一笔或多笔订单的确认金额(不含费)① + {持有份额 - (一笔或多笔交易合计确认份额) } * DB最新净值 + 最新净值日期对应的平衡因子② - 一笔或多笔订单的正平衡因子
        balanceBean.setNav(formatMoney(nav, 4, config.getJzws()));
        balanceBean.setNavDt(navDate);
        // 平衡因子
        processBalanceFactor(balanceBean, hbOneNo);
        BigDecimal marketValue;
        if (YesOrNoEnum.NO.getCode().equals(balanceBean.getConvertFinish()) && balanceBean.getBalanceFactor() != null) {
            marketValue = balanceBean.getBalanceVol().subtract(ackVol).multiply(nav).add(balanceBean.getBalanceFactor()).add(ackNetAmt).subtract(ackBalanceFactor);
        } else {
            marketValue = balanceBean.getBalanceVol().subtract(ackVol).multiply(nav).add(ackNetAmt);
        }
        balanceBean.setCurrencyMarketValue(formatMoney(marketValue, 2, config.getSzws()));
        if (CurrencyEnum.RMB.getCode().equals(balanceBean.getCurrency())) {
            balanceBean.setMarketValue(formatMoney(marketValue, 2, config.getSzws()));
        } else if (rmbZJJ != null) {
            // 外币市值处理
            balanceBean.setMarketValue(formatMoney(marketValue.multiply(rmbZJJ), 2, config.getSzws()));
        }


    }

    @Override
    public void processNavDivFlag(QueryAcctBalanceResponse.BalanceBean balanceBean, HighProductNavBean navBean,
                                  String txAcctNo, String hboneNo, Map<String, HighProductNavDivBean> fundNavDivMap) {

        if (navBean == null) {
            return;
        }
        if (StringUtils.isNotBlank(balanceBean.getDisCode()) && DisCodeEnum.HM.getCode().equals(balanceBean.getDisCode())) {
            // 仅 代销 的 现金管理、债券、对冲策略、股票策略、房地产基金、其他 支持分红提醒
            if (ScaleTypeEnum.CONSIGNMENT.getCode().equals(balanceBean.getScaleType())
                    && StringUtils.equalsAny(balanceBean.getProductSubType(), ProductDBTypeEnum.CASH.getCode(),
                    ProductDBTypeEnum.ZHAIQUAN.getCode(),
                    ProductDBTypeEnum.DUICHONG.getCode(),
                    ProductDBTypeEnum.GUPIAO.getCode(),
                    ProductDBTypeEnum.FDCFUND.getCode(),
                    ProductDBTypeEnum.OTHER.getCode(),
                    ProductDBTypeEnum.GUDINGSHOUYI.getCode())) {
                HighProductNavDivBean highProductNavDivBean = fundNavDivMap.get(balanceBean.getProductCode());
                if (highProductNavDivBean != null && (StringUtils.isNotBlank(highProductNavDivBean.getNavDivDt()) || StringUtils.isNotBlank(highProductNavDivBean.getNavDivRegDt()))) {
                    String usedDt = StringUtils.isBlank(highProductNavDivBean.getNavDivRegDt()) ? highProductNavDivBean.getNavDivDt() : highProductNavDivBean.getNavDivRegDt();
                    String balanceDt = getBalanceDt(usedDt);
                    // 查询基金产品权益登记日期有没有持仓
                    String isHold = queryAssetService.queryHisHoldInfo(hboneNo, balanceBean.getProductCode(), balanceDt);
                    if (com.howbuy.interlayer.product.enums.YesOrNoEnum.YES.getCode().equals(isHold)) {
                        if (HighDbNavDivTypeEnum.IMMEDIATELY_DIV.getCode().equals(highProductNavDivBean.getNavDivType())) {
                            // 即将分红
                            balanceBean.setNavDivFlag(HighNavDivTypeEnum.IMMEDIATELY_DIV.getCode());
                        } else if (HighDbNavDivTypeEnum.COMPLETE_DIV.getCode().equals(highProductNavDivBean.getNavDivType())) {
                            // 已更新分红净值
                            // 无分红记录,或者该分红交易记录创建日期大于等于当前日期,即标识该笔分红当天到账,也许展示 提醒收益偏差
                            List<HighDealOrderDtlPo> highDealOrderDtlPoList = highDealOrderDtlRepository.queryDivOrderNumAfterDt(txAcctNo, balanceBean.getProductCode(), usedDt);
                            Date lastDivCreateDtm = getLastDivCreateDtm(highDealOrderDtlPoList);
                            if (org.apache.commons.collections.CollectionUtils.isEmpty(highDealOrderDtlPoList) || (lastDivCreateDtm != null && !lastDivCreateDtm.before(DateUtil.getBegin(new Date())))) {
                                balanceBean.setNavDivFlag(HighNavDivTypeEnum.DEVIATION_DIV.getCode());
                            } else {
                                // 若存在交易确认时间大于等于分红日期的分红交易记录，即该笔分红已到账，则无需展示分红提醒
                                balanceBean.setNavDivFlag(HighNavDivTypeEnum.NO_NEED_DIV.getCode());
                            }
                        } else if (HighDbNavDivTypeEnum.NOT_COMPLETE_DIV.getCode().equals(highProductNavDivBean.getNavDivType())) {
                            // 未更新分红净值
                            balanceBean.setNavDivFlag(HighNavDivTypeEnum.DEVIATION_DIV.getCode());
                        }

                    }

                } else {
                    log.info("db 查询不到该基金{}，分红信息", balanceBean.getProductCode());
                }
            }

        }
    }

    @Override
    public void setAgentBalanceAssetInfo(QueryAcctBalanceResponse.BalanceBean balanceBean, HighFundAssetIncomeDomain currentAssetDto) {
        if (currentAssetDto != null) {
            if (StandardFixedIncomeFlagEnum.BOND_GS.getCode().equals(balanceBean.getStandardFixedIncomeFlag())) {
                balanceBean.setIncomeDt(currentAssetDto.getIncomeDt());
            } else {
                balanceBean.setCurrentAssetCurrency(BigUtil.formatMoney(currentAssetDto.getCurrentAsset(), 2));
                balanceBean.setCurrentAsset(BigUtil.formatMoney(currentAssetDto.getCurrentAssetRmb(), 2));
                balanceBean.setDailyAssetCurrency(BigUtil.formatMoney(currentAssetDto.getDailyAsset(), 2));
                balanceBean.setDailyAsset(BigUtil.formatMoney(currentAssetDto.getDailyAssetRmb(), 2));
                balanceBean.setIncomeDt(currentAssetDto.getIncomeDt());
                balanceBean.setAccumRealizedIncome(MoneyUtil.formatMoney(currentAssetDto.getAccumRealizedIncome(), 2));
                balanceBean.setAccumRealizedIncomeRmb(MoneyUtil.formatMoney(currentAssetDto.getAccumRealizedIncomeRmb(), 2));
                balanceBean.setAccumIncome(BigUtil.formatMoney(currentAssetDto.getAccumIncome(), 2));
                balanceBean.setAccumIncomeRmb(BigUtil.formatMoney(currentAssetDto.getAccumIncomeRmb(), 2));
            }
            if (ProductDBTypeEnum.GUQUAN.getCode().equals(balanceBean.getProductSubType())) {
                // 股权已回款金额取资产中心
                balanceBean.setCashCollection(MoneyUtil.formatMoney(currentAssetDto.getAccumCollection(), 2));
                balanceBean.setCurrencyCashCollection(MoneyUtil.formatMoney(currentAssetDto.getAccumCollection(), 2));
                balanceBean.setUnitBalanceCostExFeeRmb(MoneyUtil.formatMoney(currentAssetDto.getUnitBalanceCostExFeeRmb(), 2));
            }
            balanceBean.setBalanceCost(MoneyUtil.formatMoney(currentAssetDto.getBalanceCostRmb(), 2));
            balanceBean.setBalanceCostCurrency(MoneyUtil.formatMoney(currentAssetDto.getBalanceCost(), 2));
            balanceBean.setBalanceIncomeNew(MoneyUtil.formatMoney(currentAssetDto.getBalanceIncomeNew(), 2));
            balanceBean.setBalanceIncomeNewRmb(MoneyUtil.formatMoney(currentAssetDto.getBalanceIncomeNewRmb(), 2));
            balanceBean.setAccumIncomeNew(MoneyUtil.formatMoney(currentAssetDto.getAccumIncomeNew(), 2));
            balanceBean.setAccumIncomeNewRmb(MoneyUtil.formatMoney(currentAssetDto.getAccumIncomeNewRmb(), 2));

            balanceBean.setAccumCost(MoneyUtil.formatMoney(currentAssetDto.getAccumCost(), 2));
            balanceBean.setAccumCostRmb(MoneyUtil.formatMoney(currentAssetDto.getAccumCostRmb(), 2));
            balanceBean.setBalanceFloatIncome(MoneyUtil.formatMoney(currentAssetDto.getBalanceFloatIncome(), 2));
            balanceBean.setBalanceFloatIncomeRmb(MoneyUtil.formatMoney(currentAssetDto.getBalanceFloatIncomeRmb(), 2));

            balanceBean.setAccumCollection(MoneyUtil.formatMoney(currentAssetDto.getAccumCollection(), 2));
            balanceBean.setAccumCollectionRmb(MoneyUtil.formatMoney(currentAssetDto.getAccumCollectionRmb(), 2));
            balanceBean.setBalanceAmt(MoneyUtil.formatMoney(currentAssetDto.getBalanceAmt(), 2));
            balanceBean.setBalanceAmtRmb(MoneyUtil.formatMoney(currentAssetDto.getBalanceAmtRmb(), 2));
            balanceBean.setAccumCostNew(MoneyUtil.formatMoney(currentAssetDto.getAccumCostNew(), 2));
            balanceBean.setAccumCostRmbNew(MoneyUtil.formatMoney(currentAssetDto.getAccumCostRmbNew(), 2));
            balanceBean.setBalanceAmtExFee(MoneyUtil.formatMoney(currentAssetDto.getBalanceAmtExFee(), 2));
            balanceBean.setBalanceAmtExFeeRmb(MoneyUtil.formatMoney(currentAssetDto.getBalanceAmtExFeeRmb(), 2));
            // 单位持仓成本去费
            balanceBean.setAssetUpdateDate(currentAssetDto.getUpdateDate());
            balanceBean.setUnitBalanceCostExFee(MoneyUtil.formatMoney(currentAssetDto.getUnitBalanceCostExFee(), 2));
            balanceBean.setUnitBalanceCostExFeeRmb(MoneyUtil.formatMoney(currentAssetDto.getUnitBalanceCostExFeeRmb(), 2));
            balanceBean.setBalanceFloatIncomeRate(BigUtil.formatMoneyRate(currentAssetDto.getBalanceFloatIncomeRate(), 4));
            balanceBean.setDayAssetRate(BigUtil.formatMoneyRate(currentAssetDto.getDayAssetRate(), 4));
            balanceBean.setDayIncomeGrowthRate(BigUtil.formatMoneyRate(currentAssetDto.getDayIncomeGrowthRate(), 4));
            balanceBean.setYieldRate(BigUtil.formatMoney(currentAssetDto.getCurrentRate(), 4)); //设置收益率
            balanceBean.setAccumYieldRate(BigUtil.formatMoney(currentAssetDto.getAccumIncomeRate(), 4)); //累计收益率
            balanceBean.setAssetUpdateDate(currentAssetDto.getUpdateDate());
            // NA产品费用处理
            if (NaProductFeeTypeEnum.HOWBUY_FEE.getCode().equals(balanceBean.getNaProductFeeType())) {
                balanceBean.setReceivManageFee(currentAssetDto.getReceivManageFee());
                balanceBean.setReceivPreformFee(currentAssetDto.getReceivPreformFee());
                // 当前币种NA费用
                BigDecimal naFee = MathUtils.add(currentAssetDto.getReceivManageFee(), currentAssetDto.getReceivPreformFee());
                balanceBean.setCurrencyMarketValueExFee(MathUtils.subtract(balanceBean.getCurrencyMarketValue(), naFee));
                balanceBean.setMarketValueExFee(MathUtils.subtract(balanceBean.getMarketValue(), naFee));
            }

        }
    }

    @Override
    public void setDirectBalanceAssetInfo(QueryAcctBalanceResponse.BalanceBean balanceBean, BigDecimal rmbZJJ, JjxswConfigModel config, HighFundAssetIncomeDomain currentAssetDto) {
        if (currentAssetDto == null) {
            log.info("setDirectBalanceAssetInfo,产品没有收益信息,fundCode={},fundSubCode={}", balanceBean.getProductCode(), balanceBean.getSubProductCode());
            return;
        }
        // NA产品费用处理
        if (NaProductFeeTypeEnum.HOWBUY_FEE.getCode().equals(balanceBean.getNaProductFeeType())) {
            balanceBean.setReceivManageFee(currentAssetDto.getReceivManageFee());
            balanceBean.setReceivPreformFee(currentAssetDto.getReceivPreformFee());
            // 当前币种NA费用
            BigDecimal naFee = MathUtils.add(currentAssetDto.getReceivManageFee(), currentAssetDto.getReceivPreformFee());
            balanceBean.setCurrencyMarketValueExFee(formatMoney(MathUtils.subtract(balanceBean.getCurrencyMarketValue(), naFee), 2, config.getSzws()));
            if (CurrencyEnum.RMB.getCode().equals(balanceBean.getCurrency())) {
                balanceBean.setMarketValueExFee(MathUtils.subtract(balanceBean.getMarketValue(), naFee));
            } else if (rmbZJJ != null) {
                balanceBean.setMarketValueExFee(MathUtils.subtract(balanceBean.getMarketValue(), MathUtils.multiply(naFee, rmbZJJ)));
            }
        }
        if (StandardFixedIncomeFlagEnum.BOND_GS.getCode().equals(balanceBean.getStandardFixedIncomeFlag())) {
            balanceBean.setIncomeDt(currentAssetDto.getIncomeDt());
        } else {
            balanceBean.setDailyAssetCurrency(bigFormatMoney(currentAssetDto.getDailyAsset(), 2, config.getSyws()));
            balanceBean.setDailyAsset(bigFormatMoney(currentAssetDto.getDailyAssetRmb(), 2, config.getSyws()));
            balanceBean.setCurrentAssetCurrency(bigFormatMoney(currentAssetDto.getCurrentAsset(), 2, config.getSyws()));
            balanceBean.setCurrentAsset(bigFormatMoney(currentAssetDto.getCurrentAssetRmb(), 2, config.getSyws()));
            balanceBean.setAccumRealizedIncome(formatMoney(currentAssetDto.getAccumRealizedIncome(), 2, config.getSyws()));
            balanceBean.setAccumRealizedIncomeRmb(formatMoney(currentAssetDto.getAccumRealizedIncomeRmb(), 2, config.getSyws()));
            balanceBean.setIncomeDt(currentAssetDto.getIncomeDt());
            balanceBean.setAccumIncome(bigFormatMoney(currentAssetDto.getAccumIncome(), 2, config.getSyws()));
            balanceBean.setAccumIncomeRmb(bigFormatMoney(currentAssetDto.getAccumIncomeRmb(), 2, config.getSyws()));
            balanceBean.setDayAssetRate(formatMoneyRate(currentAssetDto.getDayAssetRate(), 4, config.getSyws()));
        }
        // 股权产品持仓成本
        if (ProductDBTypeEnum.GUQUAN.getCode().equals(balanceBean.getProductSubType())) {
            balanceBean.setAccumIncomeNew(MoneyUtil.formatMoney(currentAssetDto.getAccumIncomeNew(), 2));
            balanceBean.setAccumIncomeNewRmb(MoneyUtil.formatMoney(currentAssetDto.getAccumIncomeNewRmb(), 2));
            balanceBean.setBalanceIncomeNew(MoneyUtil.formatMoney(currentAssetDto.getBalanceIncomeNew(), 2));
            balanceBean.setBalanceIncomeNewRmb(MoneyUtil.formatMoney(currentAssetDto.getBalanceIncomeNewRmb(), 2));
            balanceBean.setAccumCost(MoneyUtil.formatMoney(currentAssetDto.getAccumCost(), 2));
            balanceBean.setAccumCostNew(MoneyUtil.formatMoney(currentAssetDto.getAccumCostNew(), 2));
            balanceBean.setAccumCollection(MoneyUtil.formatMoney(currentAssetDto.getAccumCollection(), 2));
            balanceBean.setAccumCollectionRmb(MoneyUtil.formatMoney(currentAssetDto.getAccumCollectionRmb(), 2));
            balanceBean.setAccumCostRmb(MoneyUtil.formatMoney(currentAssetDto.getAccumCostRmb(), 2));
            balanceBean.setAccumCostRmbNew(MoneyUtil.formatMoney(currentAssetDto.getAccumCostRmbNew(), 2));
            // 股权已回款金额取资产中心
            balanceBean.setCashCollection(MoneyUtil.formatMoney(currentAssetDto.getAccumCollection(), 2));
            balanceBean.setCurrencyCashCollection(MoneyUtil.formatMoney(currentAssetDto.getAccumCollection(), 2));
            balanceBean.setAssetUpdateDate(currentAssetDto.getUpdateDate());
            balanceBean.setUnitBalanceCostExFee(MoneyUtil.formatMoney(currentAssetDto.getUnitBalanceCostExFee(), 2));
            balanceBean.setUnitBalanceCostExFeeRmb(MoneyUtil.formatMoney(currentAssetDto.getUnitBalanceCostExFee(), 2));
        }
        balanceBean.setBalanceCost(MoneyUtil.formatMoney(currentAssetDto.getBalanceCostRmb(), 2));
        balanceBean.setBalanceCostCurrency(MoneyUtil.formatMoney(currentAssetDto.getBalanceCost(), 2));
        balanceBean.setBalanceIncomeNew(formatMoney(currentAssetDto.getBalanceIncomeNew(), 2, config.getSyws()));
        balanceBean.setBalanceIncomeNewRmb(formatMoney(currentAssetDto.getBalanceIncomeNewRmb(), 2, config.getSyws()));
        balanceBean.setAccumIncomeNew(formatMoney(currentAssetDto.getAccumIncomeNew(), 2, config.getSyws()));
        balanceBean.setAccumIncomeNewRmb(formatMoney(currentAssetDto.getAccumIncomeNewRmb(), 2, config.getSyws()));
        balanceBean.setDayAssetRate(formatMoneyRate(currentAssetDto.getDayAssetRate(), 4, config.getSyws()));

        balanceBean.setAccumCost(formatMoney(currentAssetDto.getAccumCost(), 2, null));
        balanceBean.setAccumCostRmb(formatMoney(currentAssetDto.getAccumCostRmb(), 2, null));
        balanceBean.setBalanceFloatIncome(formatMoney(currentAssetDto.getBalanceFloatIncome(), 2, null));
        balanceBean.setBalanceFloatIncomeRmb(formatMoney(currentAssetDto.getBalanceFloatIncomeRmb(), 2, null));

        balanceBean.setAccumCollection(formatMoney(currentAssetDto.getAccumCollection(), 2, null));
        balanceBean.setAccumCollectionRmb(formatMoney(currentAssetDto.getAccumCollectionRmb(), 2, null));
        balanceBean.setBalanceAmt(formatMoney(currentAssetDto.getBalanceAmt(), 2, null));
        balanceBean.setBalanceAmtRmb(formatMoney(currentAssetDto.getBalanceAmtRmb(), 2, null));
        balanceBean.setAccumCostNew(formatMoney(currentAssetDto.getAccumCostNew(), 2, null));
        balanceBean.setAccumCostRmbNew(formatMoney(currentAssetDto.getAccumCostRmbNew(), 2, null));
        balanceBean.setBalanceAmtExFee(formatMoney(currentAssetDto.getBalanceAmtExFee(), 2, null));
        balanceBean.setBalanceAmtExFeeRmb(formatMoney(currentAssetDto.getBalanceAmtExFeeRmb(), 2, null));

        balanceBean.setBalanceFloatIncomeRate(BigUtil.formatMoneyRate(currentAssetDto.getBalanceFloatIncomeRate(), 4));
        balanceBean.setDayIncomeGrowthRate(BigUtil.formatMoneyRate(currentAssetDto.getDayIncomeGrowthRate(), 4));
        balanceBean.setYieldRate(BigUtil.formatYieldRate(currentAssetDto.getCurrentRate(), config.getSylws())); //设置收益率
        balanceBean.setAccumYieldRate(BigUtil.formatYieldRate(currentAssetDto.getAccumIncomeRate(), config.getSylws()));

        balanceBean.setAssetUpdateDate(currentAssetDto.getUpdateDate());
        balanceBean.setUnitBalanceCostExFee(MoneyUtil.formatMoney(currentAssetDto.getUnitBalanceCostExFee(), 2));
        balanceBean.setUnitBalanceCostExFeeRmb(MoneyUtil.formatMoney(currentAssetDto.getUnitBalanceCostExFeeRmb(), 2));
    }

    @Override
    public void setYieldIncomeInfo(QueryAcctBalanceResponse.BalanceBean balanceBean) {
        // 现金管理类产品查询七日年化和收益日期
        if (StandardFixedIncomeFlagEnum.CASH_MANAGER.getCode().equals(balanceBean.getStandardFixedIncomeFlag())
                && NavDisclosureTypeEnum.JZ.getCode().equals(balanceBean.getNavDisclosureType())) {
            // 查询私募库数据st_hedge.t_st_hbjz，（产品同事确认，生产应该已无该类型产品，保持原逻辑未改变 20221122）
            HighProductDBYieldBean yieldBean = queryHighProductOuterService.getHighProductYield(balanceBean.getProductCode());
            if (yieldBean == null) {
                log.error("QueryAcctBalanceFacadeService|processNavAndAssetInfo|getHighProductYield is null,productCode:{}", balanceBean.getProductCode());
                return;
            }
            balanceBean.setYieldIncome(yieldBean.getYieldIncome());
            balanceBean.setYieldIncomeDt(yieldBean.getNavDate());
        }
        if (NavDisclosureTypeEnum.FESY.getCode().equals(balanceBean.getNavDisclosureType())) {
            // 查询固收库数据st_fixed.t_st_hbjz
            HighProductDBYieldBean yieldBean = queryHighProductOuterService.getSimuHbjjNewestSyByJjdms(balanceBean.getProductCode());
            if (yieldBean != null) {
                balanceBean.setYieldIncome(yieldBean.getYieldIncome());
                balanceBean.setYieldIncomeDt(yieldBean.getNavDate());
                // 特殊产品指标控制需求：万份收益 20221122
                balanceBean.setCopiesIncome(yieldBean.getWfsyIncome());
            } else {
                log.info("productCode:{},七日年化万分收益查询结果为空", balanceBean.getProductCode());
            }

        }

    }

    @Override
    public String getIncomeCalStatus(QueryAcctBalanceResponse.BalanceBean balanceBean, List<String> crisisFundList) {
        String incomeCalStatus = null;
        String productDBType = balanceBean.getProductSubType();
        if (crisisFundList.contains(balanceBean.getProductCode())
                || ProductDBTypeEnum.GUQUAN.getCode().equals(productDBType)
                || (ProductDBTypeEnum.GUDINGSHOUYI.getCode().equals(productDBType)
                && !StandardFixedIncomeFlagEnum.BROKERAGE_COLLECTION_GS.getCode().equals(balanceBean.getStandardFixedIncomeFlag()))
                || (ProductDBTypeEnum.GUDINGSHOUYI.getCode().equals(productDBType)
                && StandardFixedIncomeFlagEnum.BROKERAGE_COLLECTION_GS.getCode().equals(balanceBean.getStandardFixedIncomeFlag())
                && NavDisclosureTypeEnum.FESY.getCode().equals(balanceBean.getNavDisclosureType()))
                || StringUtils.isEmpty(balanceBean.getIncomeDt())) {
            // 股权类,固收默认是计算完成; 危机产品默认收益计算状态为完成, 不影响客户总收益计算状态; 资产中心未返回某产品的收益时
            incomeCalStatus = IncomeCalStatEnum.FINISHED.getCode();
        } else if (StringUtils.isNotEmpty(balanceBean.getNavDt())
                && StringUtils.isNotEmpty(balanceBean.getIncomeDt())
                && balanceBean.getNavDt().compareTo(balanceBean.getIncomeDt()) <= 0) {
            incomeCalStatus = IncomeCalStatEnum.FINISHED.getCode();
        } else {
            incomeCalStatus = IncomeCalStatEnum.PROCESSING.getCode();
        }
        log.info("getIncomeCalStatus-获取当前收益计算状态结果:{}", incomeCalStatus);
        return incomeCalStatus;
    }

    @Override
    public void setAssertWithOutCrisis(QueryAcctBalanceResponse.BalanceBean balanceBean, HighFundAssetIncomeDomain currentAssetDto) {
        setGuQuanAssertInfoWithOutCrisis(balanceBean, currentAssetDto);
        setGuShouAssetInfoWithOutCrisis(balanceBean, currentAssetDto);
    }

    @Override
    public void setGuQuanAssertInfoWithOutCrisis(QueryAcctBalanceResponse.BalanceBean balanceBean, HighFundAssetIncomeDomain currentAssetDto) {
        if (ProductDBTypeEnum.GUQUAN.getCode().equals(balanceBean.getProductSubType())) {
            if (currentAssetDto != null) {
                balanceBean.setAccumIncomeNew(MoneyUtil.formatMoney(currentAssetDto.getAccumIncomeNew(), 2));
                balanceBean.setAccumIncomeNewRmb(MoneyUtil.formatMoney(currentAssetDto.getAccumIncomeNewRmb(), 2));
                balanceBean.setBalanceIncomeNew(BigUtil.formatMoney(currentAssetDto.getBalanceIncomeNew(), 2));
                balanceBean.setBalanceIncomeNewRmb(BigUtil.formatMoney(currentAssetDto.getBalanceIncomeNewRmb(), 2));
                balanceBean.setAccumCost(MoneyUtil.formatMoney(currentAssetDto.getAccumCost(), 2));
                balanceBean.setAccumCostNew(MoneyUtil.formatMoney(currentAssetDto.getAccumCostNew(), 2));
                balanceBean.setAccumCollection(MoneyUtil.formatMoney(currentAssetDto.getAccumCollection(), 2));
                balanceBean.setAccumCollectionRmb(MoneyUtil.formatMoney(currentAssetDto.getAccumCollectionRmb(), 2));
                balanceBean.setAccumCostRmb(MoneyUtil.formatMoney(currentAssetDto.getAccumCostRmb(), 2));
                balanceBean.setAccumCostRmbNew(MoneyUtil.formatMoney(currentAssetDto.getAccumCostRmbNew(), 2));
                // 股权已回款金额取资产中心
                balanceBean.setCashCollection(MoneyUtil.formatMoney(currentAssetDto.getAccumCollection(), 2));
                balanceBean.setCurrencyCashCollection(MoneyUtil.formatMoney(currentAssetDto.getAccumCollection(), 2));
                balanceBean.setAssetUpdateDate(currentAssetDto.getUpdateDate());
                balanceBean.setUnitBalanceCostExFee(MoneyUtil.formatMoney(currentAssetDto.getUnitBalanceCostExFee(), 2));
                balanceBean.setUnitBalanceCostExFeeRmb(MoneyUtil.formatMoney(currentAssetDto.getUnitBalanceCostExFeeRmb(), 2));
            }
        }
    }

    @Override
    public void setGuShouAssetInfoWithOutCrisis(QueryAcctBalanceResponse.BalanceBean balanceBean, HighFundAssetIncomeDomain currentAssetDto) {
        if (ProductDBTypeEnum.GUDINGSHOUYI.getCode().equals(balanceBean.getProductSubType())) {
            List<String> standardFixedIncomeFlagList = new ArrayList<>();
            standardFixedIncomeFlagList.add(StandardFixedIncomeFlagEnum.STANDARD_GS.getCode());
            standardFixedIncomeFlagList.add(StandardFixedIncomeFlagEnum.NOT_STANDARD_GS.getCode());
            standardFixedIncomeFlagList.add(StandardFixedIncomeFlagEnum.BOND_GS.getCode());
            if (balanceBean.getStandardFixedIncomeFlag() != null && standardFixedIncomeFlagList.contains(balanceBean.getStandardFixedIncomeFlag())) {
                if (currentAssetDto != null) {
                    // 标准固收,非标固收,纯债固收,需要显示回款信息
                    balanceBean.setCashCollection(MoneyUtil.formatMoney(currentAssetDto.getAccumCollection(), 2));
                    balanceBean.setCurrencyCashCollection(MoneyUtil.formatMoney(currentAssetDto.getAccumCollection(), 2));
                    balanceBean.setAccumCollection(MoneyUtil.formatMoney(currentAssetDto.getAccumCollection(), 2));
                    balanceBean.setAccumCollectionRmb(MoneyUtil.formatMoney(currentAssetDto.getAccumCollectionRmb(), 2));
                }
            }
        }
    }

    @Override
    public void setQxFundInfo(Map<String, BigDecimal> netBuyAmtMap, Map<String, BigDecimal> paidInAmtMap, List<String> qianXiProducts, QueryAcctBalanceResponse.BalanceBean balanceBean, BigDecimal rmbZJJ) {
        log.info("判断是否为千禧年产品,qianXiProducts={},productCode={}", qianXiProducts, balanceBean.getProductCode());
        if (!ScaleTypeEnum.DIRECT.getCode().equals(balanceBean.getScaleType())) {
            log.info("不是直销的,不需要判断是否千禧年,因为代销的没有千禧年");
            return;
        }
        // 千禧年产品，需获取【总实缴金额】【待投金额】
        if (CollectionUtil.isNotEmpty(qianXiProducts) && qianXiProducts.contains(balanceBean.getProductCode())) {
            // 千禧年产品标识
            balanceBean.setQianXiFlag(YesOrNoEnum.YES.getCode());
            // 已投金额
            BigDecimal netBuyAmt = netBuyAmtMap.get(balanceBean.getProductCode());
            if (netBuyAmt == null) {
                netBuyAmt = new BigDecimal("0.00");
            }
            // 总实缴金额/认缴金额 币种转换
            BigDecimal paidInAmt = paidInAmtMap.get(balanceBean.getProductCode());
            if (paidInAmt == null) {
                // 总实缴金额获取为空时，返回空值
                return;
            }
            paidInAmt = paidInAmt.add(new BigDecimal("0.00"));
            balanceBean.setPaidInAmt(MoneyUtil.formatMoney(paidInAmt, 2));
            // 待投金额(当前币种)
            BigDecimal currencyUnPaidInAmt = MoneyUtil.formatMoney(paidInAmt.subtract(netBuyAmt), 2);
            if (currencyUnPaidInAmt.compareTo(BigDecimal.ZERO) < 0) {
                currencyUnPaidInAmt = new BigDecimal("0.00");
            }
            BigDecimal unpaidInAmt = new BigDecimal("0.00");
            if (CurrencyEnum.RMB.getCode().equals(balanceBean.getCurrency())) {
                unpaidInAmt = currencyUnPaidInAmt;
            } else if (rmbZJJ != null) {
                unpaidInAmt = MoneyUtil.formatMoney(currencyUnPaidInAmt.multiply(rmbZJJ), 2);
            }
            balanceBean.setCurrencyUnPaidInAmt(currencyUnPaidInAmt);
            balanceBean.setUnPaidInAmt(unpaidInAmt);
        }


    }

    @Override
    public void calFixedIncomeValueDate(QueryAcctBalanceResponse.BalanceBean balanceBean, Map<String, List<HighProductValueDateBean>> valueDateMap) {
        List<HighProductValueDateBean> fixedInconeValueDateList = valueDateMap.get(balanceBean.getProductCode());
        HighProductValueDateBean valueDateBean = new HighProductValueDateBean();
        if (!ONTHER_FIXEDINCODE_PRODUCT_SET.contains(balanceBean.getStandardFixedIncomeFlag())) {
            if (ProductTypeEnum.SM.getCode().equals(balanceBean.getProductType())) {
                valueDateBean = productValueDateCalService.getHighProductValueDate(balanceBean.getValueDate(), fixedInconeValueDateList);
            } else {
                if (!CollectionUtils.isEmpty(fixedInconeValueDateList)) {
                    valueDateBean = fixedInconeValueDateList.get(fixedInconeValueDateList.size() - 1);
                }
            }
            // 非标和标准固收才展示起息日到期日
            balanceBean.setValueDate(valueDateBean.getValueDate());
            balanceBean.setDueDate(valueDateBean.getDueDate());
        } else {
            if (!CollectionUtils.isEmpty(fixedInconeValueDateList)) {
                valueDateBean = fixedInconeValueDateList.get(fixedInconeValueDateList.size() - 1);
            }
        }
        balanceBean.setBenchmark(valueDateBean.getBenchmark());
        balanceBean.setRePurchaseFlag(valueDateBean.getRePurchaseFlag());
        balanceBean.setInvestmentHorizon(valueDateBean.getInvestmentHorizon());
        balanceBean.setCooperation(valueDateBean.getCooperation());
    }

    /**
     * 获取最后一笔分红交易记录创建时间
     *
     * @param highDealOrderDtlPoList 分红交易记录
     * @return 分红交易记录创建时间
     */
    public Date getLastDivCreateDtm(List<HighDealOrderDtlPo> highDealOrderDtlPoList) {
        if (CollectionUtils.isEmpty(highDealOrderDtlPoList)) {
            return null;
        }
        // 分红记录创建时间
        List<HighDealOrderDtlPo> sortedList = highDealOrderDtlPoList.stream().filter(x -> x.getCreateDtm() != null).sorted(Comparator.comparing(HighDealOrderDtlPo::getCreateDtm).reversed()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sortedList)) {
            return null;
        }
        Date createDtm = sortedList.get(0).getCreateDtm();
        return DateUtil.getBegin(createDtm);
    }

    /**
     * 当前时间与权益登记日期取小的那个日期
     *
     * @param usedDt 权益登记日期
     */
    private String getBalanceDt(String usedDt) {
        String currentDt = DateUtil.date2String(new Date(), DateUtil.SHORT_DATEPATTERN);
        if (StringUtils.isBlank(usedDt)) {
            return currentDt;
        }
        if (currentDt.compareTo(usedDt) <= 0) {
            return currentDt;
        }
        return usedDt;
    }

    /**
     * 处理平衡因子
     *
     * @param balanceBean 持仓信息
     * @param hbOneNo     一账通
     */
    private void processBalanceFactor(QueryAcctBalanceResponse.BalanceBean balanceBean, String hbOneNo) {
        // 香港+阳光私募的产品需要设置平衡因子
        if (YesOrNoEnum.YES.getCode().equals(balanceBean.getHkSaleFlag())) {
            if (!"2".equals(balanceBean.getProductSubType()) && !"5".equals(balanceBean.getProductSubType()) && balanceBean.getNavDt() != null) {
                QueryBalnaceFactotResult result = null;
                if (YesOrNoEnum.YES.getCode().equals(balanceBean.getStageEstablishFlag())) {
                    // 分期成立产品，通过子产品代码查询平衡因子
                    result = queryBalanceFactorDtmsService.queryBalanceFactorV2(hbOneNo, balanceBean.getSubProductCode(), balanceBean.getNavDt());
                } else {
                    // 非分期成立产品，通过母产品代码查询平衡因子
                    result = queryBalanceFactorDtmsService.queryBalanceFactorV2(hbOneNo, balanceBean.getProductCode(), balanceBean.getNavDt());
                }

                if (result != null) {
                    balanceBean.setBalanceFactor(result.getBalanceFactor());
                    if (result.getBalanceFactor() != null && result.getBalanceFactor().compareTo(BigDecimal.ZERO) != 0) {
                        balanceBean.setConvertFinish(YesOrNoEnum.NO.getCode());
                    }
                    balanceBean.setBalanceFactorDate(balanceBean.getNavDt().substring(4, 6) + "-" + balanceBean.getNavDt().substring(6));
                }

            }

        }
    }

    private BigDecimal bigFormatMoney(BigDecimal value, int defaultScale, Integer dbConfigScale) {
        if (dbConfigScale != null) {
            return BigUtil.formatMoney(value, dbConfigScale);
        }

        return BigUtil.formatMoney(value, defaultScale);
    }

    private BigDecimal formatMoney(BigDecimal value, int defaultScale, Integer dbConfigScale) {
        if (dbConfigScale != null) {
            return MoneyUtil.formatMoney(value, dbConfigScale);
        }

        return MoneyUtil.formatMoney(value, defaultScale);
    }

    private BigDecimal formatMoneyRate(BigDecimal value, int defaultScale, Integer dbConfigScale) {
        if (dbConfigScale != null) {
            return BigUtil.formatMoneyRate(value, dbConfigScale);
        }

        return BigUtil.formatMoneyRate(value, defaultScale);
    }

    /**
     * 设置直销持仓
     */
    private void setDirectConfirmBalance(QueryConfirmBalanceParam param, List<CustConfirmBalanceDto> resultList) {
        // 查询非香港直销持仓
        List<ConfirmBalanceVo> directBalanceList = cmCustFundDirectRepository.queryUnHkConfirmBalanceBaseInfo(param.getHboneNo(), param.getDisCodeList(), param.getFundCode());
        if (CollectionUtils.isNotEmpty(directBalanceList)) {
            for (ConfirmBalanceVo vo : directBalanceList) {
                resultList.add(getCustConfirmBalanceDto(vo, false));
            }
        }
    }

    /**
     * 设置代销持仓
     */
    private void setAgentConfirmBalance(QueryConfirmBalanceParam param, List<CustConfirmBalanceDto> resultList) {
        // 查询确认持仓
        List<ConfirmBalanceVo> confrimBalanceList = custBooksRepository.queryAgentConfirmBalance(param.getTxAcctNo(), param.getFundCode(), param.getDisCodeList());
        if (CollectionUtils.isNotEmpty(confrimBalanceList)) {
            // 查询子账本明细
            List<String> confirmFundCodeList = confrimBalanceList.stream().map(ConfirmBalanceVo::getFundCode).distinct().collect(Collectors.toList());
            List<ConfirmBalanceVo> subCustConfirmBookList = subCustBooksRepository.selectAcctSubCustBooksByFundCodeList(param.getTxAcctNo(), confirmFundCodeList);
            // 合并代销和子账本
            List<ConfirmBalanceVo> agentConfirmVos = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(subCustConfirmBookList)) {
                // 过滤掉存在子账本明细的持仓
                List<String> subFundCodes = subCustConfirmBookList.stream().map(ConfirmBalanceVo::getFundCode).distinct().collect(Collectors.toList());
                List<ConfirmBalanceVo> filteredConfirmBalanceList = confrimBalanceList.stream()
                        .filter(c -> !subFundCodes.contains(c.getFundCode()))
                        .collect(Collectors.toList());
                // 合并
                agentConfirmVos.addAll(filteredConfirmBalanceList);
                agentConfirmVos.addAll(subCustConfirmBookList);
            } else {
                agentConfirmVos.addAll(confrimBalanceList);
            }
            for (ConfirmBalanceVo vo : agentConfirmVos) {
                CustConfirmBalanceDto dto = getCustConfirmBalanceDto(vo, true);
                resultList.add(dto);
            }
        }
    }

    /**
     * 构建用户确认持仓
     *
     * @param vo      确认持仓
     * @param isAgent true:是代销持仓,false:不是代销持仓
     * @return 构建用户确认持仓
     */
    private CustConfirmBalanceDto getCustConfirmBalanceDto(ConfirmBalanceVo vo, boolean isAgent) {
        CustConfirmBalanceDto dto = new CustConfirmBalanceDto();
        dto.setFundCode(vo.getFundCode());
        dto.setTxAcctNo(vo.getTxAcctNo());
        dto.setBalanceVol(vo.getBalanceVol());
        dto.setDisCode(vo.getDisCode());
        if (isAgent) {
            dto.setCurrency(CurrencyEnum.RMB.getCode());
            dto.setScaleType(ScaleTypeEnum.CONSIGNMENT.getCode());
        } else {
            dto.setCurrency(vo.getCurrency());
            dto.setScaleType(ScaleTypeEnum.DIRECT.getCode());
        }
        dto.setAckDt(vo.getAckDt());
        dto.setEstablishDt(vo.getEstablishDt());
        dto.setRegDt(vo.getRegDt());
        return dto;
    }

}
