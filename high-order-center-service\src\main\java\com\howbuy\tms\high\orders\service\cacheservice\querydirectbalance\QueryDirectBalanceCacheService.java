/**
 * Copyright (c) 2016, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.service.cacheservice.querydirectbalance;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.howbuy.interlayer.product.model.JjxswConfigModel;
import com.howbuy.tms.common.enums.busi.*;
import com.howbuy.tms.common.enums.database.ProductTypeEnum;
import com.howbuy.tms.common.outerservice.crm.nt.balancefactor.QueryBalnaceFactotResult;
import com.howbuy.tms.common.outerservice.crm.td.fractionatedcall.FractionatedCallOuterResult;
import com.howbuy.tms.common.outerservice.crm.td.fractionatedcall.FractionatedCallOuterService;
import com.howbuy.tms.common.outerservice.crm.td.fractionatedcall.bean.FractionatedCallBean;
import com.howbuy.tms.common.outerservice.dtms.QueryBalanceFactorDtmsService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductDBInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductNavBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductValueDateBean;
import com.howbuy.tms.common.outerservice.simu.comprehensive.QueryComprehensiveOuterService;
import com.howbuy.tms.common.outerservice.simu.comprehensive.bean.RmbhlzjjBean;
import com.howbuy.tms.common.utils.MoneyUtil;
import com.howbuy.tms.high.orders.dao.po.CmCustFundDirectPo;
import com.howbuy.tms.high.orders.dao.po.CmCustProdYjyzPo;
import com.howbuy.tms.high.orders.dao.vo.AckDealOrderInfo;
import com.howbuy.tms.high.orders.dao.vo.BalanceVo;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceResponse.BalanceBean;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryDirectBalanceParam;
import com.howbuy.tms.high.orders.facade.search.queryasset.HighFundAssetIncomeDomain;
import com.howbuy.tms.high.orders.service.business.calvaluedate.ProductValueDateCalService;
import com.howbuy.tms.high.orders.service.cacheservice.AbstractCacheService;
import com.howbuy.tms.high.orders.service.facade.search.queryacctbalance.bean.OwnershipOrderDto;
import com.howbuy.tms.high.orders.service.facade.search.querycommon.BigUtil;
import com.howbuy.tms.high.orders.service.repository.CmCustFundDirectRepository;
import com.howbuy.tms.high.orders.service.repository.CmCustProdYjyzRepository;
import com.howbuy.tms.high.orders.service.repository.CmCusttradeDirectRepository;
import com.howbuy.tms.high.orders.service.repository.CustBooksRepository;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.AcctBalanceBaseInfoService;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.QueryAcctBalanceBaseParam;
import com.howbuy.tms.high.orders.service.service.queryasset.QueryAssetService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * Description:通过缓存查询直销资产
 *
 * <AUTHOR>
 * @reason:TODO ADD REASON(可选)
 * @date 2016年11月7日 下午3:29:18
 * @since JDK 1.7
 */
@Service("queryDirectBalanceCacheService")
public class QueryDirectBalanceCacheService extends AbstractCacheService {
    private static final Logger logger = LogManager.getLogger(QueryDirectBalanceCacheService.class);
    /**
     * 需要处理业绩因子的特殊产品
     */
    private static final List<String> YJYZ_FUND_CODE_LIST = new ArrayList<>();
    // 只有一期的固收产品类型
    private static final Set<String> ONTHER_FIXEDINCODE_PRODUCT_SET = new HashSet<>();

    static {
        YJYZ_FUND_CODE_LIST.add("P01196");
    }

    static {
        ONTHER_FIXEDINCODE_PRODUCT_SET.add(StandardFixedIncomeFlagEnum.CASH_MANAGER.getCode());
        ONTHER_FIXEDINCODE_PRODUCT_SET.add(StandardFixedIncomeFlagEnum.BOND_GS.getCode());
        ONTHER_FIXEDINCODE_PRODUCT_SET.add(StandardFixedIncomeFlagEnum.BROKERAGE_COLLECTION_GS.getCode());
    }

    @Autowired
    private CmCustFundDirectRepository cmCustFundDirectRepository;
    @Autowired
    private CmCustProdYjyzRepository cmCustProdYjyzRepository;
    @Autowired
    private QueryComprehensiveOuterService queryComprehensiveOuterService;
    @Autowired
    private CustBooksRepository custBooksRepository;
    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;
    @Autowired
    private FractionatedCallOuterService fractionatedCallOuterService;
    @Autowired
    private ProductValueDateCalService productValueDateCalService;
    @Autowired
    private QueryBalanceFactorDtmsService queryBalanceFactorDtmsService;
    @Autowired
    private QueryAssetService queryAssetService;
    @Autowired
    private AcctBalanceBaseInfoService acctBalanceBaseInfoService;
    @Autowired
    private CmCusttradeDirectRepository cmCusttradeDirectRepository;
    /**
     * 千禧年产品列表
     */
    @Value("${qianXiProductsCfg}")
    private String qianXiProductsCfg;

    /**
     * 查询直销资产
     *
     * @param queryDirectBalanceParam 入参
     * @return
     */
    public List<BalanceBean> getBalanceList(QueryDirectBalanceParam queryDirectBalanceParam) {
        List<BalanceBean> resultBalanceList = new ArrayList<>();
        // 查询直销资产
        List<CmCustFundDirectPo> cmBalanceList = cmCustFundDirectRepository.selectDirectBalance(queryDirectBalanceParam.getHbOneNo(), queryDirectBalanceParam.getBalanceStatus(), queryDirectBalanceParam.getDisCodeList());
        logger.info("QueryDirectBalanceCacheService-getBalanceList,queryDirectBalanceParam={},cmBalanceList={}", JSON.toJSONString(queryDirectBalanceParam), JSON.toJSONString(cmBalanceList));
        if (CollectionUtils.isEmpty(cmBalanceList)) {
            logger.info("QueryDirectBalanceCacheService-getBalanceList,查询直销资产,queryDirectBalanceParam={} ", JSON.toJSONString(queryDirectBalanceParam));
            return resultBalanceList;
        }
        /* 子/主产品代码处理 */
        Set<String> fundCodeSet = new HashSet<>();
        for (CmCustFundDirectPo po : cmBalanceList) {
            BalanceBean bean = new BalanceBean();
            // 债券分期成立产品, crm直销持仓记在子产品代码维度(fundCode)
            if (StringUtils.isNotEmpty(po.getMjjDm())) {
                bean.setProductCode(po.getMjjDm());
                bean.setSubProductCode(po.getFundCode());
            } else {
                bean.setProductCode(po.getFundCode());
            }
            if (StringUtils.isNotBlank(queryDirectBalanceParam.getProductCode()) && !queryDirectBalanceParam.getProductCode().equals(bean.getProductCode())) {
                continue;
            }
            // 主产品代码
            fundCodeSet.add(bean.getProductCode());
            bean.setBalanceVol(po.getBalanceVol());
            // 直销产品创建日期等价与确认日期
            bean.setValueDate(po.getCreDt());
            bean.setDisCode(po.getDisCode());
            resultBalanceList.add(bean);
        }
        logger.info("直销,resultBalanceList={}", JSON.toJSONString(resultBalanceList));
        // 批量查询产品基本信息
        Map<String, HighProductDBInfoBean> dbInfoBeanMap = queryHighProductOuterService.getHighProductDBInfoMap(new ArrayList<>(fundCodeSet));
        logger.info("直销,dbInfoBeanMap={}", JSON.toJSONString(dbInfoBeanMap));
        // 根据请求条件过滤产品列表
        processProductDBInfoMap(dbInfoBeanMap, resultBalanceList, queryDirectBalanceParam.getHkSaleFlag(), queryDirectBalanceParam.getProductType(), queryDirectBalanceParam.getProductSubType());
        if (dbInfoBeanMap == null || dbInfoBeanMap.isEmpty()) {
            logger.info("QueryDirectBalanceCacheService|getBalanceList|dbInfoBeanMap is empty, fundCodeSet:{} ", JSON.toJSONString(fundCodeSet));
            return resultBalanceList;
        }

        buildBalance(queryDirectBalanceParam, resultBalanceList, dbInfoBeanMap);

        return resultBalanceList;
    }

    private void buildBalance(QueryDirectBalanceParam queryDirectBalanceParam, List<BalanceBean> resultBalanceList, Map<String, HighProductDBInfoBean> dbInfoBeanMap) {
        BalanceBean balance = null;
        HighProductDBInfoBean productBean = null;
        Set<String> incomeFundCodeSet = new HashSet<String>();
        Set<String> navFundCodeSet = new HashSet<String>();
        Set<String> netBuyAmtFundCodeSet = new HashSet<String>();
        Set<String> fractionateCallFundCodeSet = new HashSet<String>();
        Iterator<BalanceBean> ite = resultBalanceList.iterator();
        Set<String> fixedIncomeFundCodeSet = new HashSet<>();
        Set<String> hwSaleFundCodeSet = new HashSet<>();
        while (ite.hasNext()) {
            balance = ite.next();
            logger.info("直销,balance={}", JSON.toJSONString(balance));
            // 产品信息
            productBean = dbInfoBeanMap.get(balance.getProductCode());
            logger.info("直销,productBean={}", JSON.toJSONString(productBean));
            if (productBean == null) {
                ite.remove();
                logger.warn("QueryDirectBalanceCacheService|getBalanceList|fundCode:{}, productBean is null!", balance.getProductCode());
                continue;
            }
            // 过滤产品类型
            if (!StringUtils.isEmpty(queryDirectBalanceParam.getProductType())) {
                if (!queryDirectBalanceParam.getProductType().equals(ProductTypeEnum.SM.getCode())) {
                    ite.remove();
                    continue;
                }
            }
            // 过滤产品子类型
            if (!StringUtils.isEmpty(queryDirectBalanceParam.getProductSubType())) {
                // 第一个字符是"-"，则代表的是，排除这个类型
                String firstChar = queryDirectBalanceParam.getProductSubType().substring(0, 1);
                if ("-".equals(firstChar)) {
                    String leftChars = queryDirectBalanceParam.getProductSubType().substring(1);
                    if (leftChars.equals(productBean.getFundSubType())) {
                        ite.remove();
                        continue;
                    }
                } else {
                    if (!queryDirectBalanceParam.getProductSubType().equals(productBean.getFundSubType())) {
                        ite.remove();
                        continue;
                    }
                }
            }
            balance.setProductName(productBean.getFundAttr());
            balance.setProductType(ProductTypeEnum.SM.getCode());
            balance.setProductSubType(productBean.getFundSubType());
            balance.setCurrency(productBean.getCurrency());
            balance.setScaleType(ScaleTypeEnum.DIRECT.getCode());
            balance.setHkSaleFlag(productBean.getHkSaleFlag());
            // 股权产品存续期限描述
            balance.setFundCXQXStr(productBean.getFundCXQXStr());
            balance.setFractionateCallFlag(productBean.getFractionateCallFlag());
            balance.setStageEstablishFlag(productBean.getStageEstablishFlag());
            // 标准固收标识
            balance.setStandardFixedIncomeFlag(productBean.getStandardFixedIncomeFlag());
            // 业绩基准类型
            balance.setBenchmarkType(productBean.getBenchmarkType());
            balance.setHwSaleFlag(productBean.getHwSaleFlag());
            balance.setOneStepType(productBean.getOneStepType());
            balance.setTwoStepType(productBean.getTwoStepType());
            balance.setSecondStepType(productBean.getSecondStepType());
            // NA产品收费类型
            balance.setNaProductFeeType(productBean.getNaProductFeeType());
            balance.setSfhwcxg(productBean.getSfhwcxg());
            // 股权产品不计算收益(回款代替)
            if (ProductDBTypeEnum.GUQUAN.getCode().equals(productBean.getFundSubType())) {
                netBuyAmtFundCodeSet.add(balance.getProductCode());
                // 分次call产品
                if (FractionateCallFlagEnum.YES.getCode().equals(productBean.getFractionateCallFlag())) {
                    fractionateCallFundCodeSet.add(balance.getProductCode());
                }
            } else {
                if (StageEstablishFlagEnum.STAGE.getCode().equals(productBean.getStageEstablishFlag())) {
                    // 分期成立产品, 用子产品代码查信息
                    incomeFundCodeSet.add(balance.getSubProductCode());
                    navFundCodeSet.add(balance.getSubProductCode());
                } else {
                    incomeFundCodeSet.add(balance.getProductCode());
                    navFundCodeSet.add(balance.getProductCode());
                }

                if (ProductDBTypeEnum.GUDINGSHOUYI.getCode().equals(productBean.getFundSubType())) {
                    fixedIncomeFundCodeSet.add(productBean.getFundCode());
                }
            }

            // 香港产品集合
            if (YesOrNoEnum.YES.getCode().equals(productBean.getHkSaleFlag())) {
                if (StageEstablishFlagEnum.STAGE.getCode().equals(productBean.getStageEstablishFlag())) {
                    hwSaleFundCodeSet.add(balance.getSubProductCode());
                } else {
                    hwSaleFundCodeSet.add(balance.getProductCode());
                }
            }
        }

        // 产品收益指标位数控制信息(对应数值,小数点保留几位)
        Map<String, JjxswConfigModel> configInfoMap = getJjxswConfigMMap(hwSaleFundCodeSet);

        // 数据处理
        dataProcess(queryDirectBalanceParam.getHbOneNo(), queryDirectBalanceParam.getCrisisFundList(), resultBalanceList, dbInfoBeanMap, incomeFundCodeSet, navFundCodeSet,
                netBuyAmtFundCodeSet, fractionateCallFundCodeSet, fixedIncomeFundCodeSet, queryDirectBalanceParam.getDisCodeList(), configInfoMap, queryDirectBalanceParam.getCallType());
    }


    public Map<String, JjxswConfigModel> getJjxswConfigMMap(Set<String> fundCodeSet) {
        Map<String, JjxswConfigModel> map = new HashMap<>();

        List<JjxswConfigModel> models = queryHighProductOuterService.queryJjxswConfigByJjdm(Lists.newArrayList(fundCodeSet));


        if (CollectionUtils.isEmpty(models)) {
            return map;
        }

        for (JjxswConfigModel model : models) {
            map.put(model.getJjdm(), model);
        }
        return map;
    }


    /**
     * 数据处理
     *
     * @param hbOneNo
     * @param crisisFundList
     * @param resultBalanceList
     * @param dbInfoBeanMap
     * @param incomeFundCodeSet
     * @param navFundCodeSet
     * @param netBuyAmtFundCodeSet
     * @param fractionateCallFundCodeSet
     * @param jjxswConfigMMap
     */
    private void dataProcess(String hbOneNo, List<String> crisisFundList, List<BalanceBean> resultBalanceList,
                             Map<String, HighProductDBInfoBean> dbInfoBeanMap, Set<String> incomeFundCodeSet,
                             Set<String> navFundCodeSet, Set<String> netBuyAmtFundCodeSet,
                             Set<String> fractionateCallFundCodeSet, Set<String> fixedIncomeFundCodeSet,
                             List<String> disCodeList, Map<String, JjxswConfigModel> jjxswConfigMMap, String callType) {

        // 千禧年产品列表
        // 千禧年产品也需要查询分次call数据、净购买金额
        List<String> qianXiProducts = Arrays.asList(qianXiProductsCfg.split(","));
        logger.info("千禧年产品编码为,qianXiProductsCfg={}", qianXiProductsCfg);
        Set<String> netBuyAmtFundCodeQxSet = new HashSet<>();
        if (CollectionUtil.isNotEmpty(qianXiProducts)) {
            resultBalanceList.forEach(balanceBean -> {
                if (qianXiProducts.contains(balanceBean.getProductCode())) {
                    fractionateCallFundCodeSet.add(balanceBean.getProductCode());
                    netBuyAmtFundCodeQxSet.add(balanceBean.getProductCode());
                }
            });
        }

        // 批量查询私募产品净值map
        Map<String, HighProductNavBean> productNavMap = getNavMap(new ArrayList<>(navFundCodeSet));

        // 批量查询基金收益
        Map<String, HighFundAssetIncomeDomain> currentAssetMap =
                queryAssetService.getCurrentAssetMap(new ArrayList<>(incomeFundCodeSet), hbOneNo, disCodeList);

        // 批量查询股权产品收益
        Map<String, HighFundAssetIncomeDomain> guquanCurrentAssetMap =
                queryAssetService.getCurrentAssetMap(new ArrayList<>(netBuyAmtFundCodeSet), hbOneNo, disCodeList);

        // 查询产品净购买金额
        QueryAcctBalanceBaseParam queryAcctBalanceBaseParam = new QueryAcctBalanceBaseParam();
        queryAcctBalanceBaseParam.setHbOneNo(hbOneNo);
        queryAcctBalanceBaseParam.setDisCodeList(disCodeList);
        queryAcctBalanceBaseParam.setFundCodeList(new ArrayList<>(netBuyAmtFundCodeSet));
        Map<String, OwnershipOrderDto> ownershipDtoMap = acctBalanceBaseInfoService.getOwnershipOrderInfoMap(queryAcctBalanceBaseParam);
        Map<String, BigDecimal> netBuyAmtQxMap = getNetBuyAmtMapQX(hbOneNo, new ArrayList<>(netBuyAmtFundCodeQxSet));
        // 获取股权分次call产品实缴金额
        Map<String, BigDecimal> paidInAmtMap = getPaidInAmtMap(hbOneNo, new ArrayList<>(fractionateCallFundCodeSet));
        // 查询固收类产品起息日相关信息
        Map<String, List<HighProductValueDateBean>> valueDateMap = productValueDateCalService.getValueDateMap(fixedIncomeFundCodeSet);
        // 查询净值型产品的最近确认日期确认的认申购订单
        Map<String, List<AckDealOrderInfo>> ackMap = acctBalanceBaseInfoService.getDirectAckDealDtlMap(hbOneNo, navFundCodeSet);

        for (BalanceBean balanceBean : resultBalanceList) {
            if (crisisFundList.contains(balanceBean.getProductCode())) {
                balanceBean.setCrisisFlag(YesOrNoEnum.YES.getCode());
            } else {
                setBalanceInfo(hbOneNo, crisisFundList, dbInfoBeanMap, jjxswConfigMMap, qianXiProducts, productNavMap, currentAssetMap, guquanCurrentAssetMap, ownershipDtoMap, netBuyAmtQxMap, paidInAmtMap, ackMap, balanceBean);
            }
            // 计算固收类产品的起息日
            List<HighProductValueDateBean> fixedInconeValueDateList = valueDateMap.get(balanceBean.getProductCode());
            calFixedIncomeValueDate(balanceBean, fixedInconeValueDateList);
        }
    }

    /**
     * 设置持仓信息
     */
    private void setBalanceInfo(String hbOneNo, List<String> crisisFundList, Map<String, HighProductDBInfoBean> dbInfoBeanMap,
                                Map<String, JjxswConfigModel> jjxswConfigMMap, List<String> qianXiProducts,
                                Map<String, HighProductNavBean> productNavMap, Map<String, HighFundAssetIncomeDomain> currentAssetMap,
                                Map<String, HighFundAssetIncomeDomain> guquanCurrentAssetMap,
                                Map<String, OwnershipOrderDto> ownershipDtoMap, Map<String, BigDecimal> netBuyAmtMapQX,
                                Map<String, BigDecimal> paidInAmtMap, Map<String, List<AckDealOrderInfo>> ackMap, BalanceBean balanceBean) {
        // 特殊产品指标控制需求：获取产品净值披露方式（用于市值计算方式判断） 20221122
        // 固定收益类产品，即【产品大类productSubType】=2-固定收益，且【净值披露方式】=2-份额收益，则【持仓总市值】=【持仓份额累计】* 1
        if (ProductDBTypeEnum.GUDINGSHOUYI.getCode().equals(balanceBean.getProductSubType())) {
            HighProductDBInfoBean highProductDbInfoBean = dbInfoBeanMap.get(balanceBean.getProductCode());
            if (highProductDbInfoBean != null && highProductDbInfoBean.getNavDisclosureType() != null) {
                balanceBean.setNavDisclosureType(highProductDbInfoBean.getNavDisclosureType());
            }
        }

        // 产品汇率人民币中间价
        BigDecimal rmbZJJ = getRMBZJJ(balanceBean.getCurrency());
        // 产品净值和收益处理
        List<AckDealOrderInfo> ackDealOrderInfoList = ackMap.get(StringUtils.isNotEmpty(balanceBean.getSubProductCode()) ? balanceBean.getSubProductCode() : balanceBean.getProductCode());
        HighProductDBInfoBean highProductDBInfoBean = dbInfoBeanMap.get(balanceBean.getProductCode());
        processNavAndAsset(hbOneNo, balanceBean, rmbZJJ, highProductDBInfoBean, productNavMap, currentAssetMap, crisisFundList, jjxswConfigMMap, ackDealOrderInfoList);
        // 收益计算状态
        balanceBean.setIncomeCalStat(acctBalanceBaseInfoService.getIncomeCalStatus(balanceBean, crisisFundList));
        if (ProductDBTypeEnum.GUQUAN.getCode().equals(balanceBean.getProductSubType())) {
            OwnershipOrderDto ownershipOrderDto = ownershipDtoMap.get(balanceBean.getProductCode());
            if (ownershipOrderDto != null) {
                // 股权当前投资成本/实缴金额: 净购买金额
                processNetBuyAmount(balanceBean, ownershipOrderDto.getNetBuyAmt(), rmbZJJ);
                // 股权产品持仓成本
                balanceBean.setBalanceCostCurrency(MoneyUtil.formatMoney(ownershipOrderDto.getNetBuyAmt(), 2));
                balanceBean.setBalanceCost(MoneyUtil.formatMoney(ownershipOrderDto.getNetBuyAmt(), 2));
                // 股权产品转让标识
                balanceBean.setOwnershipTransferIdentity(ownershipOrderDto.getTransferIdentity());
            }
            // 股权产品认缴金额
            balanceBean.setPaidInAmt(MoneyUtil.formatMoney(paidInAmtMap.get(balanceBean.getProductCode()), 2));
            acctBalanceBaseInfoService.setGuQuanAssertInfoWithOutCrisis(balanceBean, guquanCurrentAssetMap != null ? null : guquanCurrentAssetMap.get(balanceBean.getProductCode()));
        } else if (ProductDBTypeEnum.GUDINGSHOUYI.getCode().equals(balanceBean.getProductSubType())) {
            acctBalanceBaseInfoService.setGuShouAssetInfoWithOutCrisis(balanceBean, currentAssetMap != null ? null : currentAssetMap.get(balanceBean.getProductCode()));
        }
        // 千禧年产品适配
        acctBalanceBaseInfoService.setQxFundInfo(netBuyAmtMapQX, paidInAmtMap, qianXiProducts, balanceBean, rmbZJJ);
    }




    private BigDecimal formatMoney(BigDecimal value, int defaultScale, Integer dbConfigScale) {
        if (dbConfigScale != null) {
            return MoneyUtil.formatMoney(value, dbConfigScale);
        }

        return MoneyUtil.formatMoney(value, defaultScale);
    }



    private void processBalanceFactor(BalanceBean balanceBean, String hbOneNo) {
        // 香港+阳光私募的产品需要设置平衡因子
        if (YesOrNoEnum.YES.getCode().equals(balanceBean.getHkSaleFlag())) {
            if (!"2".equals(balanceBean.getProductSubType()) && !"5".equals(balanceBean.getProductSubType()) && balanceBean.getNavDt() != null) {
                QueryBalnaceFactotResult result = null;
                if (YesOrNoEnum.YES.getCode().equals(balanceBean.getStageEstablishFlag())) {
                    // 分期成立产品，通过子产品代码查询平衡因子
                    result = queryBalanceFactorDtmsService.queryBalanceFactorV2(hbOneNo, balanceBean.getSubProductCode(), balanceBean.getNavDt());
                } else {
                    // 非分期成立产品，通过母产品代码查询平衡因子
                    result = queryBalanceFactorDtmsService.queryBalanceFactorV2(hbOneNo, balanceBean.getProductCode(), balanceBean.getNavDt());
                }

                if (result != null) {
                    balanceBean.setBalanceFactor(result.getBalanceFactor());
                    if (result.getBalanceFactor() != null && result.getBalanceFactor().compareTo(BigDecimal.ZERO) != 0) {
                        balanceBean.setConvertFinish(YesOrNoEnum.NO.getCode());
                    }
                    balanceBean.setBalanceFactorDate(balanceBean.getNavDt().substring(4, 6) + "-" + balanceBean.getNavDt().substring(6));
                }

            }

        }
    }

    /**
     * @param bal
     * @param fixedInconeValueDateList
     * @return void
     * @Description 计算起息日
     * <AUTHOR>
     * @Date 2018/12/5 17:00
     **/
    private void calFixedIncomeValueDate(BalanceBean bal, List<HighProductValueDateBean> fixedInconeValueDateList) {
        HighProductValueDateBean valueDateBean = null;
        if (!ONTHER_FIXEDINCODE_PRODUCT_SET.contains(bal.getStandardFixedIncomeFlag())) {
            valueDateBean = productValueDateCalService.getHighProductValueDate(bal.getValueDate(),
                    fixedInconeValueDateList);
            if (valueDateBean != null) {
                bal.setValueDate(valueDateBean.getValueDate());
                bal.setDueDate(valueDateBean.getDueDate());
            }
        } else {
            if (CollectionUtils.isNotEmpty(fixedInconeValueDateList)) {
                valueDateBean = fixedInconeValueDateList.get(fixedInconeValueDateList.size() - 1);
            }
        }

        if (valueDateBean != null) {
            bal.setBenchmark(valueDateBean.getBenchmark());
            bal.setRePurchaseFlag(valueDateBean.getRePurchaseFlag());
            bal.setInvestmentHorizon(valueDateBean.getInvestmentHorizon());
            bal.setCooperation(valueDateBean.getCooperation());
        }
    }


    /**
     * 获取汇率人民币中间价
     *
     * @param currency
     * @return
     */
    public BigDecimal getRMBZJJ(String currency) {
        if (CurrencyEnum.RMB.getCode().equals(currency)) {
            return null;
        }

        RmbhlzjjBean rmbhlzjjBean = queryComprehensiveOuterService.getRmbhlzjj(null, currency);
        if (rmbhlzjjBean == null || rmbhlzjjBean.getZjj() == null) {
            logger.error("QueryDirectBalanceCacheService|getBalanceList|rmbhlzjjBean is null, currency:{}, rmbhlzjjBean:{}",
                    currency, JSON.toJSONString(rmbhlzjjBean));
            return null;
        }

        return BigDecimal.valueOf(rmbhlzjjBean.getZjj());
    }

    /**
     * 净购买金额处理(币种)
     *
     * @param balanceBean
     * @param netBuyAmt
     * @param rmbZJJ
     */
    private void processNetBuyAmount(BalanceBean balanceBean, BigDecimal netBuyAmt, BigDecimal rmbZJJ) {
        if (netBuyAmt == null) {
            return;
        }

        // 外币市值处理
        balanceBean.setCurrencyNetBuyAmount(MoneyUtil.formatMoney(netBuyAmt, 2));
        if (CurrencyEnum.RMB.getCode().equals(balanceBean.getCurrency())) {
            balanceBean.setNetBuyAmount(MoneyUtil.formatMoney(netBuyAmt, 2));
        } else if (rmbZJJ != null) {
            balanceBean.setNetBuyAmount(MoneyUtil.formatMoney(netBuyAmt.multiply(rmbZJJ), 2));
        }
    }

    /**
     * 根据请求条件过滤产品列表
     *
     * @param highProductDBInfoBeanMap
     * @param hkSaleFlag
     */
    private void processProductDBInfoMap(Map<String, HighProductDBInfoBean> highProductDBInfoBeanMap, List<BalanceBean> balanceList, String hkSaleFlag, String productType, String productSubType) {
        if (CollectionUtils.isEmpty(balanceList)) {
            return;
        }

        if (StringUtils.isEmpty(hkSaleFlag) && StringUtils.isEmpty(productType) && StringUtils.isEmpty(productSubType)) {
            return;
        }

        if (highProductDBInfoBeanMap == null || highProductDBInfoBeanMap.isEmpty()) {
            balanceList.clear();
            return;
        }

        BalanceBean balanceBean = null;
        HighProductDBInfoBean highProductDBInfoBean = null;
        Iterator<BalanceBean> ite = balanceList.iterator();
        while (ite.hasNext()) {
            balanceBean = ite.next();
            highProductDBInfoBean = highProductDBInfoBeanMap.get(balanceBean.getProductCode());
            // 过滤香港代销
            if (!StringUtils.isEmpty(hkSaleFlag)) {
                if (highProductDBInfoBean == null || StringUtils.isEmpty(highProductDBInfoBean.getHkSaleFlag())
                        || !hkSaleFlag.equals(highProductDBInfoBean.getHkSaleFlag())) {
                    ite.remove();
                    continue;
                }
            }
        }
    }

    /**
     * 产品净值和收益处理
     *
     * @param hbOneNo
     * @param balanceBean
     * @param rmbZJJ
     * @param highProductDBInfoBean
     * @param productNavMap
     * @param currentAssetMap
     * @param jjxswConfigMMap
     */
    private void processNavAndAsset(String hbOneNo, BalanceBean balanceBean, BigDecimal rmbZJJ, HighProductDBInfoBean highProductDBInfoBean,
                                    Map<String, HighProductNavBean> productNavMap, Map<String, HighFundAssetIncomeDomain> currentAssetMap,
                                    List<String> crisisFundList, Map<String, JjxswConfigModel> jjxswConfigMMap, List<AckDealOrderInfo> ackDealOrderInfoList) {

        // 查询净值/收益fundCode
        String queryFundCode = balanceBean.getProductCode();

        // 危机产品, 不处理
        if (crisisFundList.contains(queryFundCode)) {
            return;
        }

        if (StageEstablishFlagEnum.STAGE.getCode().equals(highProductDBInfoBean.getStageEstablishFlag())) {
            queryFundCode = balanceBean.getSubProductCode();
        }

        JjxswConfigModel config = getConfigModel(jjxswConfigMMap, queryFundCode);

        // 产品净值/市值处理
        HighProductNavBean navBean = productNavMap.get(queryFundCode);
        acctBalanceBaseInfoService.setDirectMarketValueAndNavInfo(hbOneNo, balanceBean, rmbZJJ, ackDealOrderInfoList, config, navBean);

        // 持仓数据格式化
        balanceBean.setBalanceVol(formatMoney(balanceBean.getBalanceVol(), 2, config.getFews()));

        // 七日年化信息
        acctBalanceBaseInfoService.setYieldIncomeInfo(balanceBean);

        // 收益信息处理
        HighFundAssetIncomeDomain currentAssetDto = currentAssetMap.get(queryFundCode);
        acctBalanceBaseInfoService.setDirectBalanceAssetInfo(balanceBean, rmbZJJ, config, currentAssetDto);
    }


    private JjxswConfigModel getConfigModel(Map<String, JjxswConfigModel> jjxswConfigMMap, String queryFundCode) {
        // 特殊海外产品指标位数配置
        return (jjxswConfigMMap.get(queryFundCode) == null ? new JjxswConfigModel() : jjxswConfigMMap.get(queryFundCode));
    }


    /**
     * 查询股权产品(分次call)认缴金额
     *
     * @param hbOneNo
     * @param fundCodeList
     * @return
     */
    private Map<String, BigDecimal> getPaidInAmtMap(String hbOneNo, List<String> fundCodeList) {
        Map<String, BigDecimal> map = new HashMap<String, BigDecimal>();
        if (CollectionUtils.isEmpty(fundCodeList)) {
            return map;
        }


        // 查询客户产品的认缴金额
        FractionatedCallOuterResult result = fractionatedCallOuterService.queryPaidInAmt(hbOneNo, fundCodeList);
        if (result == null || CollectionUtils.isEmpty(result.getFractionatedCallBeanList())) {
            return map;
        }

        // 同一个fundCode，可能会对应多条数据，因此需要累加金额 add 千禧年产品适配需求 20230214
        for (FractionatedCallBean fractionatedCallBean : result.getFractionatedCallBeanList()) {
            BigDecimal paidInAmt = map.get(fractionatedCallBean.getFundCode()) == null ? fractionatedCallBean.getPaidInAmt()
                    : map.get(fractionatedCallBean.getFundCode()).add(fractionatedCallBean.getPaidInAmt());
            map.put(fractionatedCallBean.getFundCode(), paidInAmt);
        }

        return map;
    }


    /**
     * getNavMap:(批量获取基金净值map)
     *
     * @param fundCodeList
     * @return
     * <AUTHOR>
     * @date 2017年11月20日 下午5:00:24
     */
    private Map<String, HighProductNavBean> getNavMap(List<String> fundCodeList) {
        Map<String, HighProductNavBean> highProductNavMap = new HashMap<String, HighProductNavBean>();
        if (CollectionUtils.isEmpty(fundCodeList)) {
            return highProductNavMap;
        }

        // 批量查询基金基金净值信息
        List<HighProductNavBean> highProductNavBeanList = null;
        try {
            highProductNavBeanList = queryHighProductOuterService.getHighProductNavInfo(new ArrayList<String>(fundCodeList));
        } catch (Exception e) {
            logger.error("QueryAcctBalanceFacadeService|queryHighProductOuterService.getHighProductNavInfo({}) error :", e, JSON.toJSONString(fundCodeList));
        }

        if (!CollectionUtils.isEmpty(highProductNavBeanList)) {
            for (HighProductNavBean highProductNavBean : highProductNavBeanList) {
                highProductNavMap.put(highProductNavBean.getFundCode(), highProductNavBean);
            }
        }
        return highProductNavMap;
    }

    /**
     * getIncomeCalStatus:(获取当前收益计算状态)
     *
     * @param bal
     * @param crisisFundList
     * @return
     * <AUTHOR>
     * @date 2018年11月20日 下午5:05:31
     */
    private String getIncomeCalStatus(BalanceBean bal, List<String> crisisFundList) {
        String incomeCalStatus = null;
        String productDBType = bal.getProductSubType();
        if (crisisFundList.contains(bal.getProductCode())
                || ProductDBTypeEnum.GUQUAN.getCode().equals(productDBType)
                || (ProductDBTypeEnum.GUDINGSHOUYI.getCode().equals(productDBType)
                && !StandardFixedIncomeFlagEnum.BROKERAGE_COLLECTION_GS.getCode().equals(bal.getStandardFixedIncomeFlag()))
                || (ProductDBTypeEnum.GUDINGSHOUYI.getCode().equals(bal.getProductSubType())
                && NavDisclosureTypeEnum.FESY.getCode().equals(bal.getNavDisclosureType())
                && StandardFixedIncomeFlagEnum.BROKERAGE_COLLECTION_GS.getCode().equals(bal.getStandardFixedIncomeFlag()))) {
            // 股权类默认是计算完成; 危机产品默认收益计算状态为完成, 不影响客户总收益计算状态
            incomeCalStatus = IncomeCalStatEnum.FINISHED.getCode();
        } else if (StringUtils.isNotEmpty(bal.getNavDt())
                && StringUtils.isNotEmpty(bal.getIncomeDt())
                && bal.getNavDt().compareTo(bal.getIncomeDt()) <= 0) {
            incomeCalStatus = IncomeCalStatEnum.FINISHED.getCode();
        } else {
            incomeCalStatus = IncomeCalStatEnum.PROCESSING.getCode();
        }

        return incomeCalStatus;
    }

    /**
     * getNetBuyAmtMap:(千禧年产品查询累计购买净金额直销，仅查询120、130、122)
     *
     * @param hbOneNo
     * @param fundCodeList
     * @return
     * <AUTHOR>
     * @date 2018年09月20日 下午5:00:24
     */
    private Map<String, BigDecimal> getNetBuyAmtMapQX(String hbOneNo, List<String> fundCodeList) {
        Map<String, BigDecimal> netBuyMap = new HashMap<String, BigDecimal>();
        if (CollectionUtils.isEmpty(fundCodeList)) {
            return netBuyMap;
        }

        // 查询产品累计净购买金额
        List<BalanceVo> netBuyList = custBooksRepository.selectNetBuyAmountQX(hbOneNo, fundCodeList);
        if (CollectionUtils.isNotEmpty(netBuyList)) {
            for (BalanceVo netBuyVo : netBuyList) {
                netBuyMap.put(netBuyVo.getProductCode(), netBuyVo.getNetBuyAmount());
            }
        }
        return netBuyMap;
    }

}
