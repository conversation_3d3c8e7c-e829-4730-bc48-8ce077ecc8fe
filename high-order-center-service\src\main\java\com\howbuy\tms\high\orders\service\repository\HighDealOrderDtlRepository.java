package com.howbuy.tms.high.orders.service.repository;

import com.google.common.collect.Lists;
import com.howbuy.tms.common.enums.database.TxAckFlagEnum;
import com.howbuy.tms.common.utils.StringUtils;
import com.howbuy.tms.high.orders.dao.mapper.customize.HighDealOrderDtlPoMapper;
import com.howbuy.tms.high.orders.dao.po.HighDealOrderDtlPo;
import com.howbuy.tms.high.orders.dao.po.HighDealOrderDtlPoExample;
import com.howbuy.tms.high.orders.dao.vo.*;
import com.howbuy.tms.high.orders.service.service.highdealorderdtl.QueryHighDealOrderParam;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Repository
public class HighDealOrderDtlRepository {
    @Autowired
    private HighDealOrderDtlPoMapper highDealOrderDtlPoMapper;

    public int updateCancelOrder(Map<String, Object> fundDealOrderDtlMap) {
        return highDealOrderDtlPoMapper.updateCancelOrder(fundDealOrderDtlMap);
    }

    public int updateByDealDtlNo(HighDealOrderDtlPo highDealOrderDtlPo) {
        return highDealOrderDtlPoMapper.updateByDealDtlNo(highDealOrderDtlPo);
    }

    public BigDecimal countSumBuyAmt(String txAcctNo, String taTradeDt, String fundCode) {
        return highDealOrderDtlPoMapper.countSumBuyAmt(txAcctNo, taTradeDt, fundCode);
    }

    public BigDecimal countSumRedeemVol(String txAcctNo, String taTradeDt, String fundCode) {
        return highDealOrderDtlPoMapper.countSumRedeemVol(txAcctNo, taTradeDt, fundCode);
    }

    public int queryNotCompleteOrderDtlForModifyDiv(String txAcctNo, String fundCode, String fundShareClass) {
        return highDealOrderDtlPoMapper.queryNotCompleteOrderDtlForModifyDiv(txAcctNo, fundCode, fundShareClass);
    }

    public QueryHighDealOrderDtlVo selectHighDealOrderDtlByDealNo(String dealNo) {
        return highDealOrderDtlPoMapper.selectHighDealOrderDtlByDealNo(dealNo);
    }

    public List<QueryHighDealOrderDtlVo> selectMergeSubmitOrderDtlByMainDealNo(String dealNo) {
        return highDealOrderDtlPoMapper.selectMergeSubmitOrderDtlByMainDealNo(dealNo);
    }

    public ProductTotalSalesVo selectTotalSalesAndPlaces(String fundCode, String disCode, String startDt, String endDt, String channelCode, String txAcctNo) {
        return highDealOrderDtlPoMapper.selectTotalSalesAndPlaces(fundCode, disCode, startDt, endDt, channelCode, txAcctNo);
    }

    public int selectUsedAppointmentDealNo(String appointmentDealNo) {
        return highDealOrderDtlPoMapper.selectUsedAppointmentDealNo(appointmentDealNo);
    }

    public String selectLastWithPaySuccessLimitType(String txAcctNo, String disCode, String fundCode) {
        return highDealOrderDtlPoMapper.selectLastWithPaySuccessLimitType(txAcctNo, disCode, fundCode);
    }

    public Integer countIntransitTrade(String txAcctNo, String cpAcctNo, String disCode, List<String> list, String protocolNo) {
        return highDealOrderDtlPoMapper.countIntransitTrade(txAcctNo, cpAcctNo, disCode, list, protocolNo);
    }

    public Integer countTransferOutIntransitTrade(String mBusiCode, String txAcctNo, String cpAcctNo, String disCode, String protocolNo) {
        return highDealOrderDtlPoMapper.countTransferOutIntransitTrade(mBusiCode, txAcctNo, cpAcctNo, disCode, protocolNo);
    }

    public BigDecimal countDxGqhkAmt(List<String> disCodeList, String txAcctNo, String productCode) {
        return highDealOrderDtlPoMapper.countDxGqhkAmt(disCodeList, txAcctNo, productCode);
    }

    public BigDecimal countCrmGqhkAmt(String hbOneNo, String productCode) {
        return highDealOrderDtlPoMapper.countCrmGqhkAmt(hbOneNo, productCode);
    }

    public HighDealOrderDtlPo selectDtlByDealNo(String dealNo) {
        return highDealOrderDtlPoMapper.selectDtlByDealNo(dealNo);
    }

    public HighDealOrderDtlForEsVo selectDtlForEsByDealNo(String dealNo) {
        return highDealOrderDtlPoMapper.selectDtlForEsByDealNo(dealNo);
    }

    public HighDealOrderDtlForEsVo selectMargeOrderDtlForEsByMainDealNo(String mainDealNo) {
        return highDealOrderDtlPoMapper.selectMargeOrderDtlForEsByMainDealNo(mainDealNo);
    }

    public List<HighDealOrderDtlLatestAckVo> selectAckPurchaseDealDtl(String txAcctNo, Set<String> fundCodes) {
        return highDealOrderDtlPoMapper.selectAckDealDtl( txAcctNo, fundCodes);
    }


    public List<HighDealOrderDtlPo> selectRepurchaseDeals(String repurchaseProtocolNo) {
        return highDealOrderDtlPoMapper.selectRepurchaseDeals(repurchaseProtocolNo);
    }

    public List<HighDealOrderDtlPo> selectRepurchaseDealsByNos(String txAcctNo, List<String> repurchaseProtocolNoList) {
        if (CollectionUtils.isEmpty(repurchaseProtocolNoList)) {
            return new ArrayList<>();
        }
        return highDealOrderDtlPoMapper.selectRepurchaseDealsByNos(txAcctNo, repurchaseProtocolNoList);
    }

    public List<HighDealOrderDtlPo> selectRedeemOnWayVolList(List<String> disCodeList, String txAcctNo) {
        return highDealOrderDtlPoMapper.selectRedeemOnWayVolList(disCodeList, txAcctNo);
    }

    public int countDivOrderNumAfterDt(String txAcctNo, String fundCode, String ackDt) {
        return highDealOrderDtlPoMapper.countDivOrderNumAfterDt(txAcctNo, fundCode, ackDt);
    }

    public List<HighDealOrderDtlPo> queryDivOrderNumAfterDt(String txAcctNo, String fundCode, String ackDt) {
        return highDealOrderDtlPoMapper.queryDivOrderNumAfterDt(txAcctNo, fundCode, ackDt);
    }

    public List<HighDealOrderDtlPo> selectDtlCheck(ArrayList<String> fundCodes, ArrayList<String> submitTaDts, String mBusiCode) {
        if (CollectionUtils.isEmpty(submitTaDts)) {
            return Lists.newArrayList();
        }
        return highDealOrderDtlPoMapper.selectDtlCheck(fundCodes, submitTaDts, mBusiCode);
    }

    public List<HighDealOrderDtlPo> selectDtlForCheck(ArrayList<String> fundCodes, ArrayList<String> submitTaDts, List<String> mBusiCodeList, List<String> fundTypeList) {
        if (CollectionUtils.isEmpty(submitTaDts)) {
            return Lists.newArrayList();
        }
        return highDealOrderDtlPoMapper.selectDtlForCheck(fundCodes, submitTaDts, mBusiCodeList, fundTypeList);
    }

    public HighDealOrderDtlPo getFirstAckInfoForConsignment(String txAcctNo, String productCode) {
        return highDealOrderDtlPoMapper.getFirstAckInfoForConsignment(txAcctNo, productCode);
    }

    public HighDealOrderDtlPo getLastAckInfoForConsignment(String txAcctNo, String productCode) {
        return highDealOrderDtlPoMapper.getLastAckInfoForConsignment(txAcctNo, productCode);
    }

    public BigDecimal getCxgDjZt(List<String> productCodeList, String txAcctNo) {
        return highDealOrderDtlPoMapper.getCxgDjZt(productCodeList, txAcctNo);
    }

    public List<BalanceOrderVo> selectBalanceConsignmentOrderVo(String txAcctNo, List<String> fundCodeList, List<String> disCodeList) {
        List<BalanceOrderVo> list = highDealOrderDtlPoMapper.selectBalanceConsignmentOrderVo(txAcctNo, fundCodeList, disCodeList);
        return list == null ? new ArrayList<>() : list;
    }

    public List<HighDealOrderDtlPo> getOnWayAgentDealDtlList(QueryHighDealOrderParam param) {
        QueryDealParamVo queryDealParamVo = new QueryDealParamVo();
        BeanUtils.copyProperties(param, queryDealParamVo);
        return highDealOrderDtlPoMapper.getOnWayAgentDealDtlList(queryDealParamVo);
    }

    public int insertSelective(HighDealOrderDtlPo po) {
        return highDealOrderDtlPoMapper.insertSelective(po);
    }

    public List<HighDealOrderDtlPo> selectBySubmitDtOrAckDt(String txAcctNo, String hbOneNo, List<String> fundCodeList, String submitOrAckStartDt, String submitOrAckEndDt) {
        return highDealOrderDtlPoMapper.selectBySubmitDtOrAckDt(txAcctNo, hbOneNo, fundCodeList, submitOrAckStartDt, submitOrAckEndDt);
    }

    public List<HighDealOrderDtlPo> selectBySubmitDtOrAckDt(String txAcctNo, List<String> fundCodeList, String submitOrAckStartDt, String submitOrAckEndDt) {
        List<String> ackFlagList = Lists.newArrayList(TxAckFlagEnum.CONFIRM_PART.getCode(), TxAckFlagEnum.CONFIRM_SUCCESS.getCode());
        List<HighDealOrderDtlPo> highDealOrderDtlPoList = new ArrayList<>();
        HighDealOrderDtlPoExample highDealOrderDtlPoExample1 = new HighDealOrderDtlPoExample();
        HighDealOrderDtlPoExample.Criteria criteria = highDealOrderDtlPoExample1.createCriteria().andTxAcctNoEqualTo(txAcctNo).andFundCodeIn(fundCodeList).andTxAckFlagIn(ackFlagList);
        if (StringUtils.isNotBlank(submitOrAckStartDt)) {
            criteria.andSubmitTaDtGreaterThanOrEqualTo(submitOrAckStartDt);
        }
        if (StringUtils.isNotBlank(submitOrAckEndDt)) {
            criteria.andSubmitTaDtLessThanOrEqualTo(submitOrAckEndDt);
        }
        List<HighDealOrderDtlPo> highDealDtlBySubmitDtList = highDealOrderDtlPoMapper.selectByExample(highDealOrderDtlPoExample1);
        if (CollectionUtils.isNotEmpty(highDealDtlBySubmitDtList)) {
            highDealOrderDtlPoList.addAll(highDealDtlBySubmitDtList);
        }

        HighDealOrderDtlPoExample highDealOrderDtlPoExample2 = new HighDealOrderDtlPoExample();
        HighDealOrderDtlPoExample.Criteria criteria2 = highDealOrderDtlPoExample2.createCriteria().andTxAcctNoEqualTo(txAcctNo).andFundCodeIn(fundCodeList).andTxAckFlagIn(ackFlagList);
        if (StringUtils.isNotBlank(submitOrAckStartDt)) {
            criteria2.andSubmitTaDtIsNull().andAckDtGreaterThanOrEqualTo(submitOrAckStartDt);
        }
        if (StringUtils.isNotBlank(submitOrAckEndDt)) {
            criteria2.andSubmitTaDtIsNull().andAckDtLessThanOrEqualTo(submitOrAckEndDt);
        }
        List<HighDealOrderDtlPo> highDealDtlByAckDtList = highDealOrderDtlPoMapper.selectByExample(highDealOrderDtlPoExample2);
        if (CollectionUtils.isNotEmpty(highDealDtlByAckDtList)) {
            highDealOrderDtlPoList.addAll(highDealDtlByAckDtList);
        }
        return highDealOrderDtlPoList;
    }

    /**
     * @description: 查询基金确认金额与确认份额
     * @param fundCodeList 基金代码列表
     * @param ackStartDt 确认开始日期
     * @param ackEndDt 确认结束日期
     * @param txAckFlagList 确认状态列表
     * @return List<FundAckVolAndAmtVo> 基金确认金额与确认份额列表
     * @author: hongdong.xie
     * @date: 2025/3/20 14:30
     * @since JDK 1.8
     */
    public List<FundAckVolAndAmtVo> selectFundAckVolAndAmtInfo(List<String> fundCodeList,
                                                               String ackStartDt,
                                                               String ackEndDt,
                                                               List<String> txAckFlagList) {
        return highDealOrderDtlPoMapper.selectFundAckVolAndAmtInfo(fundCodeList, ackStartDt, ackEndDt, txAckFlagList);
    }
}
