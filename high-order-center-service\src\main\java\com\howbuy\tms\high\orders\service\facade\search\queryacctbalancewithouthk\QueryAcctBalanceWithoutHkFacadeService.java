package com.howbuy.tms.high.orders.service.facade.search.queryacctbalancewithouthk;

import com.alibaba.fastjson.JSON;
import com.howbuy.interlayer.product.model.JjxswConfigModel;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.*;
import com.howbuy.tms.common.message.MessageSource;
import com.howbuy.tms.common.outerservice.crm.td.fractionatedcall.FractionatedCallOuterResult;
import com.howbuy.tms.common.outerservice.crm.td.fractionatedcall.FractionatedCallOuterService;
import com.howbuy.tms.common.outerservice.crm.td.fractionatedcall.bean.FractionatedCallBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.*;
import com.howbuy.tms.common.outerservice.simu.comprehensive.QueryComprehensiveOuterService;
import com.howbuy.tms.common.outerservice.simu.comprehensive.bean.RmbhlzjjBean;
import com.howbuy.tms.common.threadpool.CommonThreadPool;
import com.howbuy.tms.common.utils.MoneyUtil;
import com.howbuy.tms.high.orders.dao.po.SubscribeAmtDetailPo;
import com.howbuy.tms.high.orders.dao.vo.AckDealOrderInfo;
import com.howbuy.tms.high.orders.dao.vo.BalanceVo;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceRequest;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceResponse;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceResponse.BalanceBean;
import com.howbuy.tms.high.orders.facade.search.queryacctbalancewithouthk.QueryAcctBalanceWithoutHkFacade;
import com.howbuy.tms.high.orders.facade.search.queryacctbalancewithouthk.QueryAcctBalanceWithoutHkRequest;
import com.howbuy.tms.high.orders.facade.search.queryasset.HighFundAssetIncomeDomain;
import com.howbuy.tms.high.orders.service.business.calvaluedate.ProductValueDateCalService;
import com.howbuy.tms.high.orders.service.business.task.QueryStructProductNavTask;
import com.howbuy.tms.high.orders.service.facade.search.queryacctbalance.bean.OwnershipOrderDto;
import com.howbuy.tms.high.orders.service.repository.CustBooksRepository;
import com.howbuy.tms.high.orders.service.repository.SubscribeAmtDetailRepository;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.AcctBalanceBaseInfoService;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.CustConfirmBalanceDto;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.QueryAcctBalanceBaseParam;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.QueryConfirmBalanceParam;
import com.howbuy.tms.high.orders.service.service.queryasset.QueryAssetService;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * @Description:查询非海外持仓
 * @Author: yun.lu
 * Date: 2025/8/29 11:25
 */
@DubboService
@Service("queryAcctBalanceWithoutHkFacade")
@RefreshScope
public class QueryAcctBalanceWithoutHkFacadeService implements QueryAcctBalanceWithoutHkFacade {
    private static final Logger log = LogManager.getLogger(QueryAcctBalanceWithoutHkFacadeService.class);
    /**
     * 特殊净值型产品
     */
    private static final String PE0053 = "PE0053";
    @Autowired
    private AcctBalanceBaseInfoService acctBalanceBaseInfoService;
    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;
    @Autowired
    private QueryAssetService queryAssetService;
    @Autowired
    private SubscribeAmtDetailRepository subscribeAmtDetailRepository;
    @Autowired
    private FractionatedCallOuterService fractionatedCallOuterService;
    @Autowired
    private CustBooksRepository custBooksRepository;
    @Autowired
    private ProductValueDateCalService productValueDateCalService;
    @Autowired
    private QueryComprehensiveOuterService queryComprehensiveOuterService;
    /**
     * 千禧年产品列表
     */
    @Value("${qianXiProductsCfg}")
    private String qianXiProductsCfg;

    @Override
    public QueryAcctBalanceResponse execute(QueryAcctBalanceWithoutHkRequest request) {
        log.info("查询非海外持仓-start,request={}", JSON.toJSONString(request));
        QueryAcctBalanceResponse response = new QueryAcctBalanceResponse();
        response.setTxAcctNo(request.getTxAcctNo());
        response.setDisCode(request.getDisCode());
        response.setDisCodeList(request.getDisCodeList());
        response.setReturnCode(ExceptionCodes.SUCCESS);
        response.setDescription(MessageSource.getMessageByCode(ExceptionCodes.SUCCESS));
        // 1.查询确认持仓
        QueryConfirmBalanceParam queryConfirmBalanceParam = new QueryConfirmBalanceParam();
        queryConfirmBalanceParam.setHboneNo(request.getHbOneNo());
        queryConfirmBalanceParam.setFundCode(request.getProductCode());
        queryConfirmBalanceParam.setTxAcctNo(request.getTxAcctNo());
        queryConfirmBalanceParam.setDisCodeList(request.getDisCodeList());
        List<BalanceBean> confirmBalanceList = queryConfirmBalance(queryConfirmBalanceParam);
        // 查询待确认订单
        // 查询在途订单
        filterBalanceInfoByAuth(response, confirmBalanceList, request);
        // 合并数据
        // 数据小数位处理



        // 份额格式化
        balanceBean.setBalanceVol(MoneyUtil.formatMoney(balanceBean.getBalanceVol(), 2));

        return null;
    }
    /**
     * 如果未授权,就需要过滤掉,好臻/好买香港的产品
     *
     * @param response    返回实体
     * @param balanceList 所有的持仓产品信息
     */
    private void filterBalanceInfoByAuth(QueryAcctBalanceResponse response, List<BalanceBean> balanceList, QueryAcctBalanceRequest request) {
        // 如果是未授权
        Iterator<BalanceBean> iterator = balanceList.iterator();
        while (iterator.hasNext()) {
            BalanceBean balanceBean = iterator.next();
            // 过滤香港产品,并在返回实体标注该用户有香港产品
            if (YesOrNoEnum.YES.getCode().equals(balanceBean.getHkSaleFlag())) {
                response.setHasHKProduct(YesOrNoEnum.YES.getCode());
                if (YesOrNoEnum.NO.getCode().equals(request.getNotFilterHkFund())) {
                    iterator.remove();
                    continue;
                }
            }
            // 过滤好臻产品,并在返回实体中标注该用户有好臻产品
            if (DisCodeEnum.HZ.getCode().equals(balanceBean.getDisCode())) {
                response.setHasHZProduct(YesOrNoEnum.YES.getCode());
                if (YesOrNoEnum.NO.getCode().equals(request.getNotFilterHzFund())) {
                    iterator.remove();
                }
            }
        }
    }
    /**
     * 查询确认持仓信息
     */
    private List<BalanceBean> queryConfirmBalance(QueryConfirmBalanceParam queryConfirmBalanceParam) {
        log.info("查询确认持仓信息-start,queryConfirmBalanceParam={}", JSON.toJSONString(queryConfirmBalanceParam));
        List<BalanceBean> balanceList = new ArrayList<>();
        // 1.查询确认持仓
        List<CustConfirmBalanceDto> confirmBalanceList = acctBalanceBaseInfoService.queryCustConfirmBalance(queryConfirmBalanceParam);
        if (CollectionUtils.isEmpty(confirmBalanceList)) {
            log.info("queryConfirmBalance-查询不到确认持仓");
            return balanceList;
        }
        // 2.查询产品db信息
        List<String> fundCodeList = confirmBalanceList.stream().map(CustConfirmBalanceDto::getFundCode).distinct().collect(Collectors.toList());
        Map<String, HighProductDBInfoBean> highProductDbInfoBeanMap = queryHighProductOuterService.getHighProductDBInfoMap(fundCodeList);
        // 3.查询拆单基金列表
        List<String> splitFundList = queryHighProductOuterService.queryRedeemSplitFund(null, null);
        // 4.查询清盘中产品
        List<String> crisisFundList = acctBalanceBaseInfoService.getCrisisFundList();
        // 5.遍历确认持仓
        // 需要计算收益的产品
        Set<String> incomeFundCodeSet = new HashSet<>();
        // 股权产品
        Set<String> guQuanFundCodeSet = new HashSet<>();
        // 代销分次call产品
        Set<String> agentFractionateCallFundCodeSet = new HashSet<>();
        // 直销分次call产品
        Set<String> directFractionateCallFundCodeSet = new HashSet<>();
        // 类固定收益
        Set<String> fixedIncomeFundCodeSet = new HashSet<>();
        // 千禧年产品
        Set<String> qxFundCodeSet = new HashSet<>();
        // 净值产品
        Set<String> navTypeFundCodeSet = new HashSet<>();
        List<String> qianXiProducts = Arrays.asList(qianXiProductsCfg.split(","));
        for (CustConfirmBalanceDto custConfirmBalanceDto : confirmBalanceList) {
            HighProductDBInfoBean productBean = highProductDbInfoBeanMap.get(custConfirmBalanceDto.getFundCode());
            if (productBean == null) {
                log.info("queryConfirmBalance-查询不到产品db信息,fundCode={}", custConfirmBalanceDto.getFundCode());
                continue;
            }
            BalanceBean bean = getBalanceBean(splitFundList, custConfirmBalanceDto, productBean, crisisFundList);
            balanceList.add(bean);
            // 添加产品类型
            addFundSet(bean, productBean,qxFundCodeSet, incomeFundCodeSet, guQuanFundCodeSet, agentFractionateCallFundCodeSet, directFractionateCallFundCodeSet, fixedIncomeFundCodeSet, navTypeFundCodeSet, qianXiProducts, custConfirmBalanceDto);
        }
        if (CollectionUtils.isEmpty(balanceList)) {
            log.info("queryConfirmBalance-确认持仓都没有产品信息");
            return balanceList;
        }
        // 6.按照是否分期成立,查询产品净值信息,分期成立的,用子基金代码,结果key是子基金代码,否则是母基金代码
        Map<String, HighProductNavBean> navBeanMap = buildAndSetNavInfo(balanceList, incomeFundCodeSet);
        // 7.批量查询产品收益,分期成立的,用子基金代码查询,结果key是子基金代码,否则是母基金代码
        List<String> subProductCodeList = balanceList.stream().filter(x -> !StringUtils.isBlank(x.getSubProductCode())).map(BalanceBean::getSubProductCode).distinct().collect(Collectors.toList());
        List<String> mainProductCodeList = balanceList.stream().filter(x -> StringUtils.isBlank(x.getSubProductCode())).map(BalanceBean::getProductCode).distinct().collect(Collectors.toList());
        List<String> allProductCodeList = new ArrayList<>();
        allProductCodeList.addAll(subProductCodeList);
        allProductCodeList.addAll(mainProductCodeList);
        Map<String, HighFundAssetIncomeDomain> currentAssetMap = queryAssetService.getCurrentAssetMap(allProductCodeList, queryConfirmBalanceParam.getHboneNo(), queryConfirmBalanceParam.getDisCodeList());
        // 8.批量获取基金分红信息map,入参是母基金代码,结果key是母基金代码
        List<String> allMainFundCodeList = balanceList.stream().filter(x -> !StringUtils.isBlank(x.getSubProductCode())).map(BalanceBean::getSubProductCode).distinct().collect(Collectors.toList());
        Map<String, HighProductNavDivBean> fundNavDivMap = getNavDivMap(new ArrayList<>(allMainFundCodeList));
        // 9.获取当前账户股权分次call产品实缴金额,入参区分直销基金代码,直销基金代码,返回结果key是母基金代码
        Map<String, BigDecimal> paidInAmtMap = getPaidInAmtMap(queryConfirmBalanceParam, agentFractionateCallFundCodeSet, directFractionateCallFundCodeSet);
        // 10.查询净值型产品的确认的认申购订单,用母基金代码
        Map<String, List<AckDealOrderInfo>> ackDealMap = getAckDealDtlMap(queryConfirmBalanceParam.getTxAcctNo(), queryConfirmBalanceParam.getHboneNo(), navTypeFundCodeSet);
        // 11.查询固收类产品起息日相关信息,用母基金代码查询
        Map<String, List<HighProductValueDateBean>> valueDateMap = productValueDateCalService.getValueDateMap(fixedIncomeFundCodeSet);
        // 12查询产品净购买金额
        QueryAcctBalanceBaseParam queryAcctBalanceBaseParam = new QueryAcctBalanceBaseParam();
        queryAcctBalanceBaseParam.setHbOneNo(queryConfirmBalanceParam.getHboneNo());
        queryAcctBalanceBaseParam.setTxAcctNo(queryConfirmBalanceParam.getTxAcctNo());
        queryAcctBalanceBaseParam.setFundCodeList(new ArrayList<>(guQuanFundCodeSet));
        Map<String, OwnershipOrderDto> ownershipDtoMap = acctBalanceBaseInfoService.getOwnershipOrderInfoMap(queryAcctBalanceBaseParam);
        //查询千禧年信息
        Map<String, BigDecimal> netBuyAmtQxMap = getQxFundNetBuyAmtMap(queryAcctBalanceBaseParam.getHbOneNo(), qxFundCodeSet);
        // 13.按照基金维度设置市值等字段
        for (BalanceBean balanceBean : balanceList) {
            // 产品汇率人民币中间价
            BigDecimal rmbZjj = getRMBZJJ(balanceBean.getCurrency());
            // 查询fundCode,分期成立的,部分数据需要用子基金匹配
            String queryProductCode = !StringUtils.isBlank(balanceBean.getSubProductCode()) ? balanceBean.getSubProductCode() : balanceBean.getProductCode();
            HighFundAssetIncomeDomain currentAssetDto = currentAssetMap.get(queryProductCode);
            // 清盘中的不计算市值与收益
            if (!YesOrNoEnum.YES.getCode().equals(balanceBean.getCrisisFlag())) {
                // 设置未清盘持仓信息
                setNotCrisisBalanceInfo(queryConfirmBalanceParam, navBeanMap, fundNavDivMap, ackDealMap, balanceBean, rmbZjj, queryProductCode, currentAssetDto);
            }
            // 收益计算状态
            balanceBean.setIncomeCalStat(acctBalanceBaseInfoService.getIncomeCalStatus(balanceBean, crisisFundList));
            // 分次call实缴金额设置
            balanceBean.setPaidInAmt(MoneyUtil.formatMoney(paidInAmtMap.get(balanceBean.getProductCode()), 2));
            // 股权产品需要设置成本价
            setNetBuyAmtInfo(balanceBean, ownershipDtoMap);
            // 部分产品类型不管是否清盘,都需设置收益回款信息
            acctBalanceBaseInfoService.setAssertWithOutCrisis(balanceBean, currentAssetDto);
            // 千禧年产品适配
            acctBalanceBaseInfoService.setQxFundInfo(netBuyAmtQxMap, paidInAmtMap, new ArrayList<>(qxFundCodeSet), balanceBean, rmbZjj);
            // 计算固收类产品的起息日
            acctBalanceBaseInfoService.calFixedIncomeValueDate(balanceBean, valueDateMap);
        }

    }

    /**
     * 设置未清盘持仓信息
     */
    private void setNotCrisisBalanceInfo(QueryConfirmBalanceParam queryConfirmBalanceParam,
                                         Map<String, HighProductNavBean> navBeanMap, Map<String, HighProductNavDivBean> fundNavDivMap,
                                         Map<String, List<AckDealOrderInfo>> ackDealMap, BalanceBean balanceBean, BigDecimal rmbZjj, String queryProductCode, HighFundAssetIncomeDomain currentAssetDto) {
        // 净值
        HighProductNavBean navBean = navBeanMap.get(queryProductCode);
        // 最新确认订单
        List<AckDealOrderInfo> ackList = ackDealMap.get(balanceBean.getProductCode());
        // 设置市值与净值信息
        setMarketValueAndNavInfo(queryConfirmBalanceParam.getHboneNo(), balanceBean, navBean, ackList, rmbZjj);
        // 处理净值分红状态
        acctBalanceBaseInfoService.processNavDivFlag(balanceBean, navBean, queryConfirmBalanceParam.getTxAcctNo(), queryConfirmBalanceParam.getHboneNo(), fundNavDivMap);
        // 收益信息
        setBalanceAssetInfo(balanceBean, rmbZjj, currentAssetDto);
        // 现金管理类产品查询七日年化和收益日期
        acctBalanceBaseInfoService.setYieldIncomeInfo(balanceBean);
    }

    /**
     * getQxFundNetBuyAmtMap:(千禧年产品查询累计购买净金额直销，仅查询120、130、122)
     *
     * @param hboneNo 一账通号
     * @param fundCodeList 基金代码
     */
    private Map<String, BigDecimal> getQxFundNetBuyAmtMap(String hboneNo, List<String> fundCodeList) {
        Map<String, BigDecimal> netBuyMap = new HashMap<String, BigDecimal>();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(fundCodeList)) {
            return netBuyMap;
        }

        // 查询产品累计净购买金额
        List<BalanceVo> netBuyList = custBooksRepository.selectNetBuyAmountQX(hboneNo, fundCodeList);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(netBuyList)) {
            for (BalanceVo netBuyVo : netBuyList) {
                netBuyMap.put(netBuyVo.getProductCode(), netBuyVo.getNetBuyAmount());
            }
        }
        return netBuyMap;
    }
    /**
     * 设置成本价信息
     *
     * @param balanceBean     持仓信息
     * @param ownershipDtoMap 股权订单信息
     */
    private void setNetBuyAmtInfo(BalanceBean balanceBean, Map<String, OwnershipOrderDto> ownershipDtoMap) {
        if (ProductDBTypeEnum.GUQUAN.getCode().equals(balanceBean.getProductSubType())) {
            OwnershipOrderDto ownershipOrderDto = ownershipDtoMap.get(balanceBean.getProductCode());
            if (ownershipOrderDto != null) {
                // 股权当前投资成本/实缴金额: 净购买金额
                processNetBuyAmount(balanceBean, ownershipOrderDto.getNetBuyAmt(), rmbZJJ);
                // 股权产品持仓成本
                balanceBean.setBalanceCostCurrency(MoneyUtil.formatMoney(ownershipOrderDto.getNetBuyAmt(), 2));
                balanceBean.setBalanceCost(MoneyUtil.formatMoney(ownershipOrderDto.getNetBuyAmt(), 2));
                // 股权产品转让标识
                balanceBean.setOwnershipTransferIdentity(ownershipOrderDto.getTransferIdentity());
            }
        }
    }


    /**
     * 净购买金额处理(币种)
     *
     * @param balanceBean
     * @param netBuyAmt
     * @param rmbZJJ
     */
    private void processNetBuyAmount(BalanceBean balanceBean, BigDecimal netBuyAmt, BigDecimal rmbZJJ) {
        if (netBuyAmt == null) {
            return;
        }

        // 外币市值处理
        balanceBean.setCurrencyNetBuyAmount(MoneyUtil.formatMoney(netBuyAmt, 2));
        if (CurrencyEnum.RMB.getCode().equals(balanceBean.getCurrency())) {
            balanceBean.setNetBuyAmount(MoneyUtil.formatMoney(netBuyAmt, 2));
        } else if (rmbZJJ != null) {
            balanceBean.setNetBuyAmount(MoneyUtil.formatMoney(netBuyAmt.multiply(rmbZJJ), 2));
        }
    }

    /**
     * 设置收益信息
     *
     * @param balanceBean     持仓信息
     * @param rmbZJJ          人民币汇率
     * @param currentAssetDto 收益信息
     */
    private void setBalanceAssetInfo(BalanceBean balanceBean, BigDecimal rmbZJJ, HighFundAssetIncomeDomain currentAssetDto) {
        if (ScaleTypeEnum.DIRECT.getCode().equals(balanceBean.getScaleType())) {
            acctBalanceBaseInfoService.setAgentBalanceAssetInfo(balanceBean, currentAssetDto);
        } else {
            acctBalanceBaseInfoService.setDirectBalanceAssetInfo(balanceBean, rmbZJJ, new JjxswConfigModel(), currentAssetDto);
        }
    }


    /**
     * 获取汇率人民币中间价
     *
     * @param currency
     * @return
     */
    public BigDecimal getRMBZJJ(String currency) {
        if (CurrencyEnum.RMB.getCode().equals(currency)) {
            return null;
        }

        RmbhlzjjBean rmbhlzjjBean = queryComprehensiveOuterService.getRmbhlzjj(null, currency);
        if (rmbhlzjjBean == null || rmbhlzjjBean.getZjj() == null) {
            log.error("QueryDirectBalanceCacheService|getBalanceList|rmbhlzjjBean is null, currency:{}, rmbhlzjjBean:{}",
                    currency, JSON.toJSONString(rmbhlzjjBean));
            return null;
        }

        return BigDecimal.valueOf(rmbhlzjjBean.getZjj());
    }

    private void setMarketValueAndNavInfo(String hboneNo, BalanceBean balanceBean, HighProductNavBean navBean, List<AckDealOrderInfo> ackList, BigDecimal rmbZJJ) {
        if (ScaleTypeEnum.DIRECT.getCode().equals(balanceBean.getScaleType())) {
            acctBalanceBaseInfoService.setDirectMarketValueAndNavInfo(hboneNo, balanceBean, rmbZJJ, ackList, new JjxswConfigModel(), navBean);
        } else {
            acctBalanceBaseInfoService.setAgentMarketValueAndNavInfo(balanceBean, navBean, ackList);
        }
    }

    /**
     * 设置将产品按照类型筛选出不同的set
     */
    private void addFundSet(BalanceBean balanceBean, HighProductDBInfoBean productBean,
                            Set<String> qxFundCodeSet, Set<String> incomeFundCodeSet,
                            Set<String> guQuanFundCodeSet, Set<String> agentFractionateCallFundCodeSet,
                            Set<String> directFractionateCallFundCodeSet, Set<String> fixedIncomeFundCodeSet,
                            Set<String> navTypeFundCodeSet, List<String> qianXiProducts, CustConfirmBalanceDto custConfirmBalanceDto) {

        // 分次call产品
        if (FractionateCallFlagEnum.YES.getCode().equals(productBean.getFractionateCallFlag())) {
            if (ScaleTypeEnum.DIRECT.getCode().equals(custConfirmBalanceDto.getFundCode())) {
                directFractionateCallFundCodeSet.add(custConfirmBalanceDto.getFundCode());
            } else {
                agentFractionateCallFundCodeSet.add(custConfirmBalanceDto.getFundCode());
            }
        }
        // 千禧年产品当做股权与分次call产品处理
        if (qianXiProducts.contains(custConfirmBalanceDto.getFundCode())) {
            qxFundCodeSet.add(balanceBean.getProductCode());
            if (ScaleTypeEnum.DIRECT.getCode().equals(custConfirmBalanceDto.getFundCode())) {
                directFractionateCallFundCodeSet.add(custConfirmBalanceDto.getFundCode());
            } else {
                agentFractionateCallFundCodeSet.add(custConfirmBalanceDto.getFundCode());
            }
        }
        // 股权产品
        if (ProductDBTypeEnum.GUQUAN.getCode().equals(productBean.getFundSubType())) {
            guQuanFundCodeSet.add(custConfirmBalanceDto.getFundCode());
        } else {
            if (StageEstablishFlagEnum.STAGE.getCode().equals(productBean.getStageEstablishFlag()) && !StringUtils.isEmpty(balanceBean.getSubProductCode())) {
                // 分期成立产品,直销的有子基金代码, 用子产品代码查信息
                incomeFundCodeSet.add(balanceBean.getSubProductCode());
            } else {
                incomeFundCodeSet.add(balanceBean.getProductCode());
            }
        }
        // 固定收益
        if (ProductDBTypeEnum.GUDINGSHOUYI.getCode().equals(productBean.getFundSubType())) {
            fixedIncomeFundCodeSet.add(custConfirmBalanceDto.getFundCode());
        }
        // 是否净值化产品
        if (isNavTypeProduct(productBean)) {
            navTypeFundCodeSet.add(custConfirmBalanceDto.getFundCode());
        }
    }

    /**
     * 查询确认交易订单
     *
     * @param txAcctNo  交易账号
     * @param hboneNo   一账通
     * @param fundCodes 基金代码
     * @return 确认交易
     */
    private Map<String, List<AckDealOrderInfo>> getAckDealDtlMap(String txAcctNo, String hboneNo, Set<String> fundCodes) {
        // 查询代销确认订单
        Map<String, List<AckDealOrderInfo>> agentAckDealDtlMap = acctBalanceBaseInfoService.getAgentAckDealDtlMap(txAcctNo, fundCodes);
        // 查询直销确认订单
        Map<String, List<AckDealOrderInfo>> directAckDealDtlMap = acctBalanceBaseInfoService.getDirectAckDealDtlMap(hboneNo, fundCodes);
        // 合并直销与代销交易
        for (Map.Entry<String, List<AckDealOrderInfo>> directEntry : directAckDealDtlMap.entrySet()) {
            if (agentAckDealDtlMap.containsKey(directEntry.getKey())) {
                List<AckDealOrderInfo> ackDealOrderInfos = agentAckDealDtlMap.get(directEntry.getKey());
                ackDealOrderInfos.addAll(directEntry.getValue());
            } else {
                agentAckDealDtlMap.put(directEntry.getKey(), directEntry.getValue());
            }
        }
        return agentAckDealDtlMap;
    }

    /**
     * 设置净值信息
     *
     * @param balanceList
     * @param incomeFundCodeSet
     */
    private Map<String, HighProductNavBean> buildAndSetNavInfo(List<BalanceBean> balanceList, Set<String> incomeFundCodeSet) {
        List<BalanceBean> stageBalanceList = balanceList.stream().filter(balanceBean -> YesOrNoEnum.YES.getCode().equals(balanceBean.getStageFlag())).collect(Collectors.toList());
        // 分期成立的产品净值信息,注意这个里面会设置持仓产品的子基金代码,map的key是子基金代码
        Map<String, HighProductNavBean> navMap = getStructureNavMap(stageBalanceList);
        // 5.2.查询非分期成立产品净值信息
        Map<String, HighProductNavBean> noStageNavMap = getNavMap(new ArrayList<>(incomeFundCodeSet));
        // 5.3.合并净值map
        navMap.putAll(noStageNavMap);
        return navMap;
    }

    /**
     * 查询股权产品(分次call)认缴金额
     *
     * @param queryConfirmBalanceParam         查询入参
     * @param agentFractionateCallFundCodeSet  代销基金代码
     * @param directFractionateCallFundCodeSet 直销基金代码
     * @return 金额认缴金额, key是母基金代码
     */
    private Map<String, BigDecimal> getPaidInAmtMap(QueryConfirmBalanceParam queryConfirmBalanceParam,
                                                    Set<String> agentFractionateCallFundCodeSet,
                                                    Set<String> directFractionateCallFundCodeSet) {
        Map<String, BigDecimal> map = new HashMap<String, BigDecimal>();
        // 查询代销认缴金额
        if (!CollectionUtils.isEmpty(agentFractionateCallFundCodeSet)) {
            // 查询客户产品的认缴金额
            List<SubscribeAmtDetailPo> subscribeAmtDetailList = subscribeAmtDetailRepository.getSubscribeAmtDetail(queryConfirmBalanceParam.getTxAcctNo(), new ArrayList<>(agentFractionateCallFundCodeSet));
            if (!CollectionUtils.isEmpty(subscribeAmtDetailList)) {
                for (SubscribeAmtDetailPo subscribeAmtDetailPo : subscribeAmtDetailList) {
                    map.put(subscribeAmtDetailPo.getFundCode(), subscribeAmtDetailPo.getSubscribeAmt());
                }
            } else {
                log.info("代销分次call基金查不到认缴实缴信息,agentFractionateCallFundCodeSet={}", JSON.toJSON(agentFractionateCallFundCodeSet));
            }
        }
        // 查询直销认缴金额
        if (!CollectionUtils.isEmpty(directFractionateCallFundCodeSet)) {
            // 查询客户产品的认缴金额
            FractionatedCallOuterResult result = fractionatedCallOuterService.queryPaidInAmt(queryConfirmBalanceParam.getHboneNo(), new ArrayList<>(directFractionateCallFundCodeSet));
            if (result == null || CollectionUtils.isEmpty(result.getFractionatedCallBeanList())) {
                log.info("直销分次call基金查不到认缴实缴信息,directFractionateCallFundCodeSet={}", JSON.toJSON(directFractionateCallFundCodeSet));
                return map;
            }
            // 同一个fundCode，可能会对应多条数据，因此需要累加金额 add 千禧年产品适配需求 20230214
            for (FractionatedCallBean fractionatedCallBean : result.getFractionatedCallBeanList()) {
                BigDecimal paidInAmt = map.get(fractionatedCallBean.getFundCode()) == null ? fractionatedCallBean.getPaidInAmt()
                        : map.get(fractionatedCallBean.getFundCode()).add(fractionatedCallBean.getPaidInAmt());
                map.put(fractionatedCallBean.getFundCode(), paidInAmt);
            }

        }
        return map;
    }

    /**
     * 获取分期成立产品净值信息
     */
    private Map<String, HighProductNavBean> getStructureNavMap(List<BalanceBean> balanceBeanList) {
        final Map<String, HighProductNavBean> fundNavMap = new ConcurrentHashMap<>();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(balanceBeanList)) {
            return fundNavMap;
        }

        // 多线程异步查询产品净值信息
        final CountDownLatch latch = new CountDownLatch(balanceBeanList.size());
        for (BalanceBean balanceBean : balanceBeanList) {
            CommonThreadPool.submit(new QueryStructProductNavTask(queryHighProductOuterService, fundNavMap, balanceBean, latch));
        }
        try {
            latch.await();
        } catch (InterruptedException e) {
            log.error("getStructureNavMap-查询分期成立产品净值出现异常,e:", e);
            Thread.currentThread().interrupt();
        }
        log.info("分期成立产品净值map,fundNavMap={}", JSON.toJSONString(fundNavMap));
        return fundNavMap;
    }

    /**
     * getNavMap:(批量获取基金净值map)
     *
     * @param midProductIds
     * @return
     * <AUTHOR>
     * @date 2017年11月20日 下午5:00:24
     */
    private Map<String, HighProductNavBean> getNavMap(List<String> midProductIds) {
        // 批量查询基金基金净值信息
        Map<String, HighProductNavBean> highProductNavMap = new HashMap<String, HighProductNavBean>();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(midProductIds)) {
            return highProductNavMap;
        }

        List<HighProductNavBean> highProductNavBeanList = null;
        try {
            highProductNavBeanList = queryHighProductOuterService.getHighProductNavInfo(new ArrayList<String>(midProductIds));
        } catch (Exception e) {
            log.error("getNavMap-查询产品净值异常e:{},fundCode={}", e, JSON.toJSONString(midProductIds));
        }

        if (!org.apache.commons.collections.CollectionUtils.isEmpty(highProductNavBeanList)) {
            for (HighProductNavBean highProductNavBean : highProductNavBeanList) {
                highProductNavMap.put(highProductNavBean.getFundCode(), highProductNavBean);
            }
        }
        return highProductNavMap;
    }

    /**
     * @param midProductIds
     * @return java.util.Map<java.lang.String, com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductNavDivBean>
     * @description:(批量获取基金分红信息map)
     * @author: haiguang.chen
     * @date: 2022/7/14 14:16
     * @since JDK 1.8
     */
    private Map<String, HighProductNavDivBean> getNavDivMap(List<String> midProductIds) {
        // 批量查询基金基金净值信息
        Map<String, HighProductNavDivBean> highProductNavDivBeanMap = new HashMap<>();
        if (CollectionUtils.isEmpty(midProductIds)) {
            return highProductNavDivBeanMap;
        }

        List<HighProductNavDivBean> highProductNavDivBeanList = null;
        try {
            highProductNavDivBeanList = queryHighProductOuterService.getHighProductNavDivInfo(new ArrayList<>(midProductIds));
        } catch (Exception e) {
            log.error("QueryAcctBalanceFacadeService|queryHighProductOuterService.getHighProductNavInfo,productCode={}, error={}", JSON.toJSONString(midProductIds), e);
        }

        if (!CollectionUtils.isEmpty(highProductNavDivBeanList)) {
            for (HighProductNavDivBean highProductNavDivBean : highProductNavDivBeanList) {
                highProductNavDivBeanMap.put(highProductNavDivBean.getFundCode(), highProductNavDivBean);
            }
        }
        return highProductNavDivBeanMap;
    }

    /**
     * 构建持仓基金bean
     */
    private BalanceBean getBalanceBean(List<String> splitFundList, CustConfirmBalanceDto custConfirmBalanceDto, HighProductDBInfoBean productBean, List<String> crisisFundList) {
        BalanceBean bean = new BalanceBean();
        bean.setDisCode(custConfirmBalanceDto.getDisCode());
        bean.setDisCodeList(Collections.singletonList(custConfirmBalanceDto.getDisCode()));
        bean.setProductCode(custConfirmBalanceDto.getFundCode());
        bean.setSubProductCode(custConfirmBalanceDto.getSubFundCode());
        bean.setProductName(productBean.getFundAttr());
        bean.setProductType(productBean.getFundType());
        bean.setProductSubType(productBean.getFundSubType());
        bean.setCurrency(productBean.getCurrency());
        bean.setScaleType(custConfirmBalanceDto.getScaleType());
        bean.setHkSaleFlag(productBean.getHkSaleFlag());
        bean.setBalanceVol(custConfirmBalanceDto.getBalanceVol());
        // 股权产品存续期限描述
        bean.setFundCXQXStr(productBean.getFundCXQXStr());
        bean.setFractionateCallFlag(productBean.getFractionateCallFlag());
        bean.setStageEstablishFlag(productBean.getStageEstablishFlag());
        // 临时用起息日字段存储确认日期，作为后续的查询条件查询实际起息日
        bean.setValueDate(custConfirmBalanceDto.getRegDt());
        // 标准固收标识(固收类有此标识:0-非标准固收，1-标准固收，2-现金管理， 3-券商集合理财 4-纯债产品)
        bean.setStandardFixedIncomeFlag(productBean.getStandardFixedIncomeFlag());
        // 基准类型（0-业绩比较基准 1-业绩报酬计提基准
        bean.setBenchmarkType(productBean.getBenchmarkType());
        bean.setHwSaleFlag(productBean.getHwSaleFlag());
        bean.setOneStepType(productBean.getOneStepType());
        bean.setTwoStepType(productBean.getTwoStepType());
        bean.setSecondStepType(productBean.getSecondStepType());
        // 产品销售类型
        bean.setProductSaleType(productBean.getProductSaleType());
        bean.setSfhwcxg(productBean.getSfhwcxg());
        // 成立日期
        bean.setEstablishDt(custConfirmBalanceDto.getEstablishDt());
        // NA产品收费类型
        bean.setNaProductFeeType(productBean.getNaProductFeeType());
        // 是否是净值型产品 todo 后续切公共工具类
        bean.setIsNavProduct(isNavTypeProduct(productBean) ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
        // 是否拆单产品
        if (splitFundList.contains(bean.getProductCode())) {
            bean.setStageFlag(YesOrNoEnum.YES.getCode());
        }
        // 净值披露方式(1-净值,2-份额收益)
        if (productBean.getNavDisclosureType() != null) {
            bean.setNavDisclosureType(productBean.getNavDisclosureType());
        }
        // 清盘中赋值
        if (crisisFundList.contains(bean.getProductCode())) {
            bean.setCrisisFlag(YesOrNoEnum.YES.getCode());
        }
        return bean;
    }

    /**
     * 是否净值化产品
     *
     * @param productBean 产品信息
     * @return true:是净值型产品,false:非净值型产品
     */
    private boolean isNavTypeProduct(HighProductDBInfoBean productBean) {
        if (PE0053.equals(productBean.getFundCode())) {
            return true;
        }
        // 非股权或者净值化固定收益产品或者纯债固收
        return !ProductDBTypeEnum.GUQUAN.getCode().equals(productBean.getFundSubType())
                && !(ProductDBTypeEnum.GUDINGSHOUYI.getCode().equals(productBean.getFundSubType())
                && !((StandardFixedIncomeFlagEnum.BROKERAGE_COLLECTION_GS.getCode().equals(productBean.getStandardFixedIncomeFlag()))
                || StandardFixedIncomeFlagEnum.BOND_GS.getCode().equals(productBean.getStandardFixedIncomeFlag())));
    }
}
