<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0"><modelVersion>4.0.0</modelVersion>
	<groupId>com.howbuy.interlayer</groupId>
    <artifactId>product-center-model</artifactId>
    <packaging>jar</packaging>
    <version>4.8.85-RELEASE</version>
    <name>product-center-model</name>
    <properties>
        <com.howbuy.tms-common-client.version>4.8.59-RELEASE</com.howbuy.tms-common-client.version>
        <com.howbuy.tms-common-cache.version>4.8.59-RELEASE</com.howbuy.tms-common-cache.version>
        <com.howbuy.tms-common-validator.version>4.8.59-RELEASE</com.howbuy.tms-common-validator.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <version>2.6</version>
        </dependency>
<!--        依赖client,因为没有指定父pom,就在这里制定版本-->
        <dependency>
            <groupId>com.howbuy.tms</groupId>
            <artifactId>tms-common-client</artifactId>
            <version>${com.howbuy.tms-common-client.version}</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.16</version>
        </dependency>
        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>utils</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>2.4</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.5</version>
                <configuration>
					<encoding>UTF-8</encoding>
					<includes>
						<include>**/*TestM.java</include>
					</includes>
				</configuration>
                <!--<configuration>
                    <skipTests>true</skipTests>
                </configuration>-->
            </plugin>
            <plugin>
	            <groupId>org.apache.maven.plugins</groupId>
	            <artifactId>maven-jar-plugin</artifactId>
	            <configuration>
	                <archive>
	                    <manifestEntries>
	                        <Package-Stamp>${parelease}</Package-Stamp>
	                    </manifestEntries>
	                </archive>
	            </configuration>
	        </plugin>
        </plugins>
    </build>
    <distributionManagement>
        <repository>
            <id>howbuy-release</id>
            <name>howbuy-release</name>
            <url>http://nx-java.howbuy.pa/repository/howbuy-release/</url>
        </repository>
        <snapshotRepository>
            <id>howbuy-snapshot</id>
            <name>howbuy-snapshot</name>
            <url>http://nx-java.howbuy.pa/repository/howbuy-snapshot/</url>
        </snapshotRepository>
    </distributionManagement>
</project>