<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.howbuy.otc</groupId>
		<artifactId>otc-common</artifactId>
		<version>7.12.4-RELEASE</version>
	</parent>
	<artifactId>otc-common-entity</artifactId>
	<packaging>jar</packaging>
	<dependencies>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>${lombok.version}</version>
		</dependency>
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-annotation</artifactId>
			<version>3.4.2</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>com.howbuy.otc</groupId>
			<artifactId>otc-common-api</artifactId>
			<version>${com.howbuy.otc-common.version}</version>
		</dependency>
	</dependencies>
</project>