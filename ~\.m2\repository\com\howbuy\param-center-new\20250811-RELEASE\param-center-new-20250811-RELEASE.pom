<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.3.12.RELEASE</version>
		<relativePath /> 
	</parent>
	<groupId>com.howbuy</groupId>
	<artifactId>param-center-new</artifactId>
	<version>20250811-RELEASE</version>
	<name>param-center-new</name>
	<packaging>pom</packaging>
	<description>Demo project for Spring Boot</description>

	<properties>
		<java.version>1.8</java.version>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<com.howbuy.param.center.version>1.0.0-SNAPSHOT</com.howbuy.param.center.version>
		<mybatis.plus.version>3.4.2</mybatis.plus.version>
		<oracle.version>********.0</oracle.version>
		<mysql.version>8.0.11</mysql.version>
		<lombok.version>1.18.20</lombok.version>
		<guava.version>16.0.1</guava.version>
		<fastjson.version>1.2.72</fastjson.version>
		<howbuy.cache.version>3.0.1-RELEASE</howbuy.cache.version>
		<dubbo.version>3.2.12</dubbo.version>
		<druid.version>1.1.22</druid.version>
		<hutool.version>5.8.0</hutool.version>
		<pinyin4j.version>2.5.1</pinyin4j.version>
		<powermock.version>2.0.2</powermock.version>
		<spring-cloud.version>Hoxton.SR9</spring-cloud.version>
		<spring-cloud-alibaba.version>2.2.6.RELEASE</spring-cloud-alibaba.version>
		<zk.version>3.4.9</zk.version>
		<curator.version>4.2.0</curator.version>
		<shiro.version>1.5.3</shiro.version>
		<jwt.version>3.8.3</jwt.version>
		<jjwt.version>0.9.0</jjwt.version>
		<fst.version>2.57</fst.version>
		<easyexcel.version>2.2.6</easyexcel.version>
		<openpdf.version>1.3.34</openpdf.version>
		<poi.version>3.17</poi.version>
		<itext.version>7.1.15</itext.version>
		<sevenzipjbinding.version>16.02-2.01</sevenzipjbinding.version>
		<ant.version>1.10.10</ant.version>
		<curator-server.version>5.5.0</curator-server.version>
		<docx4j.version>8.2.4</docx4j.version>
		<caffeine.version>2.6.2</caffeine.version>
		<ehcache.version>1.3.0</ehcache.version>
		<dfile.version>1.18.1-RELEASE</dfile.version>
		<jedis.version>2.9.3</jedis.version>
		<collections4.version>4.1</collections4.version>
		<shardingsphere.version>4.0.0-RC1</shardingsphere.version>
		<org.mapstruct.version>1.5.5.Final</org.mapstruct.version>
		<com.howbuy.hps-boot-starter.version>3.7.0-RELEASE</com.howbuy.hps-boot-starter.version>
		<com.howbuy.howbuy-boot-actuator-dubbo3.version>2.2.0-RELEASE</com.howbuy.howbuy-boot-actuator-dubbo3.version>
		<com.howbuy.fin-online-facade.version>3.3.2-RELEASE</com.howbuy.fin-online-facade.version>
		<com.howbuy.product-center-client.version>4.8.26-RELEASE</com.howbuy.product-center-client.version>
		<com.howbuy.howbuy-boot-actuator.version>2.1.0-RELEASE</com.howbuy.howbuy-boot-actuator.version>
		<com.howbuy.param-center-new.version>20250811-RELEASE</com.howbuy.param-center-new.version>
		<com.howbuy.howbuy-cachemanagement.version>3.5.1-RELEASE</com.howbuy.howbuy-cachemanagement.version>
		<com.howbuy.howbuy-ccms-watcher.version>6.0.1-RELEASE</com.howbuy.howbuy-ccms-watcher.version>
		<com.howbuy.howbuy-message-service.version>2.4.0-RELEASE</com.howbuy.howbuy-message-service.version>
		<com.howbuy.message-public-client.version>5.1.13-RELEASE</com.howbuy.message-public-client.version>
		<com.howbuy.howbuy-auth-facade.version>2.2.0-RELEASE</com.howbuy.howbuy-auth-facade.version>
        <com.howbuy.howbuy-fund-client.version>release-20241029-fundcache-update-RELEASE</com.howbuy.howbuy-fund-client.version>
    </properties>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>param-dao</artifactId>
				<version>${com.howbuy.param-center-new.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>param-common</artifactId>
				<version>${com.howbuy.param-center-new.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>param-service</artifactId>
				<version>${com.howbuy.param-center-new.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>param-biz</artifactId>
				<version>${com.howbuy.param-center-new.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>param-console-facade</artifactId>
				<version>${com.howbuy.param-center-new.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>param-server-facade</artifactId>
				<version>${com.howbuy.param-center-new.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>param-vo</artifactId>
				<version>${com.howbuy.param-center-new.version}</version>
			</dependency>
			<dependency>
				<groupId>com.baomidou</groupId>
				<artifactId>mybatis-plus-boot-starter</artifactId>
				<version>${mybatis.plus.version}</version>
			</dependency>
			<dependency>
				<groupId>mysql</groupId>
				<artifactId>mysql-connector-java</artifactId>
				<version>${mysql.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.pa.cache</groupId>
				<artifactId>howbuy-cache-client-trade</artifactId>
				<version>${howbuy.cache.version}</version>
				<exclusions>
					<exclusion>
						<groupId>org.apache.curator</groupId>
						<artifactId>curator-client</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.howbuy.boot</groupId>
				<artifactId>howbuy-boot-actuator-dubbo3</artifactId>
				<version>${com.howbuy.howbuy-boot-actuator-dubbo3.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.dubbo</groupId>
				<artifactId>dubbo-spring-boot-starter</artifactId>
				<version>${dubbo.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.dubbo</groupId>
				<artifactId>dubbo-dependencies-zookeeper</artifactId>
				<version>${dubbo.version}</version>
				<type>pom</type>
			</dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-dependencies</artifactId>
				<version>${spring-cloud.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<dependency>
				<groupId>com.alibaba.cloud</groupId>
				<artifactId>spring-cloud-alibaba-dependencies</artifactId>
				<version>${spring-cloud-alibaba.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<dependency>
				<groupId>org.apache.zookeeper</groupId>
				<artifactId>zookeeper</artifactId>
				<version>${zk.version}</version>
				<exclusions>
					<exclusion>
						<groupId>org.slf4j</groupId>
						<artifactId>slf4j-log4j12</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<artifactId>druid</artifactId>
				<groupId>com.alibaba</groupId>
				<version>${druid.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.curator</groupId>
				<artifactId>curator-framework</artifactId>
				<version>${curator.version}</version>
				<exclusions>
					<exclusion>
						<groupId>org.apache.zookeeper</groupId>
						<artifactId>zookeeper</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>org.apache.curator</groupId>
				<artifactId>curator-recipes</artifactId>
				<version>${curator.version}</version>
			</dependency>
			<dependency>
				<groupId>cn.hutool</groupId>
				<artifactId>hutool-all</artifactId>
				<version>${hutool.version}</version>
			</dependency>
			<dependency>
				<groupId>com.belerweb</groupId>
				<artifactId>pinyin4j</artifactId>
				<version>${pinyin4j.version}</version>
			</dependency>
			<dependency>
				<groupId>org.powermock</groupId>
				<artifactId>powermock-module-testng</artifactId>
				<version>${powermock.version}</version>
			</dependency>
			<dependency>
				<groupId>org.powermock</groupId>
				<artifactId>powermock-api-mockito2</artifactId>
				<version>${powermock.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.shiro</groupId>
				<artifactId>shiro-spring-boot-starter</artifactId>
				<version>${shiro.version}</version>
			</dependency>
			<dependency>
				<groupId>com.auth0</groupId>
				<artifactId>java-jwt</artifactId>
				<version>${jwt.version}</version>
			</dependency>
			<dependency>
				<groupId>io.jsonwebtoken</groupId>
				<artifactId>jjwt</artifactId>
				<version>${jjwt.version}</version>
			</dependency>
			<dependency>
				<groupId>de.ruedigermoeller</groupId>
				<artifactId>fst</artifactId>
				<version>${fst.version}</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>easyexcel</artifactId>
				<version>${easyexcel.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.commons</groupId>
				<artifactId>commons-collections4</artifactId>
				<version>${collections4.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.fds</groupId>
				<artifactId>hps-boot-starter</artifactId>
				<version>${com.howbuy.hps-boot-starter.version}</version>
			</dependency>
			<dependency>
				<groupId>com.github.ben-manes.caffeine</groupId>
				<artifactId>caffeine</artifactId>
				<version>${caffeine.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.finonline</groupId>
				<artifactId>fin-online-facade</artifactId>
				<version>${com.howbuy.fin-online-facade.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.interlayer</groupId>
				<artifactId>product-center-client</artifactId>
				<version>${com.howbuy.product-center-client.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.shardingsphere</groupId>
				<artifactId>sharding-jdbc-spring-boot-starter</artifactId>
				<version>${shardingsphere.version}</version>
			</dependency>
			<dependency>
				<groupId>org.projectlombok</groupId>
				<artifactId>lombok</artifactId>
				<version>${lombok.version}</version>
			</dependency>
			<dependency>
				<groupId>org.mapstruct</groupId>
				<artifactId>mapstruct</artifactId>
				<version>${org.mapstruct.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.guava</groupId>
				<artifactId>guava</artifactId>
				<version>${guava.version}</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>fastjson</artifactId>
				<version>${fastjson.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-ccms-watcher</artifactId>
				<version>${com.howbuy.howbuy-ccms-watcher.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-message-service</artifactId>
				<version>${com.howbuy.howbuy-message-service.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>slf4j-log4j12</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-message-amq</artifactId>
				<version>${com.howbuy.howbuy-message-service.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>activemq-all</artifactId>
						<groupId>org.apache.activemq</groupId>
					</exclusion>
					<exclusion>
						<artifactId>slf4j-log4j12</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>org.apache.activemq</groupId>
				<artifactId>activemq-client</artifactId>
				<version>5.11.2</version>
				<exclusions>
					<exclusion>
						<artifactId>slf4j-api</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-message-rocket</artifactId>
				<version>${com.howbuy.howbuy-message-service.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>slf4j-log4j12</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			
			<dependency>
				<groupId>com.howbuy.boot</groupId>
				<artifactId>howbuy-boot-actuator</artifactId>
				<version>${com.howbuy.howbuy-boot-actuator.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>druid</artifactId>
						<groupId>com.alibaba</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-cachemanagement</artifactId>
				<version>${com.howbuy.howbuy-cachemanagement.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>slf4j-log4j12</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>activemq-all</artifactId>
						<groupId>org.apache.activemq</groupId>
					</exclusion>
					<exclusion>
						<groupId>javax.servlet</groupId>
						<artifactId>servlet-api</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>net.sf.ehcache</groupId>
				<artifactId>ehcache</artifactId>
				<version>${ehcache.version}</version>
			</dependency>
			<dependency>
				<groupId>redis.clients</groupId>
				<artifactId>jedis</artifactId>
				<version>${jedis.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.cc.message</groupId>
				<artifactId>message-public-client</artifactId>
				<version>${com.howbuy.message-public-client.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-auth-facade</artifactId>
				<version>${com.howbuy.howbuy-auth-facade.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.dfile</groupId>
				<artifactId>howbuy-dfile-service</artifactId>
				<version>${dfile.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.dfile</groupId>
				<artifactId>howbuy-dfile-impl-webdav</artifactId>
				<version>${dfile.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.dfile</groupId>
				<artifactId>howbuy-dfile-impl-local</artifactId>
				<version>${dfile.version}</version>
			</dependency>
			<dependency>
				<groupId>com.github.librepdf</groupId>
				<artifactId>openpdf</artifactId>
				<version>${openpdf.version}</version>
			</dependency>
			<dependency>
				<groupId>com.github.librepdf</groupId>
				<artifactId>openpdf-fonts-extra</artifactId>
				<version>${openpdf.version}</version>
			</dependency>
			<dependency>
				<groupId>org.docx4j</groupId>
				<artifactId>docx4j-JAXB-ReferenceImpl</artifactId>
				<version>${docx4j.version}</version>
			</dependency>
			<dependency>
				<groupId>org.docx4j</groupId>
				<artifactId>docx4j-JAXB-Internal</artifactId>
				<version>${docx4j.version}</version>
			</dependency>
			<dependency>
				<groupId>org.docx4j</groupId>
				<artifactId>docx4j-export-fo</artifactId>
				<version>${docx4j.version}</version>
			</dependency>
			
			<dependency>
				<groupId>org.apache.poi</groupId>
				<artifactId>poi</artifactId>
				<version>${poi.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.poi</groupId>
				<artifactId>poi-ooxml</artifactId>
				<version>${poi.version}</version>
			</dependency>
			
			<dependency>
				<groupId>com.itextpdf</groupId>
				<artifactId>itext7-core</artifactId>
				<version>${itext.version}</version>
				<type>pom</type>
			</dependency>
			<dependency>
				<groupId>com.github.junrar</groupId>
				<artifactId>junrar</artifactId>
				<version>4.0.0</version>
			</dependency>
			<dependency>
				<groupId>com.github.axet</groupId>
				<artifactId>java-unrar</artifactId>
				<version>1.7.0-8</version>
			</dependency>
			<dependency>
				<groupId>net.sf.sevenzipjbinding</groupId>
				<artifactId>sevenzipjbinding</artifactId>
				<version>${sevenzipjbinding.version}</version>
			</dependency>
			<dependency>
				<groupId>net.sf.sevenzipjbinding</groupId>
				<artifactId>sevenzipjbinding-all-platforms</artifactId>
				<version>${sevenzipjbinding.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.ant</groupId>
				<artifactId>ant</artifactId>
				<version>${ant.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-fund-client</artifactId>
				<version>${com.howbuy.howbuy-fund-client.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.curator</groupId>
				<artifactId>curator-x-discovery-server</artifactId>
				<version>${curator-server.version}</version>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<distributionManagement>
		<repository>
			<id>howbuy-release</id>
			<name>howbuy-release</name>
			<url>http://nx-java.howbuy.pa/repository/howbuy-release/</url>
		</repository>
		<snapshotRepository>
			<id>howbuy-snapshot</id>
			<name>howbuy-snapshot</name>
			<url>http://nx-java.howbuy.pa/repository/howbuy-snapshot/</url>
		</snapshotRepository>
	</distributionManagement>

</project>