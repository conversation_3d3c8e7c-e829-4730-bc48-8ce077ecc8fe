/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.service.facade.search.queryacctbalance;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.howbuy.cachemanagement.service.CacheService;
import com.howbuy.cachemanagement.service.CacheServiceImpl;
import com.howbuy.common.utils.DateUtil;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.interlayer.common.utils.DateUtils;
import com.howbuy.interlayer.product.enums.YesOrNoEnum;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.datasource.RouteHolder;
import com.howbuy.tms.common.enums.busi.*;
import com.howbuy.tms.common.enums.database.HighDbNavDivTypeEnum;
import com.howbuy.tms.common.enums.database.HighNavDivTypeEnum;
import com.howbuy.tms.common.enums.database.ProductTypeEnum;
import com.howbuy.tms.common.message.MessageSource;
import com.howbuy.tms.common.outerservice.cc.center.queryhboneno.QueryHbOneNoOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.*;
import com.howbuy.tms.common.threadpool.CommonThreadPool;
import com.howbuy.tms.common.utils.MathUtils;
import com.howbuy.tms.common.utils.MoneyUtil;
import com.howbuy.tms.common.utils.ReflectUtils;
import com.howbuy.tms.high.orders.dao.po.HighDealOrderDtlPo;
import com.howbuy.tms.high.orders.dao.po.SubCustBooksPo;
import com.howbuy.tms.high.orders.dao.po.SubscribeAmtDetailPo;
import com.howbuy.tms.high.orders.dao.vo.AckDealOrderInfo;
import com.howbuy.tms.high.orders.dao.vo.BalanceVo;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.*;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceResponse.BalanceBean;
import com.howbuy.tms.high.orders.facade.search.queryasset.HighFundAssetIncomeDomain;
import com.howbuy.tms.high.orders.service.business.calvaluedate.ProductValueDateCalService;
import com.howbuy.tms.high.orders.service.business.task.CheckBalanceAlarmTask;
import com.howbuy.tms.high.orders.service.business.task.QueryStructProductNavTask;
import com.howbuy.tms.high.orders.service.cacheservice.alarm.HighBalanceAlarmService;
import com.howbuy.tms.high.orders.service.cacheservice.querydirectbalance.QueryDirectBalanceCacheService;
import com.howbuy.tms.high.orders.service.config.CommonValueConfig;
import com.howbuy.tms.high.orders.service.facade.search.queryacctbalance.bean.OwnershipOrderDto;
import com.howbuy.tms.high.orders.service.facade.search.querycommon.BigUtil;
import com.howbuy.tms.high.orders.service.repository.*;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.AcctBalanceBaseInfoService;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.QueryAcctBalanceBaseParam;
import com.howbuy.tms.high.orders.service.service.custbooks.CustBooksService;
import com.howbuy.tms.high.orders.service.service.queryasset.QueryAssetService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:查询产品持仓接口实现(整个高端持仓基础接口)
 * @reason:
 * @date 20189月21日 下午5:24:12
 * @since JDK 1.7
 */
@DubboService
@Service("queryAcctBalanceFacade")
@RefreshScope
public class QueryAcctBalanceFacadeService implements QueryAcctBalanceFacade {
    private static final Logger logger = LogManager.getLogger(QueryAcctBalanceFacadeService.class);

    private static Logger monitorLogger = LogManager.getLogger("monitorlog");

    @Autowired
    private CustBooksRepository custBooksRepository;
    @Autowired
    private CustBooksService custBooksService;
    @Autowired
    private AcctBalanceBaseInfoService acctBalanceBaseInfoService;

    @Autowired
    private HighDealOrderDtlRepository highDealOrderDtlRepository;

    @Autowired
    private CmCusttradeDirectRepository cmCusttradeDirectRepository;

    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;

    @Autowired
    private QueryHbOneNoOuterService queryHbOneNoOuterService;

    @Autowired
    private QueryDirectBalanceCacheService queryDirectBalanceCacheService;

    @Autowired
    private SubCustBooksRepository subCustBooksRepository;

    @Autowired
    private DealOrderRepository dealOrderRepository;

    @Autowired
    private ProductValueDateCalService productValueDateCalService;

    @Autowired
    private QueryAssetService queryAssetService;

    @Autowired
    private HighBalanceAlarmService highBalanceAlarmService;

    @Autowired
    private SubscribeAmtDetailRepository subscribeAmtDetailRepository;

    @Autowired
    private CommonValueConfig commonValueConfig;

    @Value("${noAlarmFundCodes}")
    private String noAlarmFundCodes;

    /**
     * 缓存服务
     */
    private static final CacheService CACHE_SERVICE = CacheServiceImpl.getInstance();


    /**
     * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceFacade.execute(QueryAcctBalanceRequest request)
     * @apiVersion 1.0.0
     * @apiGroup QueryAcctBalanceFacadeService
     * @apiName execute
     * @apiDescription 查询产品持仓接口实现(整个高端持仓基础接口)
     * @apiParam (请求参数) {String} hkSaleFlag 好买香港代销标识
     * @apiParam (请求参数) {String} productCode 产品代码
     * @apiParam (请求参数) {String} productType 产品类型
     * @apiParam (请求参数) {String} productSubType 产品子类型
     * @apiParam (请求参数) {String} protocolType 协议类型，4-高端产品协议
     * @apiParam (请求参数) {Array} disCodeList 分销机构代码列表-股权直销改造
     * @apiParam (请求参数) {String} callType 1-新资产中心      2-老资产中心
     * @apiParam (请求参数) {String} balanceStatus 兼容老逻辑,注意,字段不传也是查持仓的      持仓状态,0:不持仓,1:持仓,,2:全部      默认查持仓的
     * @apiParam (请求参数) {String} notFilterHkFund 不过滤香港产品,1:是,0:否
     * @apiParam (请求参数) {String} notFilterHzFund 不过滤好臻产品,1:是,0:否
     * @apiParam (请求参数) {String} txAcctNo 交易账号
     * @apiParam (请求参数) {String} hbOneNo 一账通账号
     * @apiParam (请求参数) {String} disCode 分销机构代码
     * @apiParam (请求参数) {String} outletCode 网点代码
     * @apiParam (请求参数) {String} appDt 申请日期
     * @apiParam (请求参数) {String} appTm 申请时间
     * @apiParam (请求参数) {Number} pageNo 页码
     * @apiParam (请求参数) {Number} pageSize 每页记录数
     * @apiParam (请求参数) {String} operIp 交易IP
     * @apiParam (请求参数) {String} txCode 交易码
     * @apiParam (请求参数) {String} txChannel 交易渠道 1-柜台,2-网站,3-电话,4-Wap,5-App,6-机构
     * @apiParam (请求参数) {String} dataTrack 数据跟踪
     * @apiParam (请求参数) {String} subOutletCode 子网点代码
     * @apiParamExample 请求参数示例
     * notFilterHzFund=vNsAEo&hbOneNo=5&balanceStatus=wC3US6&pageSize=8573&protocolType=86&disCode=9z7IdwB&txChannel=48&productSubType=NAxsZnfYMM&callType=nVZ&appTm=0qsszMLX3k&productCode=QtF0B&disCodeList=4DfmV8lQK&subOutletCode=pe&pageNo=3389&operIp=QI&txAcctNo=2qjgb1j&appDt=npScAT&dataTrack=eCV&hkSaleFlag=LAnSXdLs0Y&notFilterHkFund=YxePG6um6i&txCode=SFglAH&productType=VB5TP&outletCode=OVcFX2HrXM
     * @apiSuccess (响应结果) {Array} disCodeList 分销机构号列表-股权直销改造
     * @apiSuccess (响应结果) {String} txAcctNo 交易账号
     * @apiSuccess (响应结果) {Number} totalMarketValue 总市值
     * @apiSuccess (响应结果) {Number} totalUnconfirmedAmt 在途总金额
     * @apiSuccess (响应结果) {Number} totalUnconfirmedNum 待确认笔数
     * @apiSuccess (响应结果) {Number} redeemUnconfirmedNum 赎回待确认笔数
     * @apiSuccess (响应结果) {Number} totalCurrentAsset 当前总收益
     * @apiSuccess (响应结果) {String} totalIncomCalStat 总收益计算状态: 0-计算中;1-计算成功
     * @apiSuccess (响应结果) {Number} totalCashCollection 总回款
     * @apiSuccess (响应结果) {String} hasHZProduct 是否持有好臻产品 0:没有,1:有
     * @apiSuccess (响应结果) {String} hasHKProduct 是否持有好买香港产品  0:没有,1:有
     * @apiSuccess (响应结果) {Array} balanceList 持仓明细列表
     * @apiSuccess (响应结果) {String} balanceList.disCode 分销代码
     * @apiSuccess (响应结果) {Array} balanceList.disCodeList 分销代码列表
     * @apiSuccess (响应结果) {String} balanceList.productCode 产品代码
     * @apiSuccess (响应结果) {String} balanceList.subProductCode 子产品代码
     * @apiSuccess (响应结果) {String} balanceList.productName 产品名称
     * @apiSuccess (响应结果) {String} balanceList.productType 产品类型
     * @apiSuccess (响应结果) {String} balanceList.productSubType 产品子类型(好买产品线)
     * @apiSuccess (响应结果) {Number} balanceList.balanceVol 总份额
     * @apiSuccess (响应结果) {Number} balanceList.unconfirmedVol 待确认份额
     * @apiSuccess (响应结果) {Number} balanceList.unconfirmedAmt 待确认金额
     * @apiSuccess (响应结果) {String} balanceList.currency 币种
     * @apiSuccess (响应结果) {Number} balanceList.nav 净值
     * @apiSuccess (响应结果) {String} balanceList.navDt 净值日期
     * @apiSuccess (响应结果) {String} balanceList.navDivFlag 净值分红标识 0-否，1-是
     * @apiSuccess (响应结果) {Number} balanceList.marketValue 市值
     * @apiSuccess (响应结果) {Number} balanceList.currencyMarketValue 当前币种的市值
     * @apiSuccess (响应结果) {String} balanceList.scaleType 销售类型: 1-直销;2-代销
     * @apiSuccess (响应结果) {String} balanceList.hkSaleFlag 好买香港代销标识: 0-否; 1-是
     * @apiSuccess (响应结果) {String} balanceList.StageEstablishFlag 分期成立标识(证券类有此标识:0-否,1-是)
     * @apiSuccess (响应结果) {String} balanceList.fractionateCallFlag 分次call标识(股权类有此标识:0-否,1-是)
     * @apiSuccess (响应结果) {String} balanceList.fundCXQXStr 产品存续期限(类似于5+3+2这种说明)
     * @apiSuccess (响应结果) {Number} balanceList.netBuyAmount 净购买金额(投资成本)
     * @apiSuccess (响应结果) {Number} balanceList.currencyNetBuyAmount 净购买金额(投资成本)(当前币种)
     * @apiSuccess (响应结果) {Number} balanceList.paidInAmt 认缴金额
     * @apiSuccess (响应结果) {String} balanceList.incomeDt 收益日期
     * @apiSuccess (响应结果) {String} balanceList.incomeCalStat 0-计算中；1-计算完成
     * @apiSuccess (响应结果) {Number} balanceList.currentAsset 当前收益(人民币）
     * @apiSuccess (响应结果) {Number} balanceList.currentAssetCurrency 当前收益（当前币种）
     * @apiSuccess (响应结果) {Number} balanceList.accumIncome 累计收益
     * @apiSuccess (响应结果) {Number} balanceList.accumIncomeRmb 累计收益(人民币)
     * @apiSuccess (响应结果) {Number} balanceList.accumRealizedIncome 累计已实现收益
     * @apiSuccess (响应结果) {Number} balanceList.accumRealizedIncomeRmb 累计已实现收益人民币
     * @apiSuccess (响应结果) {String} balanceList.rePurchaseFlag 是否复构 0-否 1-是
     * @apiSuccess (响应结果) {String} balanceList.benchmark 业绩比较基准
     * @apiSuccess (响应结果) {String} balanceList.benchmarkType 业绩比较基准类型：0-业绩比较基准（年化） 1-业绩报酬计提基准（年化）
     * @apiSuccess (响应结果) {String} balanceList.valueDate 起息日
     * @apiSuccess (响应结果) {String} balanceList.dueDate 到期日
     * @apiSuccess (响应结果) {String} balanceList.standardFixedIncomeFlag 标准固收标识(固收类有此标识:0-非标准固收，1-标准固收，2-现金管理， 3-券商集合理财 4-纯债产品)
     * @apiSuccess (响应结果) {String} balanceList.investmentHorizon
     * @apiSuccess (响应结果) {String} balanceList.cooperation
     * @apiSuccess (响应结果) {String} balanceList.crisisFlag
     * @apiSuccess (响应结果) {Number} balanceList.yieldIncome
     * @apiSuccess (响应结果) {String} balanceList.yieldIncomeDt
     * @apiSuccess (响应结果) {Number} balanceList.copiesIncome 万份收益
     * @apiSuccess (响应结果) {String} balanceList.hwSaleFlag 是否海外产品 0-否 1-是
     * @apiSuccess (响应结果) {String} balanceList.regDt 登记日期
     * @apiSuccess (响应结果) {String} balanceList.oneStepType 一级监管分类
     * @apiSuccess (响应结果) {String} balanceList.twoStepType 二级监管分类
     * @apiSuccess (响应结果) {String} balanceList.secondStepType 三级监管分类
     * @apiSuccess (响应结果) {String} balanceList.productSaleType 产品销售类型 0-好买 1-海外 2-其他
     * @apiSuccess (响应结果) {String} balanceList.naProductFeeType NA产品收费类型 10201-好买收费 0-管理人收费
     * @apiSuccess (响应结果) {Number} balanceList.receivManageFee 累计应收管理费
     * @apiSuccess (响应结果) {Number} balanceList.receivPreformFee 累计应收业绩报酬
     * @apiSuccess (响应结果) {Number} balanceList.currencyMarketValueExFee NA产品费后市值(当前币种， 减去当前累计应收管理费和累计应收业绩报酬)
     * @apiSuccess (响应结果) {Number} balanceList.marketValueExFee NA产品费后市值(人民币， 减去当前累计应收管理费和累计应收业绩报酬)
     * @apiSuccess (响应结果) {Number} balanceList.balanceIncomeNew 当前收益（股权新算法）不含费
     * @apiSuccess (响应结果) {Number} balanceList.balanceIncomeNewRmb 当前收益（股权新算法）不含费-人民币
     * @apiSuccess (响应结果) {Number} balanceList.balanceFactor 平衡因子
     * @apiSuccess (响应结果) {String} balanceList.convertFinish 平衡因子转换完成 1-是 0-否
     * @apiSuccess (响应结果) {String} balanceList.balanceFactorDate 平衡因子日期
     * @apiSuccess (响应结果) {Number} balanceList.yieldRate 收益率
     * @apiSuccess (响应结果) {Number} balanceList.accumIncomeNew 累计收益（股权固收新算法）不含费
     * @apiSuccess (响应结果) {Number} balanceList.accumIncomeNewRmb 累计收益（股权固收新算法）不含费-人民币
     * @apiSuccess (响应结果) {Number} balanceList.balanceCost 持仓总成本(人民币）
     * @apiSuccess (响应结果) {Number} balanceList.balanceCostCurrency 持仓总成本（当前币种）
     * @apiSuccess (响应结果) {Number} balanceList.dailyAsset 日收益(人民币）
     * @apiSuccess (响应结果) {Number} balanceList.dailyAssetCurrency 日收益（当前币种）
     * @apiSuccess (响应结果) {Number} balanceList.cashCollection 私募股权回款
     * @apiSuccess (响应结果) {Number} balanceList.currencyCashCollection 私募股权回款(当前币种)
     * @apiSuccess (响应结果) {Number} balanceList.accumYieldRate
     * @apiSuccess (响应结果) {Number} balanceList.accumCost
     * @apiSuccess (响应结果) {Number} balanceList.accumCostRmb
     * @apiSuccess (响应结果) {Number} balanceList.balanceFloatIncome
     * @apiSuccess (响应结果) {Number} balanceList.balanceFloatIncomeRmb
     * @apiSuccess (响应结果) {Number} balanceList.balanceFloatIncomeRate
     * @apiSuccess (响应结果) {Number} balanceList.dayAssetRate
     * @apiSuccess (响应结果) {Number} balanceList.dayIncomeGrowthRate
     * @apiSuccess (响应结果) {Number} balanceList.accumCostNew
     * @apiSuccess (响应结果) {Number} balanceList.accumCostRmbNew
     * @apiSuccess (响应结果) {Number} balanceList.balanceAmt
     * @apiSuccess (响应结果) {Number} balanceList.balanceAmtRmb
     * @apiSuccess (响应结果) {Number} balanceList.accumCollection
     * @apiSuccess (响应结果) {Number} balanceList.accumCollectionRmb
     * @apiSuccess (响应结果) {Number} balanceList.balanceAmtExFee
     * @apiSuccess (响应结果) {Number} balanceList.balanceAmtExFeeRmb
     * @apiSuccess (响应结果) {String} balanceList.sxz
     * @apiSuccess (响应结果) {Number} balanceList.currentIncome 当前收益(当前币种）
     * @apiSuccess (响应结果) {Number} balanceList.currentIncomeRmb 当前收益(人民币）
     * @apiSuccess (响应结果) {Number} balanceList.currentAccumIncome 当前累计收益(当前币种）
     * @apiSuccess (响应结果) {Number} balanceList.currentAccumIncomeRmb 当前累计收益(人民币）
     * @apiSuccess (响应结果) {String} balanceList.stageFlag 是否拆单产品 1-是
     * @apiSuccess (响应结果) {String} balanceList.establishDt 产品成立日期
     * @apiSuccess (响应结果) {String} balanceList.assetUpdateDate 收益计算日期
     * @apiSuccess (响应结果) {Number} balanceList.unitBalanceCostExFee 单位持仓成本去费
     * @apiSuccess (响应结果) {Number} balanceList.unitBalanceCostExFeeRmb 单位持仓成本去费(人民币)
     * @apiSuccess (响应结果) {String} balanceList.ownershipTransferIdentity 股权转让标识
     * @apiSuccess (响应结果) {String} balanceList.sfhwcxg 是否海外储蓄罐(1:是;0:否)
     * @apiSuccess (响应结果) {String} balanceList.cpqxsm 股权产品期限说明
     * @apiSuccess (响应结果) {String} balanceList.navDisclosureType 净值披露方式(1-净值,2-份额收益)--持仓特殊产品指标控制需求新增 20221122
     * @apiSuccess (响应结果) {String} balanceList.abnormalFlag 对于 阳光私募/非货基型券商集合 产品，若【最新净值日期】比第一笔交易的【确认日期】早3个月以上          异常标志(0-否 1-是)--持仓特殊产品指标控制需求新增 20221122
     * @apiSuccess (响应结果) {String} balanceList.marketValueCtl 人民币市值-是否控制表人为置空(0-否 1-是)
     * @apiSuccess (响应结果) {String} balanceList.currencyMarketValueCtl 当前币种市值-是否控制表人为置空
     * @apiSuccess (响应结果) {String} balanceList.currencyMarketValueExFeeCtl NA产品费后市值（当前币种）-是否控制表人为置空
     * @apiSuccess (响应结果) {String} balanceList.marketValueExFeeCtl NA产品费后市值（人民币）-是否控制表人为置空
     * @apiSuccess (响应结果) {String} balanceList.currentAssetCtl 人民币收益-是否控制表人为置空
     * @apiSuccess (响应结果) {String} balanceList.currentAssetCurrencyCtl 当前币种收益-是否控制表人为置空
     * @apiSuccess (响应结果) {String} balanceList.dailyAssetCtl 人民币日收益-是否控制表人为置空
     * @apiSuccess (响应结果) {String} balanceList.dailyAssetCurrencyCtl 当前币种日收益-是否控制表人为置空
     * @apiSuccess (响应结果) {String} balanceList.accumIncomeCtl 累计收益-是否控制表人为置空
     * @apiSuccess (响应结果) {String} balanceList.accumIncomeRmbCtl 累计收益人民币-是否控制表人为置空
     * @apiSuccess (响应结果) {String} balanceList.accumRealizedIncomeCtl 累计已实现收益-是否控制表人为置空
     * @apiSuccess (响应结果) {String} balanceList.accumRealizedIncomeRmbCtl 累计已实现收益人民币-是否控制表人为置空
     * @apiSuccess (响应结果) {String} balanceList.balanceIncomeNewCtl 当前收益（股权新算法）不含费-是否控制表人为置空
     * @apiSuccess (响应结果) {String} balanceList.balanceIncomeNewRmbCtl 当前收益（股权新算法）不含费人民币-是否控制表人为置空
     * @apiSuccess (响应结果) {String} balanceList.accumIncomeNewCtl 累计收益（股权固收新算法）不含费-是否控制表人为置空
     * @apiSuccess (响应结果) {String} balanceList.accumIncomeNewRmbCtl 累计收益（股权固收新算法）不含费人民币-是否控制表人为置空
     * @apiSuccess (响应结果) {String} balanceList.yieldRateCtl 收益率-是否控制表人为置空
     * @apiSuccess (响应结果) {String} balanceList.balanceVolCtl 份额-是否控制表人为置空
     * @apiSuccess (响应结果) {String} balanceList.unconfirmedVolCtl 待确认份额-是否控制表人为置空
     * @apiSuccess (响应结果) {String} balanceList.navCtl 净值-是否控制表人为置空
     * @apiSuccess (响应结果) {String} balanceList.qianXiFlag 是否为千禧年产品 0-否、1-是
     * @apiSuccess (响应结果) {Number} balanceList.unPaidInAmt 待投金额（人民币）
     * @apiSuccess (响应结果) {Number} balanceList.currencyUnPaidInAmt 待投金额（当前币种）
     * @apiSuccess (响应结果) {Array} unconfirmeProducts
     * @apiSuccess (响应结果) {String} unconfirmeProducts.fundCode 产品代码
     * @apiSuccess (响应结果) {String} unconfirmeProducts.productType 产品类型
     * @apiSuccess (响应结果) {String} unconfirmeProducts.productSubType 产品子类型
     * @apiSuccess (响应结果) {Number} unconfirmeProducts.unconfirmedAmt 待确认金额(人民币)
     * @apiSuccess (响应结果) {String} unconfirmeProducts.hkSaleFlag 好买香港代销标识: 0-否; 1-是
     * @apiSuccess (响应结果) {String} unconfirmeProducts.disCode 销售渠道
     * @apiSuccess (响应结果) {String} returnCode 返回码
     * @apiSuccess (响应结果) {String} description 描述
     * @apiSuccess (响应结果) {Number} totalCount 总记录数
     * @apiSuccess (响应结果) {Number} totalPage 总页数
     * @apiSuccess (响应结果) {Number} pageNo 当前页
     * @apiSuccessExample 响应结果示例
     * {"hasHZProduct":"ubo0ddLkJ","totalUnconfirmedAmt":6111.771175209467,"totalPage":6879,"totalUnconfirmedNum":9118,"totalCashCollection":4478.549346450369,"totalMarketValue":5085.953316580214,"description":"vDQ","totalCount":3414,"returnCode":"nvyog","disCodeList":["k"],"unconfirmeProducts":[{"fundCode":"snN","unconfirmedAmt":3803.0211664444646,"disCode":"Qp","hkSaleFlag":"1CrE0Uu2pB","productSubType":"Kz4S","productType":"gocyPdyt"}],"pageNo":7163,"txAcctNo":"5zotik","balanceList":[{"balanceCostCurrency":8691.09372269286,"accumIncomeRmbCtl":"rOsUh0Y9I8","dailyAsset":2673.9788794821206,"currencyCashCollection":398.19746855141227,"disCode":"1qw","dailyAssetCurrency":5728.982907297374,"accumIncomeNewRmbCtl":"spNPJDLPhz","balanceIncomeNewRmbCtl":"0jP","productSubType":"s3OoF","accumRealizedIncomeCtl":"i0NvWZRQ","productName":"JEqyhStFz","balanceIncomeNewRmb":9627.582558008997,"currentAsset":7899.1961646203,"oneStepType":"bFMJUt5","accumIncomeNewRmb":730.9005309877126,"accumYieldRate":5986.963636785905,"productSaleType":"A4p5M","cashCollection":8049.879562653798,"currentAccumIncome":2438.829790274505,"balanceFloatIncomeRate":9012.144152709945,"cooperation":"J94a","nav":2592.5939095497465,"navDt":"Wuf","accumRealizedIncome":4943.***********,"abnormalFlag":"kh","unconfirmedVolCtl":"oNtZgvHRg","dayAssetRate":8855.***********,"benchmark":"UvlRhFJ","regDt":"PVvGT33k","netBuyAmount":8046.089110565013,"balanceAmtExFeeRmb":6807.4650993758705,"balanceFloatIncome":3105.0689650405584,"accumIncomeNew":9755.160212414268,"currencyMarketValueExFee":7506.717863238405,"unPaidInAmt":3545.4329864617016,"dayIncomeGrowthRate":8785.078440105555,"marketValueExFeeCtl":"B1BJr","establishDt":"ye4d1rUNT","currencyMarketValue":1392.908011753885,"navDisclosureType":"Z3LId5T","assetUpdateDate":"sGNgAX","currency":"4curOADaG","balanceFactor":7557.691832571883,"accumIncomeRmb":9049.327088058802,"hwSaleFlag":"bYbp","standardFixedIncomeFlag":"hP9aw7Y","yieldIncome":4755.364890396418,"balanceVolCtl":"cRak","balanceIncomeNew":5230.335101794674,"currentAccumIncomeRmb":883.1378356620834,"naProductFeeType":"c0","qianXiFlag":"S0FU","incomeCalStat":"RXaNsIC","yieldRate":4273.500765147252,"accumCollectionRmb":8669.***********,"productCode":"o3m","paidInAmt":6421.519491337989,"currentAssetCurrency":382.3347887293138,"accumCollection":4177.9810382683245,"copiesIncome":1903.6948983503298,"dailyAssetCtl":"H","accumIncomeNewCtl":"G9nWThBQR","crisisFlag":"69kT","dueDate":"kK","investmentHorizon":"ipwgY","marketValueExFee":8282.346242521939,"disCodeList":["lkaWFZ3a"],"accumCost":6352.469898677976,"balanceAmtExFee":3312.2671741345475,"StageEstablishFlag":"5kVPwn3l","incomeDt":"xeGv7L4","balanceAmtRmb":490.7182638879248,"balanceIncomeNewCtl":"FB","receivPreformFee":338.1960203480361,"accumIncome":3948.7155299668807,"yieldRateCtl":"LElQ7Og7pQ","currencyNetBuyAmount":9116.146933837626,"currencyUnPaidInAmt":361.8327588501136,"rePurchaseFlag":"ngXxc","ownershipTransferIdentity":"2","unitBalanceCostExFee":7851.116212852519,"sxz":"TEZI","scaleType":"3Z4In8","currentAssetCtl":"EP0nGSF","currencyMarketValueCtl":"kJJA","navCtl":"lPtY4kGc2E","benchmarkType":"j1mdFnDG","accumIncomeCtl":"EltOBo","balanceFactorDate":"6UrJ7w01D","currencyMarketValueExFeeCtl":"cwAcvUQ","secondStepType":"SsJ6tn","navDivFlag":"E7g6lUih","fundCXQXStr":"Wk","currentIncome":6525.371641222509,"balanceVol":663.7267815476644,"marketValueCtl":"tbS","balanceAmt":7266.922634810486,"balanceCost":9614.535451812,"yieldIncomeDt":"SHouB6pim","balanceFloatIncomeRmb":7275.614315905495,"hkSaleFlag":"us","sfhwcxg":"VECXYdKXWh","accumRealizedIncomeRmbCtl":"gczfJnuBUH","productType":"m9CNFUeLG","accumCostRmbNew":4124.683458154883,"cpqxsm":"wTbw","subProductCode":"3KntdMWk","twoStepType":"M","accumCostRmb":4275.212146523384,"dailyAssetCurrencyCtl":"idrpUaOKq","marketValue":667.4606234464353,"valueDate":"oupqd7rZ","receivManageFee":4372.352774509722,"accumRealizedIncomeRmb":1100.484911539874,"currentIncomeRmb":5814.907465685174,"unitBalanceCostExFeeRmb":3817.556000149068,"unconfirmedVol":6425.779074435479,"unconfirmedAmt":8707.275989906051,"fractionateCallFlag":"PilxYz4E","currentAssetCurrencyCtl":"Qva","stageFlag":"HZ83cn","convertFinish":"ooaO3fAb0h","accumCostNew":7326.975363347431}],"hasHKProduct":"ABOF16VMGb","totalCurrentAsset":6859.994958840143,"redeemUnconfirmedNum":3881,"totalIncomCalStat":"8"}
     */
    @Override
    public QueryAcctBalanceResponse execute(QueryAcctBalanceRequest request) {
        // 1.参数变更设置
        resetParam(request);
        // 2.返回结果构建
        String disCode = request.getDisCode();
        List<String> disCodeList = getDisCodeList(request);
        String txAcctNo = request.getTxAcctNo();
        String hbOneNo = request.getHbOneNo();
        QueryAcctBalanceResponse response = new QueryAcctBalanceResponse();
        response.setTxAcctNo(txAcctNo);
        response.setDisCode(disCode);
        response.setDisCodeList(disCodeList);
        response.setReturnCode(ExceptionCodes.SUCCESS);
        response.setDescription(MessageSource.getMessageByCode(ExceptionCodes.SUCCESS));
        List<BalanceBean> balanceList = new ArrayList<>();
        response.setBalanceList(balanceList);
        if (StringUtils.isBlank(txAcctNo) && StringUtils.isBlank(hbOneNo)) {
            logger.error("QueryAcctBalanceFacadeService-execute,查询持仓,交易账号/一账通号都是空的,返回空结果");
            return response;
        }


        // 3.查询清盘中产品
        List<String> crisisFundList = acctBalanceBaseInfoService.getCrisisFundList();

        // 4.处理代销资产
        processConsignmentBalance(request, hbOneNo, txAcctNo, balanceList, crisisFundList);

        // 5.处理直销资产
        processDirectBalance(hbOneNo, txAcctNo, request, balanceList, crisisFundList);

        // 6.是否需要过滤掉,好臻/好买香港的产品
        filterBalanceInfoByAuth(response, balanceList, request);

        // 7.设置db配置
        processDbConfig(balanceList);

        // 8.设置集合特殊字段值
        processListSpecialField(balanceList);

        // 9.检查告警
        checkAlarm(txAcctNo, request.getHbOneNo(), balanceList, crisisFundList);

        // 10.通过产品代码批量查询特殊产品指标控制配置项
        Map<String, List<HighProductFieldControlBean>> fieldMap = getProductFieldControl(balanceList);

        // 11.资产汇总处理
        totalProcess(response, balanceList, fieldMap, request);

        // 12.获取在途资产
        getOnWay(request, disCodeList, txAcctNo, response);

        // 13.结果集排序
        sortBalance(balanceList);

        // 14.特殊产品指标控制处理（根据配置表，将对应属性置空）（特殊产品指标控制需求）20221123
        dealProductFieldControl(balanceList, fieldMap);

        return response;
    }


    private void resetParam(QueryAcctBalanceRequest request) {
        // 默认只查持仓的
        if (StringUtils.isBlank(request.getBalanceStatus())) {
            request.setBalanceStatus(YesOrNoEnum.YES.getCode());
        }

        if (StringUtils.isNotBlank(request.getIsAuth()) && YesOrNoEnum.NO.getCode().equals(request.getIsAuth())) {
            request.setNotFilterHkFund(YesOrNoEnum.NO.getCode());
            request.setNotFilterHzFund(YesOrNoEnum.NO.getCode());
        } else {
            if (StringUtils.isBlank(request.getNotFilterHkFund())) {
                request.setNotFilterHkFund(YesOrNoEnum.YES.getCode());
            }
            if (StringUtils.isBlank(request.getNotFilterHzFund())) {
                request.setNotFilterHzFund(YesOrNoEnum.YES.getCode());
            }
        }
    }


    /**
     * 如果未授权,就需要过滤掉,好臻/好买香港的产品
     *
     * @param response    返回实体
     * @param balanceList 所有的持仓产品信息
     */
    private void filterBalanceInfoByAuth(QueryAcctBalanceResponse response, List<BalanceBean> balanceList, QueryAcctBalanceRequest request) {
        // 如果是未授权
        Iterator<BalanceBean> iterator = balanceList.iterator();
        while (iterator.hasNext()) {
            BalanceBean balanceBean = iterator.next();
            // 过滤香港产品,并在返回实体标注该用户有香港产品
            if (YesOrNoEnum.YES.getCode().equals(balanceBean.getHkSaleFlag())) {
                response.setHasHKProduct(YesOrNoEnum.YES.getCode());
                if (YesOrNoEnum.NO.getCode().equals(request.getNotFilterHkFund())) {
                    iterator.remove();
                    continue;
                }
            }
            // 过滤好臻产品,并在返回实体中标注该用户有好臻产品
            if (DisCodeEnum.HZ.getCode().equals(balanceBean.getDisCode())) {
                response.setHasHZProduct(YesOrNoEnum.YES.getCode());
                if (YesOrNoEnum.NO.getCode().equals(request.getNotFilterHzFund())) {
                    iterator.remove();
                }
            }
        }
    }

    /**
     * 通过产品代码批量查询特殊产品指标控制配置项
     *
     * @param balanceList
     * @return
     */
    private Map<String, List<HighProductFieldControlBean>> getProductFieldControl(List<BalanceBean> balanceList) {
        // 通过产品代码批量查询特殊产品指标控制配置项（用于总市值/总收益产品剔除、接口返回值特殊产品指标置空） 特殊产品指标控制需求20221124
        Map<String, List<HighProductFieldControlBean>> fieldMap = new HashMap<>();
        // 获取产品代码list
        List<String> productCodeList = balanceList.stream().map(BalanceBean::getProductCode).collect(Collectors.toList());
        fieldMap = queryHighProductOuterService.queryProductFieldControlByFundCodeForMap(productCodeList);
        return fieldMap;
    }

    /**
     * 特殊产品指标控制处理（根据配置表，将对应属性置空）
     *
     * @param balanceList 1-市值类:
     *                    人民币市值	marketValue
     *                    当前币种市值	currencyMarketValue
     *                    NA产品费后市值（当前币种）	currencyMarketValueExFee
     *                    NA产品费后市值（人民币）	marketValueExFee
     *                    2-收益类:
     *                    人民币收益	currentAsset
     *                    当前币种收益	currentAssetCurrency
     *                    人民币日收益	dailyAsset
     *                    当前币种日收益	dailyAssetCurrency
     *                    累计收益	accumIncome
     *                    累计收益人民币	accumIncomeRmb
     *                    累计已实现收益	accumRealizedIncome
     *                    累计已实现收益人民币	accumRealizedIncomeRmb
     *                    当前收益（股权新算法）不含费	balanceIncomeNew
     *                    当前收益（股权新算法）不含费人民币	balanceIncomeNewRmb
     *                    累计收益（股权固收新算法）不含费	accumIncomeNew
     *                    累计收益（股权固收新算法）不含费人民币	accumIncomeNewRmb
     *                    收益率	yieldRate
     *                    3-份额类:
     *                    份额	balanceVol
     *                    待确认份额	unconfirmedVol
     *                    4-净值类:F
     *                    净值	nav
     *                    5-自定义类:
     *                    总份额 balanceVol
     *                    净值	nav
     */
    private void dealProductFieldControl(List<BalanceBean> balanceList, Map<String, List<HighProductFieldControlBean>> fieldMap) {
        try {
            if (CollectionUtils.isEmpty(balanceList)) {
                return;
            }
            for (BalanceBean bal : balanceList) {
                List<HighProductFieldControlBean> fieldList = fieldMap.get(bal.getProductCode());
                if (CollectionUtil.isNotEmpty(fieldList)) {
                    for (HighProductFieldControlBean field : fieldList) {
                        if (StringUtil.isEmpty(field.getField())) {
                            logger.warn("特殊产品指标控制配置信息异常，field为空，配置信息:{}", JSON.toJSONString(field));
                            continue;
                        }
                        try {
                            ReflectUtils.setValue(bal, field.getField(), null);
                            // 是否控制表人为置空
                            ReflectUtils.setValue(bal, field.getField() + "Ctl", "1");
                        } catch (Exception e) {
                            logger.warn("特殊产品指标置空异常，产品:{}，指标:{}，异常信息:{}", field.getFundCode(), field.getField(), e);
                            monitorLogger.info(JSON.toJSONString(getMsgMap(HighBalanceAlarmEnum.PRODUCT_5, field.getFundCode(), field.getField())));
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.warn("特殊产品指标控制处理异常:{}", e);
        }
    }

    private Map<String, String> getMsgMap(HighBalanceAlarmEnum alarmEnum, String fundCode, String field) {
        Map<String, String> msgMap = new HashMap<>();
        msgMap.put("time", DateUtils.formatToString(new Date(), DateUtils.YYYY_MM_DD_HH_MM_SS_SSS));
        String msg = alarmEnum.getName() + "，指标：" + field;
        msgMap.put("msg", msg);
        msgMap.put("fundCode", fundCode);
        msgMap.put("txAcctNo", null);
        msgMap.put("type", alarmEnum.getType());
        return msgMap;
    }

    /**
     * 设置特殊字段值
     *
     * @param balanceList
     */
    private void processListSpecialField(List<BalanceBean> balanceList) {

        if (CollectionUtils.isEmpty(balanceList)) {
            return;
        }
        for (BalanceBean bean : balanceList) {
            if (isFixIn(bean)) {
                bean.setCurrentIncome(bean.getBalanceIncomeNew());
                bean.setCurrentIncomeRmb(bean.getBalanceIncomeNewRmb());
                bean.setCurrentAccumIncome(bean.getAccumIncomeNew());
                bean.setCurrentAccumIncomeRmb(bean.getAccumIncomeNewRmb());
            } else {
                bean.setCurrentIncome(bean.getCurrentAssetCurrency());
                bean.setCurrentIncomeRmb(bean.getCurrentAsset());
                bean.setCurrentAccumIncome(bean.getAccumIncome());
                bean.setCurrentAccumIncomeRmb(bean.getAccumIncomeRmb());
            }
        }
    }

    private boolean isFixIn(BalanceBean bean) {
        String incomeFlag = bean.getStandardFixedIncomeFlag();
        String productCode = "PE0053";

        boolean flag = StandardFixedIncomeFlagEnum.NOT_STANDARD_GS.getCode().equals(incomeFlag) ||
                StandardFixedIncomeFlagEnum.STANDARD_GS.getCode().equals(incomeFlag) ||
                StandardFixedIncomeFlagEnum.CASH_MANAGER.getCode().equals(incomeFlag) ||
                StandardFixedIncomeFlagEnum.BOND_GS.getCode().equals(incomeFlag);

        if ((ProductDBTypeEnum.GUDINGSHOUYI.getCode().equals(bean.getProductSubType()) && flag) ||
                (ProductDBTypeEnum.GUQUAN.getCode().equals(bean.getProductSubType())) && !productCode.equals(bean.getProductCode())) {
            return true;
        }

        return false;
    }

    /**
     * 检查告警
     *
     * @param txAcctNo
     * @param balanceList
     */
    private void checkAlarm(String txAcctNo, String hbOneNo, List<BalanceBean> balanceList, List<String> crisisFundList) {
        try {
            if (CollectionUtils.isNotEmpty(balanceList)) {
                final CountDownLatch latch = new CountDownLatch(balanceList.size());
                for (BalanceBean balanceBean : balanceList) {
                    CommonThreadPool.submit(new CheckBalanceAlarmTask(balanceBean, highBalanceAlarmService, highDealOrderDtlRepository, txAcctNo, hbOneNo, latch, RouteHolder.getRouteKey(), crisisFundList, cmCusttradeDirectRepository, noAlarmFundCodes));
                }
                try {
                    latch.await();
                } catch (InterruptedException e) {
                    logger.error("QueryAcctBalanceFacadeService|latch.await exception.", e);
                    Thread.currentThread().interrupt();
                }
            }
        } catch (Exception e) {
            logger.warn("检查告警异常", e);
        }
    }

    /**
     * 设置db配置期限说明
     *
     * @param balanceList
     */
    private void processDbConfig(List<BalanceBean> balanceList) {
        if (CollectionUtils.isEmpty(balanceList)) {
            return;
        }
        Set<String> queryProductCodeSet = new HashSet<>();
        for (BalanceBean balanceBean : balanceList) {
            queryProductCodeSet.add(balanceBean.getProductCode());
        }
        // 批量查询产品基本信息
        Map<String, HighProductDBInfoBean> highProductDBInfoMap =
                queryHighProductOuterService.getHighProductDBInfoMap(new ArrayList<>(queryProductCodeSet));

        for (BalanceBean bean : balanceList) {
            HighProductDBInfoBean productDBInfoBean = highProductDBInfoMap.get(bean.getProductCode());
            if (productDBInfoBean != null) {
                bean.setCpqxsm(productDBInfoBean.getCpqxsm());
                bean.setSxz(productDBInfoBean.getNaProductFeeType());
                if (StringUtils.isNotEmpty(productDBInfoBean.getFundCXQXStr())) {
                    bean.setFundCXQXStr(productDBInfoBean.getFundCXQXStr());
                }
            }
        }
    }

    /**
     * @param request
     * @return java.util.List<java.lang.String>
     * @description:(获取分销机构代码)
     * @author: haiguang.chen
     * @date: 2022/1/4 11:04
     * @since JDK 1.8
     */
    private List<String> getDisCodeList(QueryAcctBalanceRequest request) {
        if (CollectionUtils.isNotEmpty(request.getDisCodeList())) {
            return request.getDisCodeList();
        } else {
            List<String> disCodeList = new ArrayList<>();
            disCodeList.add(request.getDisCode());
            request.setDisCodeList(disCodeList);
            return disCodeList;
        }
    }

    /**
     * 资产汇总处理
     *
     * @param response    返回实体
     * @param balanceList 持仓信息
     * @param fieldMap    产品信息
     */
    private void totalProcess(QueryAcctBalanceResponse response, List<BalanceBean> balanceList, Map<String, List<HighProductFieldControlBean>> fieldMap, QueryAcctBalanceRequest request) {
        // 汇总市值
        //总收益
        BigDecimal totalCurrentAsset = BigDecimal.ZERO;
        // 总市值
        BigDecimal totalMarketValue = BigDecimal.ZERO;
        // 总回款金额（人民币）
        BigDecimal totalCashCollection = BigDecimal.ZERO;
        // 总收益的计算状态
        String totalIncomCalStat = IncomeCalStatEnum.FINISHED.getCode();
        Set<String> hasProcessedFixedIncomeProductCodes = new HashSet<>();
        for (BalanceBean balanceBean : balanceList) {
            // 是否需要排除汇总 20221124
            // 产品存在于 特殊产品指标控制表中 ，或 abnormalFlag=1 ，均需在汇总时排除
            boolean needExclude = false;
            List<HighProductFieldControlBean> controlList = fieldMap.get(balanceBean.getProductCode());
            if (CollectionUtil.isNotEmpty(controlList)) {
                needExclude = true;
            }
            if (ProductDBTypeEnum.GUQUAN.getCode().equals(balanceBean.getProductSubType())) {
                balanceBean.setMarketValue(balanceBean.getNetBuyAmount());
                balanceBean.setCurrencyMarketValue(balanceBean.getCurrencyNetBuyAmount());
            }

            if (balanceBean.getMarketValue() != null && !needExclude && balanceBean.isBalance()) {
                totalMarketValue = totalMarketValue.add(balanceBean.getMarketValue());
            }
            // 千禧年产品需要将待投金额累计进总资产
            if ("1".equals(balanceBean.getQianXiFlag())) {
                if (balanceBean.getUnPaidInAmt() != null && !needExclude && balanceBean.isBalance()) {
                    totalMarketValue = totalMarketValue.add(balanceBean.getUnPaidInAmt());
                }
            }

            if (balanceBean.getCurrentAsset() != null && !needExclude && !canNotCountAssert(request, balanceBean.getProductCode())) {
                // 收益汇总，固收每个产品只汇总一次
                if (ProductDBTypeEnum.GUDINGSHOUYI.getCode().equals(balanceBean.getProductSubType())) {
                    if (!hasProcessedFixedIncomeProductCodes.contains(balanceBean.getProductCode())) {
                        totalCurrentAsset = totalCurrentAsset.add(balanceBean.getCurrentAsset());
                        hasProcessedFixedIncomeProductCodes.add(balanceBean.getProductCode());
                    }
                } else {
                    totalCurrentAsset = totalCurrentAsset.add(balanceBean.getCurrentAsset());
                }
            }
            if (balanceBean.getCashCollection() != null) {
                totalCashCollection = totalCashCollection.add(balanceBean.getCashCollection());
            }

            // 存在一笔持仓产品的收益是计算中，则总收益的收益状态是计算中
            if (IncomeCalStatEnum.PROCESSING.getCode().equals(balanceBean.getIncomeCalStat())
                    && IncomeCalStatEnum.FINISHED.getCode().equals(totalIncomCalStat)) {
                totalIncomCalStat = IncomeCalStatEnum.PROCESSING.getCode();
            }
        }
        response.setTotalIncomCalStat(totalIncomCalStat);
        // 四舍五入保留2位小数
        response.setTotalMarketValue(BigUtil.formatMoney(totalMarketValue, 2));
        // 四舍五入保留2位小数
        response.setTotalCurrentAsset(BigUtil.formatMoney(totalCurrentAsset, 2));

        // 总回款
        response.setTotalCashCollection(MoneyUtil.formatMoney(totalCashCollection, 2));

    }

    /**
     * 是否不需要计算收益
     *
     * @param request
     * @param balanceFundCode
     * @return true:不需要计算,false:需要计算
     */
    private boolean canNotCountAssert(QueryAcctBalanceRequest request, String balanceFundCode) {
        String notCountAssertFund = commonValueConfig.getNotCountAssertFund();
        logger.info("canNotCountAssert,不计算收益产品配置,notCountAssertFund={}", notCountAssertFund);
        // 没有配置不需要剔除
        if (StringUtils.isNotBlank(notCountAssertFund)) {
            List<String> notCountAssertFundList = Arrays.asList(notCountAssertFund.split(","));
            if (!notCountAssertFundList.contains(balanceFundCode)) {
                logger.info("canNotCountAssert-有配置,配置不包含,需要计算总收益,balanceFundCode={}", balanceFundCode);
                return false;
            }
        } else {
            logger.info("canNotCountAssert-无配置,需要计算总收益,balanceFundCode={}", balanceFundCode);
            return false;
        }
        // 资产详情页不需要剔除
        if (StringUtils.isNotBlank(request.getProductCode())) {
            logger.info("canNotCountAssert-资产详情页,需要计算总收益,balanceFundCode={}", balanceFundCode);
            return false;
        }
        if (StringUtils.isNotBlank(request.getProductSubType())) {
            logger.info("canNotCountAssert-非首页,可能是好臻专区,需要计算总收益,balanceFundCode={}", balanceFundCode);
            return false;
        }
        if (StringUtils.isNotBlank(request.getHkSaleFlag()) && YesOrNoEnum.YES.getCode().equals(request.getHkSaleFlag())) {
            logger.info("canNotCountAssert-香港专区,需要计算总收益,balanceFundCode={}", balanceFundCode);
            return false;
        }
        logger.info("其他情况,需要不需要计算总收益,balanceFundCode={}", balanceFundCode);
        return true;
    }

    /**
     * 获取在途资产相关信息
     *
     * @param request
     * @param disCodeList
     * @param txAcctNo
     * @param response
     */
    public void getOnWay(QueryAcctBalanceRequest request, List<String> disCodeList, String txAcctNo, QueryAcctBalanceResponse response) {
        List<UnconfirmeProduct> unconfirmeFundsTmp = custBooksService.getTotalUnconfirmedAmtByQueryAcctBalance(request.getHkSaleFlag(), request.getProductSubType(), disCodeList, txAcctNo, request.getNotFilterHkFund(), request.getNotFilterHzFund());
        List<UnconfirmeProduct> unconfirmeFunds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(unconfirmeFundsTmp)) {
            // 总待确认金额需要剔除储蓄罐预约冻结的在途
            Map<String, List<UnconfirmeProduct>> fundMap = unconfirmeFundsTmp.stream().collect(Collectors.groupingBy(UnconfirmeProduct::getFundCode));
            for (Map.Entry<String, List<UnconfirmeProduct>> fundEntry : fundMap.entrySet()) {
                BigDecimal cxgDjZt = highDealOrderDtlRepository.getCxgDjZt(Collections.singletonList(fundEntry.getKey()), txAcctNo);
                // 该产品的未确认数据
                List<UnconfirmeProduct> subList = fundEntry.getValue();
                UnconfirmeProduct unconfirmeProduct = subList.get(0);
                // 求和
                BigDecimal totalAmount = subList.stream().map(UnconfirmeProduct::getUnconfirmedAmt).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (totalAmount.compareTo(cxgDjZt) >= 0) {
                    totalAmount = totalAmount.subtract(cxgDjZt);
                }
                unconfirmeProduct.setUnconfirmedAmt(totalAmount);
                unconfirmeFunds.add(unconfirmeProduct);
            }
        }
        unconfirmeFunds = unconfirmeFunds.stream().filter(x -> x.getUnconfirmedAmt() != null && x.getUnconfirmedAmt().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        response.setUnconfirmeProducts(unconfirmeFunds);
        BigDecimal totalUnconfirmedAmt = unconfirmeFunds.stream().map(UnconfirmeProduct::getUnconfirmedAmt).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add).abs();
        // 总市值，需要加上在途资产
        logger.info("QueryAcctBalanceFacadeService|getOnWay|totalMarketValue:{},totalUnconfirmedAmt:{},unConfirmFund:{}", response.getTotalMarketValue(), totalUnconfirmedAmt, JSON.toJSONString(unconfirmeFunds));
        response.setTotalMarketValue(BigUtil.formatMoney(response.getTotalMarketValue().add(totalUnconfirmedAmt), 2));
        // 总在途资产
        response.setTotalUnconfirmedAmt(BigUtil.formatMoney(totalUnconfirmedAmt, 2));

        // 未持仓,在途的不需要计算
        if (YesOrNoEnum.YES.getCode().equals(request.getBalanceStatus())) {
            // 在途交易总笔数
            response.setTotalUnconfirmedNum(dealOrderRepository.selectOnWayTradeNum(disCodeList, txAcctNo, request.getNotFilterHkFund(), request.getNotFilterHzFund()));

            // 赎回在途笔数
            response.setRedeemUnconfirmedNum(dealOrderRepository.selectRedeemOnWayTradeAuthNum(disCodeList, txAcctNo, request.getNotFilterHkFund(), request.getNotFilterHzFund()));

        } else {
            // 在途交易总笔数
            response.setTotalUnconfirmedNum(0);
            // 赎回在途笔数
            response.setRedeemUnconfirmedNum(0);
        }


    }

    /**
     * sortBalance:(根据productCode排序结果集)
     *
     * @param balanceList
     * <AUTHOR>
     * @date 2017年7月12日 上午10:27:15
     */
    private void sortBalance(List<BalanceBean> balanceList) {
        if (CollectionUtils.isEmpty(balanceList)) {
            return;
        }

        Collections.sort(balanceList);
    }

    /**
     * processConsignmentBalance:(处理代销资产)
     *
     * @param request
     * @param txAcctNo
     * @param balanceList
     * @param crisisFundList 清盘中产品
     * <AUTHOR>
     * @date 2017年7月11日 下午4:58:58
     */
    private void processConsignmentBalance(QueryAcctBalanceRequest request, String hbOneNo, String txAcctNo, List<BalanceBean> balanceList, List<String> crisisFundList) {
        if (StringUtils.isEmpty(txAcctNo)) {
            txAcctNo = queryHbOneNoOuterService.queryTxAcctNoByHbOneNo(hbOneNo);
            request.setTxAcctNo(txAcctNo);
        }
        if (StringUtils.isEmpty(txAcctNo)) {
            logger.warn("QueryAcctBalanceFacadeService|processConsignmentBalance() txAcctNo is null, hbOneNo:{}", hbOneNo);
            return;
        }
        if (StringUtils.isEmpty(hbOneNo)) {
            hbOneNo = queryHbOneNoOuterService.queryHbOneNoByTxAcctNo(txAcctNo);
            request.setHbOneNo(hbOneNo);
        }
        if (StringUtils.isEmpty(hbOneNo)) {
            logger.warn("QueryAcctBalanceFacadeService|processDirectBalance() hbOneNo is null, txAcctNo:{}", txAcctNo);
            return;
        }
        // 后面告警方法，会用到request.hbOneNo 20221201
        request.setHbOneNo(hbOneNo);

        // 查询客户代销持仓，基于产品维度
        List<BalanceVo> list = custBooksRepository.selectBalanceWithLockPeriod(request.getDisCodeList(), txAcctNo, StringUtils.isNotBlank(request.getProductCode()) ? Collections.singletonList(request.getProductCode()) : null, request.getBalanceStatus());
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        // 20230810,应业务方要求,将特定用户:1297653362,的产品PE0054,持仓不展示
        if ("1297653362".equals(txAcctNo) || "8003718407".equals(hbOneNo)) {
            list = list.stream().filter(x -> !"PE0054".equals(x.getProductCode())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        // 持仓产品(份额=0)代码Set
        Set<String> unBalanceFundCodeSet = new HashSet<String>();
        for (BalanceVo balanceVo : list) {
            if (BigDecimal.ZERO.compareTo(balanceVo.getBalanceVol()) == 0) {
                unBalanceFundCodeSet.add(balanceVo.getProductCode());
            }
        }
        // 支持查询清仓的产品后,可能查询出原先有付款,但是后续撤单了,这条记录会出现在cust_books中,当做清仓的数据查出来了,需要将这部分数据过滤掉
        list = unBalanceListFilter(unBalanceFundCodeSet, txAcctNo, list);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        // 找出所有产品编码
        Set<String> queryProductCodeSet = new HashSet<String>();
        for (BalanceVo balanceVo : list) {
            queryProductCodeSet.add(balanceVo.getProductCode());
        }
        // 批量查询产品基本信息
        Map<String, HighProductDBInfoBean> highProductDBInfoBeanMap = queryHighProductOuterService.getHighProductDBInfoMap(new ArrayList<>(queryProductCodeSet));
        // 根据请求条件过滤产品列表
        processProductDBInfoMap(highProductDBInfoBeanMap, request);

        // 查询拆单基金列表
        List<String> splitfundList = queryHighProductOuterService.queryRedeemSplitFund(null, null);

        // 非结构化产品(非债券分期成立的产品)处理
        List<BalanceBean> notStructureBalanceList = processNotStructureProduct(txAcctNo, hbOneNo, request.getDisCodeList(), highProductDBInfoBeanMap, list, crisisFundList, splitfundList, request.getBalanceStatus());
        balanceList.addAll(notStructureBalanceList);

        // 结构化产品(债券分期成立的产品)处理
        List<BalanceBean> structureBalanceList = processStructureProduct(txAcctNo, hbOneNo, request.getDisCodeList(), highProductDBInfoBeanMap, list, crisisFundList, splitfundList, request.getBalanceStatus());
        balanceList.addAll(structureBalanceList);

    }

    /**
     * 清仓产品需要过滤掉没有确认交易的
     *
     * @param unBalanceFundCodeSet 清仓产品编码
     * @param txAcctNo             交易账号
     * @param list                 持仓列表
     * @return 持仓列表
     */
    private List<BalanceVo> unBalanceListFilter(Set<String> unBalanceFundCodeSet, String txAcctNo, List<BalanceVo> list) {
        if (CollectionUtils.isEmpty(unBalanceFundCodeSet)) {
            logger.info("unBalanceListFilter-过滤掉没有确认的交易记录的产品清仓数据-没有清仓产品,不需要过滤");
            return list;
        }
        // 1.找出有份额为0的份额数据但是没有确认的交易记录的数据
        List<String> unConfirmedOrderFundCodeList = custBooksRepository.queryUnConfirmedOrderFundCode(unBalanceFundCodeSet, txAcctNo);
        if (CollectionUtils.isEmpty(unConfirmedOrderFundCodeList)) {
            logger.info("unBalanceListFilter-过滤掉没有确认的交易记录的产品清仓数据-没有不存在确认交易的清仓产品,不需要过滤");
            return list;
        }
        // 2.过滤出这部分产品的持仓数据,这部分不算清仓
        return list.stream().filter(x -> !unConfirmedOrderFundCodeList.contains(x.getProductCode())).collect(Collectors.toList());
    }


    /**
     * 根据请求条件过滤产品列表
     *
     * @param highProductDBInfoBeanMap
     * @param request
     */
    private void processProductDBInfoMap(Map<String, HighProductDBInfoBean> highProductDBInfoBeanMap, QueryAcctBalanceRequest request) {
        if (highProductDBInfoBeanMap == null || highProductDBInfoBeanMap.isEmpty()) {
            return;
        }

        if (StringUtils.isEmpty(request.getHkSaleFlag()) && StringUtils.isEmpty(request.getProductType()) && StringUtils.isEmpty(request.getProductSubType())) {
            return;
        }

        HighProductDBInfoBean highProductDBInfoBean = null;
        Iterator<String> ite = highProductDBInfoBeanMap.keySet().iterator();
        while (ite.hasNext()) {
            highProductDBInfoBean = highProductDBInfoBeanMap.get(ite.next());
            // 过滤香港代销
            if (!StringUtils.isEmpty(request.getHkSaleFlag())) {
                if (highProductDBInfoBean == null || !request.getHkSaleFlag().equals(highProductDBInfoBean.getHkSaleFlag())) {
                    if (!("0".equals(request.getHkSaleFlag()) && null == highProductDBInfoBean.getHkSaleFlag())) {
                        ite.remove();
                        continue;
                    }
                }
            }
            // 过滤产品类型
            if (!StringUtils.isEmpty(request.getProductType())) {
                if (highProductDBInfoBean == null || !request.getProductType().equals(highProductDBInfoBean.getFundType())) {
                    ite.remove();
                    continue;
                }
            }
            // 过滤产品子类型
            if (!StringUtils.isEmpty(request.getProductSubType())) {
                // 第一个字符是"-"，则代表的是，排除这个类型
                String firstChar = request.getProductSubType().substring(0, 1);
                if ("-".equals(firstChar)) {
                    String leftChars = request.getProductSubType().substring(1);
                    if (highProductDBInfoBean == null || leftChars.equals(highProductDBInfoBean.getFundSubType())) {
                        ite.remove();
                    }
                } else {
                    if (highProductDBInfoBean == null || !request.getProductSubType().equals(highProductDBInfoBean.getFundSubType())) {
                        ite.remove();
                    }
                }
            }

        }
    }

    /**
     * processStructureProduct:(结构化产品(债券分期成立的产品)处理)
     *
     * @param txAcctNo
     * @param hbOneNo
     * @param disCodeList
     * @param highProductDbInfoBeanMap
     * @param crisisFundList           清盘中产品
     * @param splitFundList
     * @return
     * <AUTHOR>
     * @date 2017年7月25日 下午3:57:11
     */
    private List<BalanceBean> processStructureProduct(String txAcctNo, String hbOneNo, List<String> disCodeList,
                                                      Map<String, HighProductDBInfoBean> highProductDbInfoBeanMap, List<BalanceVo> list,
                                                      List<String> crisisFundList, List<String> splitFundList, String balanceStatus) {
        List<BalanceBean> subBalanceList = new ArrayList<>();
        List<String> productCodeList = new ArrayList<>();
        // 获取非固收非股权产品
        Set<String> notGSAndGQFundCodeSet = getNotGsFundCode(highProductDbInfoBeanMap, list, productCodeList);
        logger.info("QueryAcctBalanceFacadeService|processStructureProduct|productCodeList:{}", JSON.toJSONString(productCodeList));
        if (CollectionUtils.isEmpty(productCodeList)) {
            return subBalanceList;
        }

        // 查询子账本表(开放赎回日期维度)
        List<SubCustBooksPo> subCustBooksPoList = new ArrayList<>();// 分期成立产品列表
        for (String productCode : productCodeList) {
            List<SubCustBooksPo> bookPolist = subCustBooksRepository.selectSubCustBooks(disCodeList, txAcctNo, productCode, null, balanceStatus);
            logger.info("QueryAcctBalanceFacadeService|productCode:{}, bookPolist:{}", productCode, JSON.toJSONString(bookPolist));
            subCustBooksPoList.addAll(bookPolist);
        }
        logger.info("QueryAcctBalanceFacadeService|subCustBooksPoList:{}", JSON.toJSONString(subCustBooksPoList));
        if (CollectionUtils.isEmpty(subCustBooksPoList)) {
            return subBalanceList;
        }
        // 子账本信息添加
        setSubBalanceInfo(disCodeList, highProductDbInfoBeanMap, splitFundList, subBalanceList, subCustBooksPoList);
        logger.info("QueryAcctBalanceFacadeService|subBalanceList:{}", JSON.toJSONString(subBalanceList));
        if (CollectionUtils.isEmpty(productCodeList)) {
            return subBalanceList;
        }

        // 批量获取基金净值map
        Map<String, HighProductNavBean> fundNavMap = getStructureNavMap(subBalanceList);

        // 批量获取基金分红信息map
        Map<String, HighProductNavDivBean> fundNavDivMap = getNavDivMap(new ArrayList<>(productCodeList));

        // 批量查询基金收益map, 一定要先获取净值(子产品代码), 再根据子产品代码获取收益信息
        Map<String, HighFundAssetIncomeDomain> currentAssetMap =
                queryAssetService.getCurrentAssetMap(getSubProductCodeList(subBalanceList), hbOneNo, disCodeList);

        // 查询非固收非股权最近确认日期确认的认申购订单
        Map<String, List<AckDealOrderInfo>> latestAckMap = acctBalanceBaseInfoService.getAgentAckDealDtlMap(txAcctNo, notGSAndGQFundCodeSet);

        // 设置返回信息中产品信息、净值信息和市值计算
        setProductInfoAndNavInfo(txAcctNo, hbOneNo, highProductDbInfoBeanMap, crisisFundList, subBalanceList, fundNavMap, fundNavDivMap, currentAssetMap, latestAckMap);

        return subBalanceList;
    }

    /**
     * 设置返回信息中产品信息、净值信息和市值计算
     */
    private void setProductInfoAndNavInfo(String txAcctNo, String hbOneNo, Map<String, HighProductDBInfoBean> highProductDbInfoBeanMap,
                                          List<String> crisisFundList, List<BalanceBean> subBalanceList,
                                          Map<String, HighProductNavBean> fundNavMap, Map<String, HighProductNavDivBean> fundNavDivMap,
                                          Map<String, HighFundAssetIncomeDomain> currentAssetMap, Map<String, List<AckDealOrderInfo>> latestAckMap) {
        for (BalanceBean bal : subBalanceList) {
            if (StringUtils.isBlank(bal.getSubProductCode())) {
                logger.error("分期成立的基金持仓,查询不到子基金代码,fundCode={}", bal.getProductCode());
                continue;
            }
            // 特殊产品指标控制需求：获取产品净值披露方式（用于市值计算方式判断） 20221122
            // 固定收益类产品，即【产品大类productSubType】=2-固定收益，且【净值披露方式】=2-份额收益，则【持仓总市值】=【持仓份额累计】* 1
            if (ProductDBTypeEnum.GUDINGSHOUYI.getCode().equals(bal.getProductSubType())) {
                HighProductDBInfoBean highProductDbInfoBean = highProductDbInfoBeanMap.get(bal.getProductCode());
                if (highProductDbInfoBean != null && highProductDbInfoBean.getNavDisclosureType() != null) {
                    bal.setNavDisclosureType(highProductDbInfoBean.getNavDisclosureType());
                }
            }

            // 净值和收益信息处理
            processNavAndAssetInfo(bal, true, fundNavMap, currentAssetMap, crisisFundList, latestAckMap, txAcctNo, hbOneNo, fundNavDivMap);

            // 收益计算状态
            bal.setIncomeCalStat(acctBalanceBaseInfoService.getIncomeCalStatus(bal, crisisFundList));
        }
    }

    /**
     * 获取非固收非股权产品
     */
    private Set<String> getNotGsFundCode(Map<String, HighProductDBInfoBean> highProductDbInfoBeanMap, List<BalanceVo> list, List<String> productCodeList) {
        HighProductDBInfoBean productBean;
        Set<String> notGSAndGQFundCodeSet = new HashSet<>();
        for (BalanceVo vo : list) {
            productBean = highProductDbInfoBeanMap.get(vo.getProductCode());
            if (productBean == null) {
                // 这里要么是产品中心没有查到,要么是前面产品过滤的时候给过滤了
                logger.info("processStructureProduct-遍历持仓产品信息,匹配产品中心查询结果,匹配不上,fundCode={}", vo.getProductCode());
                continue;
            }
            if (StageEstablishFlagEnum.STAGE.getCode().equals(productBean.getStageEstablishFlag())) {
                productCodeList.add(productBean.getFundCode());
            }

            // 是否净值化产品
            if (isNavTypeProduct(productBean)) {
                notGSAndGQFundCodeSet.add(vo.getProductCode());
            }
        }
        return notGSAndGQFundCodeSet;
    }

    /**
     * 子账本信息添加
     */
    private void setSubBalanceInfo(List<String> disCodeList, Map<String, HighProductDBInfoBean> highProductDbInfoBeanMap, List<String> splitFundList, List<BalanceBean> subBalanceList, List<SubCustBooksPo> subCustBooksPoList) {
        HighProductDBInfoBean productBean;
        for (SubCustBooksPo book : subCustBooksPoList) {
            productBean = highProductDbInfoBeanMap.get(book.getFundCode());
            if (productBean == null) {
                logger.warn("QueryAcctBalanceFacadeService|processStructureProduct|fundCode:{}, productBean is null!", book.getFundCode());
                continue;
            }
            BalanceBean bean = new BalanceBean();
            bean.setDisCode(book.getDisCode());
            bean.setDisCodeList(disCodeList);
            bean.setProductCode(book.getFundCode());
            bean.setProductName(productBean.getFundAttr());
            bean.setProductType(productBean.getFundType());
            bean.setProductSubType(productBean.getFundSubType());
            bean.setBalanceVol(book.getBalanceVol());
            bean.setUnconfirmedVol(book.getFrznVol());
            bean.setCurrency(productBean.getCurrency());
            bean.setScaleType(ScaleTypeEnum.CONSIGNMENT.getCode());
            bean.setHkSaleFlag(productBean.getHkSaleFlag());
            bean.setFractionateCallFlag(productBean.getFractionateCallFlag());
            bean.setStageEstablishFlag(productBean.getStageEstablishFlag());
            bean.setIncomeDt(book.getEstablishDt()); // 临时用收益日期字段存储成立日期
            bean.setProductSaleType(productBean.getProductSaleType());// 产品销售类型
            bean.setHwSaleFlag(productBean.getHwSaleFlag());// 是否海外
            bean.setNaProductFeeType(productBean.getNaProductFeeType()); // NA产品
            bean.setStandardFixedIncomeFlag(productBean.getStandardFixedIncomeFlag());// 特殊产品指标控制需求 20221130
            // 是否拆单产品
            if (splitFundList.contains(bean.getProductCode())) {
                bean.setStageFlag(YesOrNoEnum.YES.getCode());
            }
            // 成立日期
            bean.setEstablishDt(book.getEstablishDt());
            subBalanceList.add(bean);
        }
    }

    // 只有一期的固收产品类型
    private static Set<String> ONTHER_FIXEDINCODE_PRODUCT_SET = new HashSet<>();

    static {
        ONTHER_FIXEDINCODE_PRODUCT_SET.add(StandardFixedIncomeFlagEnum.CASH_MANAGER.getCode());
        ONTHER_FIXEDINCODE_PRODUCT_SET.add(StandardFixedIncomeFlagEnum.BOND_GS.getCode());
        ONTHER_FIXEDINCODE_PRODUCT_SET.add(StandardFixedIncomeFlagEnum.BROKERAGE_COLLECTION_GS.getCode());
    }

    /**
     * @param productBean
     * @return boolean
     * @Description 是否净值化产品
     * <AUTHOR>
     * @Date 2019/9/2 20:55
     **/
    private boolean isNavTypeProduct(HighProductDBInfoBean productBean) {
        // 非股权或者净值化固定收益产品或者纯债固收
        if (!ProductDBTypeEnum.GUQUAN.getCode().equals(productBean.getFundSubType())
                && !(ProductDBTypeEnum.GUDINGSHOUYI.getCode().equals(productBean.getFundSubType())
                && !((StandardFixedIncomeFlagEnum.BROKERAGE_COLLECTION_GS.getCode().equals(productBean.getStandardFixedIncomeFlag()))
                || StandardFixedIncomeFlagEnum.BOND_GS.getCode().equals(productBean.getStandardFixedIncomeFlag())))) {
            return true;
        }

        return false;
    }

    /**
     * processNotStructureProduct:(非结构化产品(非债券分期成立的产品)处理)
     *
     * @param txAcctNo
     * @param hbOneNo
     * @param highProductDBInfoBeanMap
     * @param list
     * @param crisisFundList
     * @param splitfundList
     * @return
     * <AUTHOR>
     * @date 2017年7月25日 下午3:54:48
     */
    private List<BalanceBean> processNotStructureProduct(String txAcctNo, String hbOneNo, List<String> disCodeList,
                                                         Map<String, HighProductDBInfoBean> highProductDBInfoBeanMap, List<BalanceVo> list,
                                                         List<String> crisisFundList, List<String> splitfundList, String balanceStatus) {
        // 封装返回信息中的份额相关信息和产品代码维度map
        List<BalanceBean> subBalanceList = new ArrayList<>();
        Set<String> incomeFundCodeSet = new HashSet<>();
        Set<String> navFundCodeSet = new HashSet<>();
        Set<String> netBuyAmtFundCodeSet = new HashSet<>();
        Set<String> fractionateCallFundCodeSet = new HashSet<>();
        Set<String> fixedIncomeFundCodeSet = new HashSet<>();
        Set<String> notGSAndGQFundCodeSet = new HashSet<>();
        processBalanceVo(txAcctNo, disCodeList, highProductDBInfoBeanMap, list, subBalanceList, incomeFundCodeSet, navFundCodeSet, netBuyAmtFundCodeSet, fractionateCallFundCodeSet, fixedIncomeFundCodeSet, notGSAndGQFundCodeSet, balanceStatus);

        // 批量获取基金净值map
        Map<String, HighProductNavBean> fundNavMap = getNavMap(new ArrayList<>(navFundCodeSet));

        // 批量获取基金分红信息map
        Map<String, HighProductNavDivBean> fundNavDivMap = getNavDivMap(new ArrayList<>(navFundCodeSet));

        // 批量获取当前收益map,当前用户的需要计算收益
        Map<String, HighFundAssetIncomeDomain> currentAssetMap =
                queryAssetService.getCurrentAssetMap(new ArrayList<>(incomeFundCodeSet), hbOneNo, disCodeList);

        // 获取当前账户股权分次call产品实缴金额
        Map<String, BigDecimal> paidInAmtMap = getPaidInAmtMap(txAcctNo, new ArrayList<>(fractionateCallFundCodeSet));

        // 批量查询股权产品收益
        Map<String, HighFundAssetIncomeDomain> guquanCurrentAssetMap =
                queryAssetService.getCurrentAssetMap(new ArrayList<>(netBuyAmtFundCodeSet), hbOneNo, disCodeList);

        // 查询产品净购买金额
        QueryAcctBalanceBaseParam queryAcctBalanceBaseParam = new QueryAcctBalanceBaseParam();
        queryAcctBalanceBaseParam.setHbOneNo(hbOneNo);
        queryAcctBalanceBaseParam.setTxAcctNo(txAcctNo);
        queryAcctBalanceBaseParam.setDisCodeList(disCodeList);
        queryAcctBalanceBaseParam.setFundCodeList(new ArrayList<>(netBuyAmtFundCodeSet));
        Map<String, OwnershipOrderDto> ownershipDtoMap = acctBalanceBaseInfoService.getOwnershipOrderInfoMap(queryAcctBalanceBaseParam);

        // 查询固收类产品起息日相关信息
        Map<String, List<HighProductValueDateBean>> valueDateMap = productValueDateCalService.getValueDateMap(fixedIncomeFundCodeSet);

        // 查询非固收非股权最近确认日期确认的认申购订单
        Map<String, List<AckDealOrderInfo>> latestAckMap = acctBalanceBaseInfoService.getAgentAckDealDtlMap(txAcctNo, notGSAndGQFundCodeSet);

        // 设置返回信息中产品信息、净值信息和市值计算, 格式处理
        setOthersInfo(txAcctNo, hbOneNo, highProductDBInfoBeanMap, crisisFundList, splitfundList, subBalanceList, fundNavMap, fundNavDivMap, currentAssetMap, paidInAmtMap, guquanCurrentAssetMap, ownershipDtoMap, valueDateMap, latestAckMap);

        return subBalanceList;
    }

    private void setOthersInfo(String txAcctNo, String hbOneNo, Map<String, HighProductDBInfoBean> highProductDbInfoBeanMap,
                               List<String> crisisFundList, List<String> splitFundList, List<BalanceBean> subBalanceList,
                               Map<String, HighProductNavBean> fundNavMap, Map<String, HighProductNavDivBean> fundNavDivMap,
                               Map<String, HighFundAssetIncomeDomain> currentAssetMap, Map<String, BigDecimal> paidInAmtMap,
                               Map<String, HighFundAssetIncomeDomain> guquanCurrentAssetMap, Map<String, OwnershipOrderDto> ownershipDtoMap,
                               Map<String, List<HighProductValueDateBean>> valueDateMap, Map<String, List<AckDealOrderInfo>> latestAckMap) {
        for (BalanceBean balanceBean : subBalanceList) {
            if (CollectionUtils.isNotEmpty(crisisFundList) && crisisFundList.contains(balanceBean.getProductCode())) {
                balanceBean.setCrisisFlag(YesOrNoEnum.YES.getCode());
            } else {
                // 特殊产品指标控制需求：获取产品净值披露方式（用于市值计算方式判断） 20221122
                // 固定收益类产品，即【产品大类productSubType】=2-固定收益，且【净值披露方式】=2-份额收益，则【持仓总市值】=【持仓份额累计】* 1
                if (ProductDBTypeEnum.GUDINGSHOUYI.getCode().equals(balanceBean.getProductSubType())) {
                    HighProductDBInfoBean highProductDbInfoBean = highProductDbInfoBeanMap.get(balanceBean.getProductCode());
                    if (highProductDbInfoBean != null && highProductDbInfoBean.getNavDisclosureType() != null) {
                        balanceBean.setNavDisclosureType(highProductDbInfoBean.getNavDisclosureType());
                    }
                }

                // 是否拆单产品
                if (splitFundList.contains(balanceBean.getProductCode())) {
                    balanceBean.setStageFlag(YesOrNoEnum.YES.getCode());
                }

                // 净值和收益信息处理
                processNavAndAssetInfo(balanceBean, false, fundNavMap, currentAssetMap, crisisFundList, latestAckMap, txAcctNo, hbOneNo, fundNavDivMap);

                // 收益计算状态,这个就是看上个方法计算收益的有没有覆盖到
                balanceBean.setIncomeCalStat(acctBalanceBaseInfoService.getIncomeCalStatus(balanceBean, crisisFundList));

                // 股权产品认缴金额,只有固收分次的才会有认缴
                balanceBean.setPaidInAmt(MoneyUtil.formatMoney(paidInAmtMap.get(balanceBean.getProductCode()), 2));

                // 股权产品持仓成本
                if (ProductDBTypeEnum.GUQUAN.getCode().equals(balanceBean.getProductSubType())) {
                    OwnershipOrderDto ownershipOrderDto = ownershipDtoMap.get(balanceBean.getProductCode());
                    if (ownershipOrderDto != null) {
                        // 股权当前投资成本/实缴金额: 净购买金额; 代销不支持外币
                        balanceBean.setNetBuyAmount(MoneyUtil.formatMoney(ownershipOrderDto.getNetBuyAmt(), 2));
                        balanceBean.setCurrencyNetBuyAmount(MoneyUtil.formatMoney(ownershipOrderDto.getNetBuyAmt(), 2));
                        balanceBean.setBalanceCostCurrency(MoneyUtil.formatMoney(ownershipOrderDto.getNetBuyAmt(), 2));
                        balanceBean.setBalanceCost(MoneyUtil.formatMoney(ownershipOrderDto.getNetBuyAmt(), 2));
                        // 股权产品转让标识
                        balanceBean.setOwnershipTransferIdentity(ownershipOrderDto.getTransferIdentity());
                    }
                    // 处理股权产品收益(资产中心新收益字段)
                    HighFundAssetIncomeDomain assetDto = guquanCurrentAssetMap.get(balanceBean.getProductCode());
                    acctBalanceBaseInfoService.setGuQuanAssertInfoWithOutCrisis(balanceBean, assetDto);
                } else if (ProductDBTypeEnum.GUDINGSHOUYI.getCode().equals(balanceBean.getProductSubType())) {
                    HighFundAssetIncomeDomain assetDto = currentAssetMap.get(balanceBean.getProductCode());
                    acctBalanceBaseInfoService.setGuShouAssetInfoWithOutCrisis(balanceBean, assetDto);
                }

            }

            // 计算固收类产品的起息日
            calFixedIncomeValueDate(balanceBean, valueDateMap);
        }
    }

    private void processBalanceVo(String txAcctNo, List<String> disCodeList, Map<String,
            HighProductDBInfoBean> highProductDBInfoBeanMap, List<BalanceVo> list,
                                  List<BalanceBean> subBalanceList, Set<String> incomeFundCodeSet,
                                  Set<String> navFundCodeSet, Set<String> netBuyAmtFundCodeSet,
                                  Set<String> fractionateCallFundCodeSet,
                                  Set<String> fixedIncomeFundCodeSet, Set<String> notGSAndGQFundCodeSet,
                                  String balanceStatus) {
        HighProductDBInfoBean productBean;
        for (BalanceVo vo : list) {
            productBean = highProductDBInfoBeanMap.get(vo.getProductCode());
            if (productBean == null) {
                // 这里要么是产品中心没有查到,要么是前面产品过滤的时候给过滤了
                logger.info("processNotStructureProduct-遍历持仓产品信息,匹配产品中心查询结果,匹配不上,fundCode={}", vo.getProductCode());
                continue;
            }
            if (StageEstablishFlagEnum.STAGE.getCode().equals(productBean.getStageEstablishFlag())) {
                continue;
            }

            // 股权产品不计算收益(回款代替)
            if (ProductDBTypeEnum.GUQUAN.getCode().equals(productBean.getFundSubType())) {
                netBuyAmtFundCodeSet.add(vo.getProductCode());
                if (FractionateCallFlagEnum.YES.getCode().equals(productBean.getFractionateCallFlag())) {// 分次call产品
                    fractionateCallFundCodeSet.add(vo.getProductCode());
                }
            } else {
                incomeFundCodeSet.add(vo.getProductCode());
                navFundCodeSet.add(vo.getProductCode());
            }

            if (ProductDBTypeEnum.GUDINGSHOUYI.getCode().equals(productBean.getFundSubType())) {
                fixedIncomeFundCodeSet.add(vo.getProductCode());
                if (!ONTHER_FIXEDINCODE_PRODUCT_SET.contains(productBean.getStandardFixedIncomeFlag())) {
                    if (ProductTypeEnum.SM.getCode().equals(productBean.getFundType())) {
                        List<BalanceBean> fixedIncomeBalances = buildFixedIncomeBalances(txAcctNo, vo.getProductCode(), productBean, disCodeList, balanceStatus);
                        subBalanceList.addAll(fixedIncomeBalances);
                    } else {
                        BalanceBean bean = buildBalance(productBean, vo, disCodeList);
                        subBalanceList.add(bean);
                    }
                } else {
                    BalanceBean bean = buildBalance(productBean, vo, disCodeList);
                    subBalanceList.add(bean);
                }

            } else {
                BalanceBean bean = buildBalance(productBean, vo, disCodeList);
                subBalanceList.add(bean);
            }

            // 是否净值化产品
            if (isNavTypeProduct(productBean)) {
                notGSAndGQFundCodeSet.add(vo.getProductCode());
            }
        }
    }

    private List<BalanceBean> buildFixedIncomeBalances(String txAcctNo, String fundCode, HighProductDBInfoBean productBean, List<String> disCodeList, String balanceStatus) {
        List<SubCustBooksPo> subCustBooksList = subCustBooksRepository.selectSubCustBookSumByAckDt(disCodeList,
                txAcctNo, fundCode, balanceStatus);
        if (CollectionUtils.isEmpty(subCustBooksList)) {
            return Collections.emptyList();
        }

        List<BalanceBean> fixedIncomeBalances = new ArrayList<>();
        BalanceBean bean = null;
        for (SubCustBooksPo subCustBooksPo : subCustBooksList) {
            bean = new BalanceBean();
            bean.setDisCode(subCustBooksPo.getDisCode());
            bean.setDisCodeList(disCodeList);
            bean.setProductCode(subCustBooksPo.getFundCode());
            bean.setProductName(productBean.getFundAttr());
            bean.setProductType(productBean.getFundType());
            bean.setProductSubType(productBean.getFundSubType());
            bean.setCurrency(productBean.getCurrency());
            bean.setScaleType(ScaleTypeEnum.CONSIGNMENT.getCode());
            bean.setHkSaleFlag(productBean.getHkSaleFlag());
            bean.setBalanceVol(subCustBooksPo.getBalanceVol());
            bean.setUnconfirmedVol(subCustBooksPo.getFrznVol());
            bean.setFundCXQXStr(productBean.getFundCXQXStr()); // 股权产品存续期限描述
            bean.setFractionateCallFlag(productBean.getFractionateCallFlag());
            bean.setStageEstablishFlag(productBean.getStageEstablishFlag());
            bean.setValueDate(subCustBooksPo.getRegDt());// 临时用起息日字段存储确认日期，作为后续的查询条件查询实际起息日
            bean.setStandardFixedIncomeFlag(productBean.getStandardFixedIncomeFlag());// 是否标准固收
            bean.setBenchmarkType(productBean.getBenchmarkType());// 基准类型（0-业绩比较基准 1-业绩报酬计提基准
            bean.setHwSaleFlag(productBean.getHwSaleFlag());
            bean.setOneStepType(productBean.getOneStepType());
            bean.setTwoStepType(productBean.getTwoStepType());
            bean.setSecondStepType(productBean.getSecondStepType());
            bean.setProductSaleType(productBean.getProductSaleType());// 产品销售类型
            bean.setSfhwcxg(productBean.getSfhwcxg());
            fixedIncomeBalances.add(bean);
        }

        return fixedIncomeBalances;
    }

    private BalanceBean buildBalance(HighProductDBInfoBean productBean, BalanceVo vo, List<String> disCodeList) {
        BalanceBean bean = new BalanceBean();
        bean.setDisCode(vo.getDisCode());
        bean.setDisCodeList(disCodeList);
        bean.setProductCode(vo.getProductCode());
        bean.setProductName(productBean.getFundAttr());
        bean.setProductType(productBean.getFundType());
        bean.setProductSubType(productBean.getFundSubType());
        bean.setCurrency(productBean.getCurrency());
        bean.setScaleType(ScaleTypeEnum.CONSIGNMENT.getCode());
        bean.setHkSaleFlag(productBean.getHkSaleFlag());
        bean.setBalanceVol(vo.getBalanceVol());
        bean.setUnconfirmedVol(vo.getUnconfirmedVol());
        bean.setUnconfirmedAmt(vo.getUnconfirmedAmt());
        bean.setFundCXQXStr(productBean.getFundCXQXStr()); // 股权产品存续期限描述
        bean.setFractionateCallFlag(productBean.getFractionateCallFlag());
        bean.setStageEstablishFlag(productBean.getStageEstablishFlag());
        bean.setStandardFixedIncomeFlag(productBean.getStandardFixedIncomeFlag());// 是否标准固收
        bean.setBenchmarkType(productBean.getBenchmarkType());// 基准类型（0-业绩比较基准 1-业绩报酬计提基准
        bean.setHwSaleFlag(productBean.getHwSaleFlag());
        bean.setOneStepType(productBean.getOneStepType());
        bean.setTwoStepType(productBean.getTwoStepType());
        bean.setSecondStepType(productBean.getSecondStepType());
        bean.setProductSaleType(productBean.getProductSaleType());// 产品销售类型
        bean.setNaProductFeeType(productBean.getNaProductFeeType());// NA产品收费类型
        bean.setSfhwcxg(productBean.getSfhwcxg());
        return bean;
    }

    /**
     * 净值和收益信息处理
     *
     * @param balanceBean
     * @param structureFlag   结构化标识
     * @param fundNavMap
     * @param currentAssetMap
     */
    private void processNavAndAssetInfo(BalanceBean balanceBean, boolean structureFlag, Map<String, HighProductNavBean> fundNavMap,
                                        Map<String, HighFundAssetIncomeDomain> currentAssetMap, List<String> crisisFundList,
                                        Map<String, List<AckDealOrderInfo>> LatestAckMap,
                                        String txAcctNo, String hbOneNo, Map<String, HighProductNavDivBean> fundNavDivMap) {
        // 持仓数据格式化
        balanceBean.setUnconfirmedVol(MoneyUtil.formatMoney(balanceBean.getUnconfirmedVol(), 2));
        balanceBean.setUnconfirmedAmt(MoneyUtil.formatMoney(balanceBean.getUnconfirmedAmt(), 2));

        // 危机产品, 不处理
        if (crisisFundList.contains(balanceBean.getProductCode())) {
            balanceBean.setCrisisFlag(YesOrNoEnum.YES.getCode());
            return;
        }

        // 查询fundCode, 根据是否结构化, 决定用子产品代码还是主产品代码查询
        String queryProductCode = structureFlag ? balanceBean.getSubProductCode() : balanceBean.getProductCode();

        // 净值
        HighProductNavBean navBean = fundNavMap.get(queryProductCode);
        // 最新确认订单
        List<AckDealOrderInfo> latestAckList = LatestAckMap.get(balanceBean.getProductCode());
        // 计算市值
        acctBalanceBaseInfoService.setAgentMarketValueAndNavInfo(balanceBean, navBean, latestAckList);
        // 份额格式化
        balanceBean.setBalanceVol(MoneyUtil.formatMoney(balanceBean.getBalanceVol(), 2));

        // 处理净值分红状态
        acctBalanceBaseInfoService.processNavDivFlag(balanceBean, navBean, txAcctNo, hbOneNo, fundNavDivMap);

        // 收益信息
        HighFundAssetIncomeDomain currentAssetDto = currentAssetMap.get(queryProductCode);
        acctBalanceBaseInfoService.setAgentBalanceAssetInfo(balanceBean, currentAssetDto);
        // 七日年化和收益日期
        acctBalanceBaseInfoService.setYieldIncomeInfo(balanceBean);
    }


    private void setYieldInfo(BalanceBean balanceBean) {
        // 查询固收库数据st_fixed.t_st_hbjz
        HighProductDBYieldBean yieldBean = queryHighProductOuterService.getSimuHbjjNewestSyByJjdms(balanceBean.getProductCode());
        if (yieldBean != null) {
            balanceBean.setYieldIncome(yieldBean.getYieldIncome());
            balanceBean.setYieldIncomeDt(yieldBean.getNavDate());
            // 特殊产品指标控制需求：万份收益 20221122
            balanceBean.setCopiesIncome(yieldBean.getWfsyIncome());
        } else {
            logger.info("productCode:{},七日年化万分收益查询结果为空", balanceBean.getProductCode());
        }
    }


    /**
     * 获取子产品列表
     *
     * @param subBalanceList
     * @return
     */
    private List<String> getSubProductCodeList(List<BalanceBean> subBalanceList) {
        List<String> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(subBalanceList)) {
            return list;
        }

        for (BalanceBean balanceBean : subBalanceList) {
            if (StringUtils.isNotEmpty(balanceBean.getSubProductCode())) {
                list.add(balanceBean.getSubProductCode());
            }
        }

        return list;
    }

    /**
     * getGqhkAmt:(获取股权产品回款金额)
     *
     * @param disCodeList
     * @param txAcctNo
     * @param hbOneNo
     * @param productDBType
     * @param productCode
     * @return
     * <AUTHOR>
     * @date 2017年12月19日 下午9:59:22
     */
    private BigDecimal getGqhkAmt(List<String> disCodeList, String txAcctNo, String hbOneNo, String productDBType, String productCode) {
        if (ProductDBTypeEnum.GUQUAN.getCode().equals(productDBType)) {// 如果是私募股权产品，获取用户的分红回款金额
            BigDecimal dxGqhkAmt = highDealOrderDtlRepository.countDxGqhkAmt(disCodeList, txAcctNo, productCode);// 代销股权回款
            BigDecimal crmGqhkAmt = highDealOrderDtlRepository.countCrmGqhkAmt(hbOneNo, productCode);// 直销股权回款
            BigDecimal gqhkAmt = hkAmt(dxGqhkAmt, crmGqhkAmt);
            return gqhkAmt;
        } else {
            return null;
        }
    }

    /**
     * @param dxGqhkAmt  代销回款金额
     * @param crmGqhkAmt 股权回款金额
     * @return java.math.BigDecimal
     * @Description 计算回款金额
     * <AUTHOR>
     * @Date 2019/1/15 18:31
     **/
    private BigDecimal hkAmt(BigDecimal dxGqhkAmt, BigDecimal crmGqhkAmt) {
        if (dxGqhkAmt == null && crmGqhkAmt == null) {
            return null;
        }
        if (dxGqhkAmt == null) {
            return crmGqhkAmt;
        } else if (crmGqhkAmt == null) {
            return dxGqhkAmt;
        }

        return dxGqhkAmt.add(crmGqhkAmt);
    }

    /**
     * processDirectBalance:(处理直销资产)
     *
     * @param hbOneNo
     * @param txAcctNo
     * @param request
     * @param balanceList
     * <AUTHOR>
     * @date 2017年7月11日 下午4:30:22
     */
    private void processDirectBalance(String hbOneNo, String txAcctNo, QueryAcctBalanceRequest request, List<BalanceBean> balanceList, List<String> crisisFundList) {
        if (StringUtils.isEmpty(hbOneNo)) {
            hbOneNo = queryHbOneNoOuterService.queryHbOneNoByTxAcctNo(txAcctNo);
        }
        if (StringUtils.isEmpty(hbOneNo)) {
            logger.warn("QueryAcctBalanceFacadeService2|processDirectBalance() hbOneNo is null, txAcctNo:{}", txAcctNo);
            return;
        }
        QueryDirectBalanceParam queryDirectBalanceParam = new QueryDirectBalanceParam();
        queryDirectBalanceParam.setCallType(request.getCallType());
        queryDirectBalanceParam.setCrisisFundList(crisisFundList);
        queryDirectBalanceParam.setHbOneNo(hbOneNo);
        queryDirectBalanceParam.setDisCodeList(request.getDisCodeList());
        queryDirectBalanceParam.setTxAcctNo(request.getTxAcctNo());
        queryDirectBalanceParam.setHkSaleFlag(request.getHkSaleFlag());
        queryDirectBalanceParam.setProductCode(request.getProductCode());
        queryDirectBalanceParam.setProductSubType(request.getProductSubType());
        queryDirectBalanceParam.setProductType(request.getProductType());
        queryDirectBalanceParam.setBalanceStatus(request.getBalanceStatus());
        List<BalanceBean> subBalanceList = queryDirectBalanceCacheService.getBalanceList(queryDirectBalanceParam);
        if (CollectionUtils.isEmpty(subBalanceList)) {
            return;
        }

        balanceList.addAll(subBalanceList);
    }

    /**
     * 收益率计算
     * <p>
     * i. 当收益为负数，若第5位小数大于0，则向下进一保留4位小数，如：-0.26594，处理为-0.2660；
     * ii. 当收益为负数，若第5位小数等于0，则直接截断保留4位小数；
     * iii. 当收益为正数，则直接截断保留4位小数，如0.26594，处理为0.2659
     *
     * @param balanceCost  持仓总成本
     * @param currentAsset 收益
     * @param configScale  db收益率类指标位数
     */
    public BigDecimal getYieldRate(BigDecimal balanceCost, BigDecimal currentAsset, Integer configScale) {
        BigDecimal yieldRate;


        int scale = 4; // 默认4位

        if (configScale != null) {
            scale = configScale;
        }

        if (balanceCost == null || BigDecimal.ZERO.compareTo(balanceCost) == 0) {
            yieldRate = BigDecimal.ZERO;
        } else {
            // 收益为负
            if (currentAsset != null && BigDecimal.ZERO.compareTo(currentAsset) > 0) {
                yieldRate = currentAsset.divide(balanceCost, scale + 1, BigDecimal.ROUND_DOWN).setScale(scale, BigDecimal.ROUND_UP);
            } else {
                // 收益为正
                yieldRate = (currentAsset == null ? BigDecimal.ZERO : currentAsset).divide(balanceCost, scale, BigDecimal.ROUND_DOWN);
            }
        }

        return yieldRate;
    }

    /**
     * getNavMap:(批量获取基金净值map)
     *
     * @param midProductIds
     * @return
     * <AUTHOR>
     * @date 2017年11月20日 下午5:00:24
     */
    private Map<String, HighProductNavBean> getNavMap(List<String> midProductIds) {
        // 批量查询基金基金净值信息
        Map<String, HighProductNavBean> highProductNavMap = new HashMap<String, HighProductNavBean>();
        if (CollectionUtils.isEmpty(midProductIds)) {
            return highProductNavMap;
        }

        List<HighProductNavBean> highProductNavBeanList = null;
        try {
            highProductNavBeanList = queryHighProductOuterService.getHighProductNavInfo(new ArrayList<String>(midProductIds));
        } catch (Exception e) {
            logger.error("QueryAcctBalanceFacadeService|queryHighProductOuterService.getHighProductNavInfo({}) error :", e, JSON.toJSONString(midProductIds));
        }

        if (!CollectionUtils.isEmpty(highProductNavBeanList)) {
            for (HighProductNavBean highProductNavBean : highProductNavBeanList) {
                highProductNavMap.put(highProductNavBean.getFundCode(), highProductNavBean);
            }
        }
        return highProductNavMap;
    }

    /**
     * @param midProductIds
     * @return java.util.Map<java.lang.String, com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductNavDivBean>
     * @description:(批量获取基金分红信息map)
     * @author: haiguang.chen
     * @date: 2022/7/14 14:16
     * @since JDK 1.8
     */
    private Map<String, HighProductNavDivBean> getNavDivMap(List<String> midProductIds) {
        // 批量查询基金基金净值信息
        Map<String, HighProductNavDivBean> highProductNavDivBeanMap = new HashMap<String, HighProductNavDivBean>();
        if (CollectionUtils.isEmpty(midProductIds)) {
            return highProductNavDivBeanMap;
        }

        List<HighProductNavDivBean> highProductNavDivBeanList = null;
        try {
            highProductNavDivBeanList = queryHighProductOuterService.getHighProductNavDivInfo(new ArrayList<>(midProductIds));
        } catch (Exception e) {
            logger.error("QueryAcctBalanceFacadeService|queryHighProductOuterService.getHighProductNavInfo,productCode={}, error={}", JSON.toJSONString(midProductIds), e);
        }

        if (!CollectionUtils.isEmpty(highProductNavDivBeanList)) {
            for (HighProductNavDivBean highProductNavDivBean : highProductNavDivBeanList) {
                highProductNavDivBeanMap.put(highProductNavDivBean.getFundCode(), highProductNavDivBean);
            }
        }
        return highProductNavDivBeanMap;
    }

    /**
     * 获取分期成立产品净值信息
     *
     * @param subBalanceList
     * @return
     */
    private Map<String, HighProductNavBean> getStructureNavMap(List<BalanceBean> subBalanceList) {
        final Map<String, HighProductNavBean> fundNavMap = new ConcurrentHashMap<>();
        if (CollectionUtils.isEmpty(subBalanceList)) {
            return fundNavMap;
        }

        // 多线程异步查询产品净值信息
        final CountDownLatch latch = new CountDownLatch(subBalanceList.size());
        for (BalanceBean balanceBean : subBalanceList) {
            CommonThreadPool.submit(new QueryStructProductNavTask(queryHighProductOuterService, fundNavMap, balanceBean, latch));
        }
        try {
            latch.await();
        } catch (InterruptedException e) {
            logger.error("QueryAcctBalanceFacadeService|latch.await exception.", e);
            Thread.currentThread().interrupt();
        }
        logger.info("分期成立产品净值map,fundNavMap={}", JSON.toJSONString(fundNavMap));
        return fundNavMap;
    }


    /**
     * 查询股权产品(分次call)认缴金额
     *
     * @param txAcctNo
     * @param fundCodeList
     * @return
     */
    private Map<String, BigDecimal> getPaidInAmtMap(String txAcctNo, List<String> fundCodeList) {
        Map<String, BigDecimal> map = new HashMap<String, BigDecimal>();
        if (CollectionUtils.isEmpty(fundCodeList)) {
            return map;
        }

        // 查询客户产品的认缴金额
        List<SubscribeAmtDetailPo> subscribeAmtDetailList = subscribeAmtDetailRepository.getSubscribeAmtDetail(txAcctNo, fundCodeList);
        if (CollectionUtils.isEmpty(subscribeAmtDetailList)) {
            logger.info("没查询到认缴信息,txAcctNo={}, fundCodeList={}", txAcctNo, fundCodeList);
            return map;
        }
        for (SubscribeAmtDetailPo subscribeAmtDetailPo : subscribeAmtDetailList) {
            map.put(subscribeAmtDetailPo.getFundCode(), subscribeAmtDetailPo.getSubscribeAmt());
        }
        return map;
    }

    /**
     * getIncomeCalStatus:(获取当前收益计算状态)
     *
     * @param bal
     * @return
     * <AUTHOR>
     * @date 2018年11月20日 下午5:05:31
     */
    private String getIncomeCalStatus(BalanceBean bal, List<String> crisisFundList) {
        String incomeCalStatus = null;
        String productDBType = bal.getProductSubType();
        if (crisisFundList.contains(bal.getProductCode())
                || ProductDBTypeEnum.GUQUAN.getCode().equals(productDBType)
                || (ProductDBTypeEnum.GUDINGSHOUYI.getCode().equals(productDBType)
                && !StandardFixedIncomeFlagEnum.BROKERAGE_COLLECTION_GS.getCode().equals(bal.getStandardFixedIncomeFlag()))
                || (ProductDBTypeEnum.GUDINGSHOUYI.getCode().equals(productDBType)
                && StandardFixedIncomeFlagEnum.BROKERAGE_COLLECTION_GS.getCode().equals(bal.getStandardFixedIncomeFlag())
                && NavDisclosureTypeEnum.FESY.getCode().equals(bal.getNavDisclosureType()))
                || StringUtils.isEmpty(bal.getIncomeDt())) {
            // 股权类,固收默认是计算完成; 危机产品默认收益计算状态为完成, 不影响客户总收益计算状态; 资产中心未返回某产品的收益时
            incomeCalStatus = IncomeCalStatEnum.FINISHED.getCode();
        } else if (StringUtils.isNotEmpty(bal.getNavDt())
                && StringUtils.isNotEmpty(bal.getIncomeDt())
                && bal.getNavDt().compareTo(bal.getIncomeDt()) <= 0) {
            incomeCalStatus = IncomeCalStatEnum.FINISHED.getCode();
        } else {
            incomeCalStatus = IncomeCalStatEnum.PROCESSING.getCode();
        }

        return incomeCalStatus;
    }

    /**
     * @param bal
     * @param valueDateMap
     * @return void
     * @Description 计算起息日
     * <AUTHOR>
     * @Date 2018/12/5 17:00
     **/
    private void calFixedIncomeValueDate(BalanceBean bal, Map<String, List<HighProductValueDateBean>> valueDateMap) {
        List<HighProductValueDateBean> fixedInconeValueDateList = valueDateMap.get(bal.getProductCode());
        HighProductValueDateBean valueDateBean = new HighProductValueDateBean();
        if (!ONTHER_FIXEDINCODE_PRODUCT_SET.contains(bal.getStandardFixedIncomeFlag())) {
            if (ProductTypeEnum.SM.getCode().equals(bal.getProductType())) {
                valueDateBean = productValueDateCalService.getHighProductValueDate(bal.getValueDate(),
                        fixedInconeValueDateList);
            } else {
                if (!CollectionUtils.isEmpty(fixedInconeValueDateList)) {
                    valueDateBean = fixedInconeValueDateList.get(fixedInconeValueDateList.size() - 1);
                }
            }
            // 非标和标准固收才展示起息日到期日
            bal.setValueDate(valueDateBean.getValueDate());
            bal.setDueDate(valueDateBean.getDueDate());
        } else {
            if (!CollectionUtils.isEmpty(fixedInconeValueDateList)) {
                valueDateBean = fixedInconeValueDateList.get(fixedInconeValueDateList.size() - 1);
            }
        }

        bal.setBenchmark(valueDateBean.getBenchmark());
        bal.setRePurchaseFlag(valueDateBean.getRePurchaseFlag());
        bal.setInvestmentHorizon(valueDateBean.getInvestmentHorizon());
        bal.setCooperation(valueDateBean.getCooperation());

    }
}
