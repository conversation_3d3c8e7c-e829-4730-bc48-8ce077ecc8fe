<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.howbuy</groupId>
    <artifactId>howbuy-simu-client</artifactId>
    <version>release-20250818-jjbq-RELEASE</version>
    <name>howbuy-simu-client</name>
    <properties>
        <com.howbuy.howbuy-simu-dto.version>release-hw2.5.4-RELEASE</com.howbuy.howbuy-simu-dto.version>
        <com.howbuy.howbuy-persistence.version>release-20250425-hw2.9-RELEASE</com.howbuy.howbuy-persistence.version>
    </properties>
    <profiles>
        <profile>
            <id>dev</id>
            <!--测试环境 e.g: mvn clean install -P dev -->
            <activation>
                <!-- 开发环境 Windows,Mac 默认激活 当前 profile-->
                <!-- 设置默认激活 -->
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <howbuy.version>1.0.0-SNAPSHOT</howbuy.version>
            </properties>
        </profile>
        <profile>
            <!-- 生产环境 linux 激活 当前 profile e.g: mvn clean install -P prod -->
            <id>prod</id>
            <properties>
                <howbuy.version>1.0.0-release</howbuy.version>
            </properties>
        </profile>
    </profiles>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-infrastructure</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <dependencies>
        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>howbuy-simu-dto</artifactId>
            <version>${com.howbuy.howbuy-simu-dto.version}</version>
        </dependency>

        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>howbuy-persistence</artifactId>
            <version>${com.howbuy.howbuy-persistence.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.howbuy</groupId>
                    <artifactId>utils</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.vdurmont</groupId>
                    <artifactId>emoji-java</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>utils</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>1.6.4</version>
        </dependency>
        <dependency>
            <groupId>net.sourceforge.pinyin4j</groupId>
            <artifactId>pinyin4j</artifactId>
            <version>2.5.0</version>
        </dependency>
        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
            <version>3.2.1</version>
        </dependency>
        <dependency>
            <groupId>com.howbuy.fp</groupId>
            <artifactId>howbuy-comcalculate</artifactId>
            <version>1.1.2</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.62</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.24</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-annotation</artifactId>
            <version>3.4.1</version>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>16.0</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>2.3.2</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-dependency-plugin</artifactId>
                <version>3.1.1</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.9</version>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
            <!-- 打包源码 -->
            <plugin>
                <artifactId>maven-source-plugin</artifactId>
                <version>2.1</version>
                <configuration>
                    <attach>true</attach>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!--生成javadoc-->
            <!-- <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <executions>
                    <execution>
                        <id>attach-javadocs</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
            </executions>
            <configuration>
                <aggregate>true</aggregate>
                <charset>UTF-8</charset>
                <encoding>UTF-8</encoding>
                <docencoding>UTF-8</docencoding>
                <show>private</show>
                <tags>
                    <tag>
                        <name>todo</name>
                        <placement>a</placement>
                        <head>To do something:</head>
                    </tag>
                    <tag>
                        <name>modify</name>
                        <placement>a</placement>
                        <head>modify</head>
                    </tag>
                    <tag>
                        <name>create</name>
                        <placement>a</placement>
                        <head>create time</head>
                    </tag>
                </tags>
                </configuration>
            </plugin>  -->
        </plugins>
        <defaultGoal>install</defaultGoal>
    </build>
    <distributionManagement>
        <repository>
            <id>howbuy-release</id>
            <name>howbuy-release</name>
            <url>http://nx-java.howbuy.pa/repository/howbuy-release/</url>
        </repository>
        <snapshotRepository>
            <id>howbuy-snapshot</id>
            <name>howbuy-snapshot</name>
            <url>http://nx-java.howbuy.pa/repository/howbuy-snapshot/</url>
        </snapshotRepository>
    </distributionManagement>
</project>