<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.sonatype.oss</groupId>
    <artifactId>oss-parent</artifactId>
    <version>7</version>
  </parent>

  <groupId>com.squareup.okhttp3</groupId>
  <artifactId>parent</artifactId>
  <version>3.14.9</version>
  <packaging>pom</packaging>

  <name>OkHttp (Parent)</name>
  <description>An HTTP+HTTP/2 client for Android and Java applications</description>
  <url>https://github.com/square/okhttp</url>

  <modules>
    <module>okhttp</module>
    <module>okhttp-tests</module>

    <module>okhttp-sse</module>
    <module>okhttp-testing-support</module>
    <module>okhttp-tls</module>
    <module>okhttp-urlconnection</module>

    <module>okhttp-logging-interceptor</module>

    <module>okhttp-dnsoverhttps</module>

    <module>okcurl</module>
    <module>mockwebserver</module>
    <module>samples</module>
  </modules>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

    <!-- Compilation -->
    <airlift.version>0.8</airlift.version>
    <robolectric.version>10-robolectric-5803371</robolectric.version>
    <animal.sniffer.version>1.17</animal.sniffer.version>
    <bouncycastle.version>1.60</bouncycastle.version>
    <guava.version>27.0.1-jre</guava.version>
    <java.version>1.8</java.version>
    <moshi.version>1.8.0</moshi.version>
    <jnr-unixsocket.version>0.22</jnr-unixsocket.version>
    <okio.version>1.17.2</okio.version>
    <conscrypt.version>2.0.0</conscrypt.version>

    <!-- Test Dependencies -->
    <junit.version>4.12</junit.version>
    <assertj.version>3.11.0</assertj.version>

    <!-- platform test mode -->
    <okhttp.platform>platform</okhttp.platform>
  </properties>

  <scm>
    <url>https://github.com/square/okhttp/</url>
    <connection>scm:git:https://github.com/square/okhttp.git</connection>
    <developerConnection>scm:git:**************:square/okhttp.git</developerConnection>
    <tag>parent-3.14.9</tag>
  </scm>

  <issueManagement>
    <system>GitHub Issues</system>
    <url>https://github.com/square/okhttp/issues</url>
  </issueManagement>

  <licenses>
    <license>
      <name>Apache 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
    </license>
  </licenses>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.squareup.okio</groupId>
        <artifactId>okio</artifactId>
        <version>${okio.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.code.findbugs</groupId>
        <artifactId>jsr305</artifactId>
        <version>3.0.2</version>
        <scope>provided</scope>
      </dependency>
      <dependency>
        <groupId>junit</groupId>
        <artifactId>junit</artifactId>
        <version>${junit.version}</version>
      </dependency>
      <dependency>
        <groupId>org.bouncycastle</groupId>
        <artifactId>bcprov-jdk15on</artifactId>
        <version>${bouncycastle.version}</version>
      </dependency>
      <dependency>
        <groupId>io.airlift</groupId>
        <artifactId>airline</artifactId>
        <version>${airlift.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.guava</groupId>
        <artifactId>guava</artifactId>
        <version>${guava.version}</version>
      </dependency>
      <dependency>
        <groupId>org.robolectric</groupId>
        <artifactId>android-all</artifactId>
        <version>${robolectric.version}</version>
      </dependency>
      <dependency>
        <groupId>com.squareup.moshi</groupId>
        <artifactId>moshi</artifactId>
        <version>${moshi.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.jnr</groupId>
        <artifactId>jnr-unixsocket</artifactId>
        <version>${jnr-unixsocket.version}</version>
      </dependency>
      <dependency>
        <groupId>org.conscrypt</groupId>
        <artifactId>conscrypt-openjdk-uber</artifactId>
        <version>${conscrypt.version}</version>
      </dependency>
      <dependency>
        <groupId>org.assertj</groupId>
        <artifactId>assertj-core</artifactId>
        <version>${assertj.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>3.8.0</version>
          <configuration>
            <source>${java.version}</source>
            <target>${java.version}</target>
          </configuration>
        </plugin>

        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>2.22.1</version>
          <configuration>
            <systemPropertyVariables>
              <okhttp.platform>${okhttp.platform}</okhttp.platform>
            </systemPropertyVariables>
            <redirectTestOutputToFile>true</redirectTestOutputToFile>
            <properties>
              <!--
                Configure a listener for enforcing that no uncaught exceptions issue from OkHttp
                tests. Every test must have a <scope>test</scope> dependency on
                okhttp-testing-support.
                -->
              <property>
                <name>listener</name>
                <value>okhttp3.testing.InstallUncaughtExceptionHandlerListener</value>
              </property>
            </properties>
          </configuration>
          <dependencies>
            <dependency>
              <groupId>org.apache.maven.surefire</groupId>
              <artifactId>surefire-junit47</artifactId>
              <version>2.22.1</version>
            </dependency>
          </dependencies>
        </plugin>

        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>3.0.1</version>
          <configuration>
            <failOnError>false</failOnError>
            <doclint>none</doclint>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>

    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-release-plugin</artifactId>
        <version>2.5.3</version>
        <configuration>
          <autoVersionSubmodules>true</autoVersionSubmodules>
        </configuration>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <version>3.0.0</version>
        <dependencies>
          <dependency>
            <groupId>com.puppycrawl.tools</groupId>
            <artifactId>checkstyle</artifactId>
            <version>8.15</version>
          </dependency>
        </dependencies>
        <configuration>
          <failsOnError>true</failsOnError>
          <configLocation>checkstyle.xml</configLocation>
          <consoleOutput>true</consoleOutput>
          <excludes>**/CipherSuite.java</excludes>
        </configuration>
        <executions>
          <execution>
            <phase>verify</phase>
            <goals>
              <goal>checkstyle</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>animal-sniffer-maven-plugin</artifactId>
        <version>${animal.sniffer.version}</version>
        <executions>
          <execution>
            <id>sniff-java18</id>
            <phase>test</phase>
            <goals>
              <goal>check</goal>
            </goals>
            <configuration>
              <signature>
                <groupId>org.codehaus.mojo.signature</groupId>
                <artifactId>java18</artifactId>
                <version>1.0</version>
              </signature>
            </configuration>
          </execution>
          <execution>
            <id>sniff-android5</id>
            <phase>test</phase>
            <goals>
              <goal>check</goal>
            </goals>
            <configuration>
              <signature>
                <groupId>net.sf.androidscents.signature</groupId>
                <artifactId>android-api-level-21</artifactId>
                <version>5.0.1_r2</version>
              </signature>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>errorprone</id>
      <activation>
        <jdk>1.8</jdk>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-compiler-plugin</artifactId>
            <version>3.8.0</version>
            <configuration>
              <compilerId>javac-with-errorprone</compilerId>
              <forceJavacCompilerUse>true</forceJavacCompilerUse>
              <source>${java.version}</source>
              <target>${java.version}</target>
            </configuration>
            <dependencies>
              <dependency>
                <groupId>org.codehaus.plexus</groupId>
                <artifactId>plexus-compiler-javac-errorprone</artifactId>
                <version>2.8.5</version>
              </dependency>
              <dependency>
                <groupId>com.google.errorprone</groupId>
                <artifactId>error_prone_core</artifactId>
                <version>2.3.2</version>
              </dependency>
            </dependencies>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>javadoc-lenient</id>
      <!-- MJAVADOC-555 Fixed in upcoming JDK builds -->
      <activation>
        <jdk>11</jdk>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <configuration>
              <skip>true</skip>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>alpn-when-jdk8</id>
      <activation>
        <jdk>1.8</jdk>
      </activation>
      <properties>
        <bootclasspathPrefix>
          ${settings.localRepository}/org/mortbay/jetty/alpn/alpn-boot/${alpn.jdk8.version}/alpn-boot-${alpn.jdk8.version}.jar
        </bootclasspathPrefix>
        <okhttp.platform>jdk-with-jetty-boot</okhttp.platform>
      </properties>
      <build>
        <pluginManagement>
          <plugins>
            <plugin>
              <groupId>org.apache.maven.plugins</groupId>
              <artifactId>maven-surefire-plugin</artifactId>
              <configuration>
                <argLine>-Xbootclasspath/p:${bootclasspathPrefix} -Xms512m -Xmx512m</argLine>
              </configuration>
              <dependencies>
                <dependency>
                  <groupId>org.mortbay.jetty.alpn</groupId>
                  <artifactId>alpn-boot</artifactId>
                  <version>${alpn.jdk8.version}</version>
                </dependency>
              </dependencies>
            </plugin>
          </plugins>
        </pluginManagement>
      </build>
    </profile>
    <profile>
      <id>jdk9</id>
      <activation>
        <jdk>9</jdk>
      </activation>
      <properties>
        <okhttp.platform>jdk9</okhttp.platform>
      </properties>
    </profile>
    <profile>
      <id>jdk10</id>
      <activation>
        <jdk>10</jdk>
      </activation>
      <properties>
        <okhttp.platform>jdk9</okhttp.platform>
      </properties>
    </profile>
    <profile>
      <id>jdk11</id>
      <activation>
        <jdk>11</jdk>
      </activation>
      <properties>
        <okhttp.platform>jdk9</okhttp.platform>
      </properties>
    </profile>
    <profile>
      <id>jdk12</id>
      <activation>
        <jdk>12</jdk>
      </activation>
      <properties>
        <okhttp.platform>jdk9</okhttp.platform>
      </properties>
    </profile>
    <profile>
      <id>conscrypt</id>
      <properties>
        <okhttp.platform>conscrypt</okhttp.platform>
      </properties>
      <dependencies>
        <dependency>
          <groupId>org.conscrypt</groupId>
          <artifactId>conscrypt-openjdk-uber</artifactId>
        </dependency>
      </dependencies>
    </profile>
    <!-- ALPN Versions targeted for each Java 8 minor release -->
    <!-- Check versions with this page: -->
    <!-- http://www.eclipse.org/jetty/documentation/current/alpn-chapter.html#alpn-chapterchapterversions -->
    <profile>
      <id>alpn-when-jdk8_05</id>
      <activation>
        <jdk>1.8.0_05</jdk>
      </activation>
      <properties>
        <alpn.jdk8.version>8.1.0.v20141016</alpn.jdk8.version>
      </properties>
    </profile>
    <profile>
      <id>alpn-when-jdk8_11</id>
      <activation>
        <jdk>1.8.0_11</jdk>
      </activation>
      <properties>
        <alpn.jdk8.version>8.1.0.v20141016</alpn.jdk8.version>
      </properties>
    </profile>
    <profile>
      <id>alpn-when-jdk8_20</id>
      <activation>
        <jdk>1.8.0_20</jdk>
      </activation>
      <properties>
        <alpn.jdk8.version>8.1.0.v20141016</alpn.jdk8.version>
      </properties>
    </profile>
    <profile>
      <id>alpn-when-jdk8_25</id>
      <activation>
        <jdk>1.8.0_25</jdk>
      </activation>
      <properties>
        <alpn.jdk8.version>8.1.2.v20141202</alpn.jdk8.version>
      </properties>
    </profile>
    <profile>
      <id>alpn-when-jdk8_31</id>
      <activation>
        <jdk>1.8.0_31</jdk>
      </activation>
      <properties>
        <alpn.jdk8.version>8.1.3.v20150130</alpn.jdk8.version>
      </properties>
    </profile>
    <profile>
      <id>alpn-when-jdk8_40</id>
      <activation>
        <jdk>1.8.0_40</jdk>
      </activation>
      <properties>
        <alpn.jdk8.version>8.1.3.v20150130</alpn.jdk8.version>
      </properties>
    </profile>
    <profile>
      <id>alpn-when-jdk8_45</id>
      <activation>
        <jdk>1.8.0_45</jdk>
      </activation>
      <properties>
        <alpn.jdk8.version>8.1.3.v20150130</alpn.jdk8.version>
      </properties>
    </profile>
    <profile>
      <id>alpn-when-jdk8_51</id>
      <activation>
        <jdk>1.8.0_51</jdk>
      </activation>
      <properties>
        <alpn.jdk8.version>8.1.4.v20150727</alpn.jdk8.version>
      </properties>
    </profile>
    <profile>
      <id>alpn-when-jdk8_60</id>
      <activation>
        <jdk>1.8.0_60</jdk>
      </activation>
      <properties>
        <alpn.jdk8.version>8.1.5.v20150921</alpn.jdk8.version>
      </properties>
    </profile>
    <profile>
      <id>alpn-when-jdk8_65</id>
      <activation>
        <jdk>1.8.0_65</jdk>
      </activation>
      <properties>
        <alpn.jdk8.version>8.1.6.v20151105</alpn.jdk8.version>
      </properties>
    </profile>
    <profile>
      <id>alpn-when-jdk8_66</id>
      <activation>
        <jdk>1.8.0_66</jdk>
      </activation>
      <properties>
        <alpn.jdk8.version>8.1.6.v20151105</alpn.jdk8.version>
      </properties>
    </profile>
    <profile>
      <id>alpn-when-jdk8_71</id>
      <activation>
        <jdk>1.8.0_71</jdk>
      </activation>
      <properties>
        <alpn.jdk8.version>8.1.7.v20160121</alpn.jdk8.version>
      </properties>
    </profile>
    <profile>
      <id>alpn-when-jdk8_72</id>
      <activation>
        <jdk>1.8.0_72</jdk>
      </activation>
      <properties>
        <alpn.jdk8.version>8.1.7.v20160121</alpn.jdk8.version>
      </properties>
    </profile>
    <profile>
      <id>alpn-when-jdk8_73</id>
      <activation>
        <jdk>1.8.0_73</jdk>
      </activation>
      <properties>
        <alpn.jdk8.version>8.1.7.v20160121</alpn.jdk8.version>
      </properties>
    </profile>
    <profile>
      <id>alpn-when-jdk8_74</id>
      <activation>
        <jdk>1.8.0_74</jdk>
      </activation>
      <properties>
        <alpn.jdk8.version>8.1.7.v20160121</alpn.jdk8.version>
      </properties>
    </profile>
    <profile>
      <id>alpn-when-jdk8_77</id>
      <activation>
        <jdk>1.8.0_77</jdk>
      </activation>
      <properties>
        <alpn.jdk8.version>8.1.7.v20160121</alpn.jdk8.version>
      </properties>
    </profile>
    <profile>
      <id>alpn-when-jdk8_91</id>
      <activation>
        <jdk>1.8.0_91</jdk>
      </activation>
      <properties>
        <alpn.jdk8.version>8.1.7.v20160121</alpn.jdk8.version>
      </properties>
    </profile>
    <profile>
      <id>alpn-when-jdk8_92</id>
      <activation>
        <jdk>1.8.0_92</jdk>
      </activation>
      <properties>
        <alpn.jdk8.version>8.1.8.v20160420</alpn.jdk8.version>
      </properties>
    </profile>
    <profile>
      <id>alpn-when-jdk8_101</id>
      <activation>
        <jdk>1.8.0_101</jdk>
      </activation>
      <properties>
        <alpn.jdk8.version>8.1.8.v20160420</alpn.jdk8.version>
      </properties>
    </profile>
    <profile>
      <id>alpn-when-jdk8_102</id>
      <activation>
        <jdk>1.8.0_102</jdk>
      </activation>
      <properties>
        <alpn.jdk8.version>8.1.9.v20160720</alpn.jdk8.version>
      </properties>
    </profile>
    <profile>
      <id>alpn-when-jdk8_111</id>
      <activation>
        <jdk>1.8.0_111</jdk>
      </activation>
      <properties>
        <alpn.jdk8.version>8.1.9.v20160720</alpn.jdk8.version>
      </properties>
    </profile>
    <profile>
      <id>alpn-when-jdk8_112</id>
      <activation>
        <jdk>1.8.0_112</jdk>
      </activation>
      <properties>
        <alpn.jdk8.version>8.1.9.v20160720</alpn.jdk8.version>
      </properties>
    </profile>
    <profile>
      <id>alpn-when-jdk8_121</id>
      <activation>
        <jdk>1.8.0_121</jdk>
      </activation>
      <properties>
        <alpn.jdk8.version>8.1.11.v20170118</alpn.jdk8.version>
      </properties>
    </profile>
    <profile>
      <id>alpn-when-jdk8_131</id>
      <activation>
        <jdk>1.8.0_131</jdk>
      </activation>
      <properties>
        <alpn.jdk8.version>8.1.11.v20170118</alpn.jdk8.version>
      </properties>
    </profile>
    <profile>
      <id>alpn-when-jdk8_141</id>
      <activation>
        <jdk>1.8.0_141</jdk>
      </activation>
      <properties>
        <alpn.jdk8.version>8.1.11.v20170118</alpn.jdk8.version>
      </properties>
    </profile>
    <profile>
      <id>alpn-when-jdk8_144</id>
      <activation>
        <jdk>1.8.0_144</jdk>
      </activation>
      <properties>
        <alpn.jdk8.version>8.1.11.v20170118</alpn.jdk8.version>
      </properties>
    </profile>
    <profile>
      <id>alpn-when-jdk8_151</id>
      <activation>
        <jdk>1.8.0_151</jdk>
      </activation>
      <properties>
        <alpn.jdk8.version>8.1.11.v20170118</alpn.jdk8.version>
      </properties>
    </profile>
    <profile>
      <id>alpn-when-jdk8_152</id>
      <activation>
        <jdk>1.8.0_152</jdk>
      </activation>
      <properties>
        <alpn.jdk8.version>8.1.11.v20170118</alpn.jdk8.version>
      </properties>
    </profile>
    <profile>
      <id>alpn-when-jdk8_161</id>
      <activation>
        <jdk>1.8.0_161</jdk>
      </activation>
      <properties>
        <alpn.jdk8.version>8.1.12.v20180117</alpn.jdk8.version>
      </properties>
    </profile>
    <profile>
      <id>alpn-when-jdk8_162</id>
      <activation>
        <jdk>1.8.0_162</jdk>
      </activation>
      <properties>
        <alpn.jdk8.version>8.1.12.v20180117</alpn.jdk8.version>
      </properties>
    </profile>
    <profile>
      <id>alpn-when-jdk8_171</id>
      <activation>
        <jdk>1.8.0_171</jdk>
      </activation>
      <properties>
        <alpn.jdk8.version>8.1.12.v20180117</alpn.jdk8.version>
      </properties>
    </profile>
    <profile>
      <id>alpn-when-jdk8_172</id>
      <activation>
        <jdk>1.8.0_172</jdk>
      </activation>
      <properties>
        <alpn.jdk8.version>8.1.12.v20180117</alpn.jdk8.version>
      </properties>
    </profile>
    <profile>
      <id>alpn-when-jdk8_181</id>
      <activation>
        <jdk>1.8.0_181</jdk>
      </activation>
      <properties>
        <alpn.jdk8.version>8.1.12.v20180117</alpn.jdk8.version>
      </properties>
    </profile>
    <profile>
      <id>alpn-when-jdk8_191</id>
      <activation>
        <jdk>1.8.0_191</jdk>
      </activation>
      <properties>
        <alpn.jdk8.version>8.1.13.v20181017</alpn.jdk8.version>
      </properties>
    </profile>
    <profile>
      <id>alpn-when-jdk8_192</id>
      <activation>
        <jdk>1.8.0_192</jdk>
      </activation>
      <properties>
        <alpn.jdk8.version>8.1.13.v20181017</alpn.jdk8.version>
      </properties>
    </profile>
    <profile>
      <id>alpn-when-jdk8_201</id>
      <activation>
        <jdk>1.8.0_201</jdk>
      </activation>
      <properties>
        <alpn.jdk8.version>8.1.13.v20181017</alpn.jdk8.version>
      </properties>
    </profile>
    <profile>
      <id>alpn-when-jdk8_202</id>
      <activation>
        <jdk>1.8.0_202</jdk>
      </activation>
      <properties>
        <alpn.jdk8.version>8.1.13.v20181017</alpn.jdk8.version>
      </properties>
    </profile>
  </profiles>
</project>
