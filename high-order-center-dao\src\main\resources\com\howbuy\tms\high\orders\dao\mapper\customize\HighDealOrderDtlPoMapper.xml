<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.tms.high.orders.dao.mapper.customize.HighDealOrderDtlPoMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.tms.high.orders.dao.po.HighDealOrderDtlPo"
             extends="com.howbuy.tms.high.orders.dao.mapper.HighDealOrderDtlPoAutoMapper.BaseResultMap">
  </resultMap>

   <!-- 查询TA下, 用户总购买金额 -->
  <select id="countSumBuyAmt" resultType="DECIMAL" parameterType="map" >
  	select ifnull(sum(t.app_amt), 0) as app_amt
	from high_deal_order_dtl t, payment_order p
	where t.tx_acct_no = #{txAcctNo, jdbcType=VARCHAR}
	and t.ta_trade_dt = #{taTradeDt,jdbcType=VARCHAR}
	and t.fund_code = #{fundCode,jdbcType=VARCHAR}
	and t.tx_app_flag = '0'
	and p.deal_no = t.deal_no
	and p.tx_pmt_flag = '2'
   </select>

   <!-- 查询TA下, 用户总赎回份额-->
  <select id="countSumRedeemVol" resultType="DECIMAL" parameterType="map" >
  	 select ifnull(sum(t.app_vol), 0) as app_vol
	  from high_deal_order_dtl t
	  left join deal_order d
	    on t.deal_no = d.deal_no
	  left join deal_order_extend e
	    on t.deal_no = e.deal_no
	 where t.tx_acct_no = #{txAcctNo, jdbcType = VARCHAR}
	   and t.ta_trade_dt = #{taTradeDt, jdbcType = VARCHAR}
	   and t.fund_code = #{fundCode,jdbcType = VARCHAR}
	   and t.tx_app_flag = '0'
	   and t.m_busi_code = '1124'
	   and d.deal_type = '2'
	   and (e.rec_stat = null or e.rec_stat = '0')
   </select>

   <!-- 查询未完成的认申购记录数 -->
   <select id="queryNotCompleteOrderDtlForModifyDiv" resultType="int" parameterType="map" >
   	   	select count(1)
   	   	from high_deal_order_dtl t
		where t.tx_acct_no = #{txAcctNo, jdbcType=VARCHAR}
		and t.fund_code = #{fundCode, jdbcType=VARCHAR}
		and t.fund_share_class = #{fundShareClass, jdbcType=VARCHAR}
		and t.m_busi_code = '1129'
		and t.tx_app_flag = '0'
		and (t.tx_ack_flag is null or t.tx_ack_flag in ('1', '2', '3'))
   </select>

   <sql id="Extend_Column_List">
	    T1.DEAL_DTL_NO, T1.DEAL_NO, T1.FUND_CODE, T1.FUND_NAME, T1.FUND_TYPE, T1.FUND_SUB_TYPE, T1.FUND_SHARE_CLASS,
	    T1.APP_AMT, T1.NET_APP_AMT, T1.APP_VOL, T1.REDEEM_DIRECTION, T1.DISCOUNT_RATE, T1.TX_APP_FLAG,
	    T1.RISK_FLAG, T1.LARGE_REDM_FLAG, T1.ALLOW_DT, T1.FUND_DIV_MODE, T1.M_BUSI_CODE, T1.FEE,
	    T1.ACK_AMT, T1.ACK_VOL, T1.ACK_DT, T1.TX_ACK_FLAG, T1.TA_TRADE_DT, T1.CANCEL_ORDER_SRC, T1.NOTIFY_SUBMIT_FLAG, T1.MEMO,
	    T1.UPDATE_DTM, T1.CREATE_DTM, T1.DIS_CODE, T1.OUTLET_CODE, T1.TA_CODE, T1.TX_ACCT_NO,T2.TX_PMT_FLAG,T1.PRODUCT_CLASS ,T1.SUPPORT_ADVANCE_FLAG,
	    T1.SUBMIT_TA_DT,T1.APPOINTMENT_DEALNO_TYPE,T1.MERGE_SUBMIT_FLAG,T1.MAIN_DEAL_ORDER_NO,T1.HIGH_FUND_INV_PLAN_FLAG,T1.APPOINT_ID,T1.CALM_DTM
  	</sql>

	<resultMap id="ExtendResultMap" type="com.howbuy.tms.high.orders.dao.vo.HighDealOrderDtlVo">
	    <id column="DEAL_DTL_NO" jdbcType="VARCHAR" property="dealDtlNo" />
	    <result column="DEAL_NO" jdbcType="VARCHAR" property="dealNo" />
	    <result column="FUND_CODE" jdbcType="VARCHAR" property="fundCode" />
	    <result column="FUND_NAME" jdbcType="VARCHAR" property="fundName" />
	    <result column="FUND_TYPE" jdbcType="VARCHAR" property="fundType" />
	    <result column="FUND_SUB_TYPE" jdbcType="VARCHAR" property="fundSubType" />
	    <result column="FUND_SHARE_CLASS" jdbcType="CHAR" property="fundShareClass" />
	    <result column="APP_AMT" jdbcType="DECIMAL" property="appAmt" />
        <result column="NET_APP_AMT" jdbcType="DECIMAL" property="netAppAmt" />
        <result column="APP_VOL" jdbcType="DECIMAL" property="appVol" />
	    <result column="REDEEM_DIRECTION" jdbcType="CHAR" property="redeemDirection" />
	    <result column="DISCOUNT_RATE" jdbcType="DECIMAL" property="discountRate" />
	    <result column="TX_APP_FLAG" jdbcType="CHAR" property="txAppFlag" />
	    <result column="RISK_FLAG" jdbcType="CHAR" property="riskFlag" />
	    <result column="LARGE_REDM_FLAG" jdbcType="CHAR" property="largeRedmFlag" />
	    <result column="ALLOW_DT" jdbcType="VARCHAR" property="allowDt" />
	    <result column="FUND_DIV_MODE" jdbcType="CHAR" property="fundDivMode" />
	    <result column="M_BUSI_CODE" jdbcType="CHAR" property="mBusiCode" />
	    <result column="FEE" jdbcType="DECIMAL" property="fee" />
	    <result column="ACK_AMT" jdbcType="DECIMAL" property="ackAmt" />
	    <result column="ACK_VOL" jdbcType="DECIMAL" property="ackVol" />
	    <result column="ACK_DT" jdbcType="VARCHAR" property="ackDt" />
	    <result column="TX_ACK_FLAG" jdbcType="CHAR" property="txAckFlag" />
	    <result column="TA_TRADE_DT" jdbcType="VARCHAR" property="taTradeDt" />
	    <result column="CANCEL_ORDER_SRC" jdbcType="CHAR" property="cancelOrderSrc" />
	    <result column="NOTIFY_SUBMIT_FLAG" jdbcType="CHAR" property="notifySubmitFlag" />
	    <result column="MEMO" jdbcType="VARCHAR" property="memo" />
	    <result column="UPDATE_DTM" jdbcType="TIMESTAMP" property="updateDtm" />
	    <result column="CREATE_DTM" jdbcType="TIMESTAMP" property="createDtm" />
	    <result column="DIS_CODE" jdbcType="VARCHAR" property="disCode" />
        <result column="APPOINT_ID" jdbcType="VARCHAR" property="appointId" />
	    <result column="OUTLET_CODE" jdbcType="VARCHAR" property="outletCode" />
	    <result column="TA_CODE" jdbcType="VARCHAR" property="taCode" />
	    <result column="TX_ACCT_NO" jdbcType="VARCHAR" property="txAcctNo" />
	    <result column="TX_PMT_FLAG" jdbcType="VARCHAR" property="txPmtFlag" />
	    <result column="PRODUCT_CLASS" jdbcType="VARCHAR" property="productClass" />
	    <result column="CALM_DTM" jdbcType="TIMESTAMP" property="calmDtm" />
	    <result column="SUPPORT_ADVANCE_FLAG" jdbcType="VARCHAR" property="supportAdvanceFlag"/>
	    <result column="SUBMIT_TA_DT" jdbcType="VARCHAR" property="submitTaDt"/>
	    <result column="APPOINTMENT_DEALNO_TYPE" jdbcType="VARCHAR" property="appointmentDealNoType"/>
	    <result column="MERGE_SUBMIT_FLAG" jdbcType="VARCHAR" property="mergeSubmitFlag"/>
	    <result column="MAIN_DEAL_ORDER_NO" jdbcType="VARCHAR" property="mainDealOrderNo"/>
        <result column="HIGH_FUND_INV_PLAN_FLAG" jdbcType="VARCHAR" property="highFundInvPlanFlag"/>
  	</resultMap>

    <!-- 关联支付明细表查询撤单明细订单 -->
	<select id="selectCancelByDealNo" resultMap="ExtendResultMap" parameterType="java.lang.String">
		SELECT <include refid="Extend_Column_List" />
		from high_deal_order_dtl T1 Left JOIN PAYMENT_ORDER T2 on
		T1.DEAL_NO = T2.DEAL_NO
		where T1.TX_APP_FLAG <![CDATA[<>]]> '2'
		and T1.TX_APP_FLAG <![CDATA[<>]]> '3'
		AND T1.DEAL_NO = #{dealNo}
	</select>

	<update id="updateCancelOrder" parameterType="map">
		update high_deal_order_dtl
		set TX_APP_FLAG = #{txAppFlag,jdbcType=CHAR},
		CANCEL_ORDER_SRC = #{cancelOrderSrc,jdbcType=CHAR},
		NOTIFY_SUBMIT_FLAG = #{notifySubmitFlag,jdbcType=CHAR},
		UPDATE_DTM = #{curDate,jdbcType=TIMESTAMP},
		REFUND_DT = #{refundDt,jdbcType=VARCHAR}
		where DEAL_DTL_NO = #{dealDtlNo,jdbcType=VARCHAR}
		AND UPDATE_DTM = #{updateDtm,jdbcType=TIMESTAMP}
	</update>

	 <resultMap id="QueryHighOrderDtlMap" type="com.howbuy.tms.high.orders.dao.vo.QueryHighDealOrderDtlVo">
	    <result column="DEAL_NO" jdbcType="VARCHAR" property="dealNo" />
	    <result column="FUND_CODE" jdbcType="VARCHAR" property="fundCode" />
	    <result column="FUND_NAME" jdbcType="VARCHAR" property="fundName" />
	    <result column="FUND_TYPE" jdbcType="VARCHAR" property="fundType" />
	    <result column="FUND_SHARE_CLASS" jdbcType="CHAR" property="fundShareClass" />
	    <result column="APP_AMT" jdbcType="DECIMAL" property="appAmt" />
        <result column="SUBS_AMT" jdbcType="DECIMAL" property="subsAmt" />
	    <result column="APP_VOL" jdbcType="DECIMAL" property="appVol" />
	    <result column="REDEEM_DIRECTION" jdbcType="CHAR" property="redeemDirection" />
       <result column="DIRECT_REDEEM_DIRECTION" jdbcType="VARCHAR" property="directRedeemDirection" />
	    <result column="DISCOUNT_RATE" jdbcType="DECIMAL" property="discountRate" />
	    <result column="TX_APP_FLAG" jdbcType="CHAR" property="txAppFlag" />
	    <result column="FUND_DIV_MODE" jdbcType="CHAR" property="fundDivMode" />
	    <result column="M_BUSI_CODE" jdbcType="CHAR" property="mBusiCode" />
	    <result column="FEE" jdbcType="DECIMAL" property="fee" />
	    <result column="ACK_AMT" jdbcType="DECIMAL" property="ackAmt" />
	    <result column="ACK_VOL" jdbcType="DECIMAL" property="ackVol" />
	    <result column="ACK_DT" jdbcType="VARCHAR" property="ackDt" />
	    <result column="TX_ACK_FLAG" jdbcType="CHAR" property="txAckFlag" />
	    <result column="TA_TRADE_DT" jdbcType="VARCHAR" property="taTradeDt" />
        <result column="PROTOCOL_TYPE" jdbcType="VARCHAR" property="protocolType" />
	    <result column="MEMO" jdbcType="VARCHAR" property="memo" />
	    <result column="TX_ACCT_NO" jdbcType="VARCHAR" property="txAcctNo" />
	    <result column="NAV" jdbcType="DECIMAL" property="nav" />
	    <result column="APPOINTMENT_DISCOUNT" jdbcType="DECIMAL" property="appointmentDiscount" />
	    <result column="ESITMATE_FEE" jdbcType="DECIMAL" property="esitmateFee" />
     	<result column="PMT_COMPLETE_DTM" jdbcType="TIMESTAMP" property="payTime" />
      	<result column="PAY_STATUS" jdbcType="CHAR" property="payStatus" />
      	<result column="ORDER_STATUS" jdbcType="VARCHAR" property="orderStatus" />
      	<result column="CP_ACCT_NO" jdbcType="VARCHAR" property="cpAcctNo" />
       	<result column="APP_DATE" jdbcType="VARCHAR" property="appDt" />
       	<result column="APP_TIME" jdbcType="VARCHAR" property="appTm" />
       	<result column="PAYMENT_TYPE" jdbcType="VARCHAR" property="paymentType" />
       <result column="DIRECT_PAYMENT_TYPE" jdbcType="VARCHAR" property="directPaymentType" />
       	<result column="PRODUCT_CHANNEL" jdbcType="VARCHAR" property="productChannel" />
       	<result column="ACHIEVEMENT_PAY" jdbcType="DECIMAL" property="achievementPay" />
       	<result column="FIRST_BUY_FLAG" jdbcType="DECIMAL" property="firstBuyFlag" />
       	<result column="CONTRACT_NO" jdbcType="VARCHAR" property="contractNo" />
       	<result column="SCALE_TYPE" jdbcType="VARCHAR" property="scaleType" />
       	<result column="APP_DTM" jdbcType="TIMESTAMP" property="appDtm"/>
       	<result column="ADVANCE_FLAG" jdbcType="VARCHAR" property="advanceFlag"/>
       	<result column="APPOINTMENT_DEALNO_TYPE" jdbcType="VARCHAR" property="appointmentDealNoType"/>
       	<result column="SUBMIT_TA_DT" jdbcType="VARCHAR" property="submitTaDt"/>
        <result column="PRE_APP_VOL" jdbcType="DECIMAL" property="preAppVol" />
        <result column="CXG_DEAL_NO" jdbcType="VARCHAR" property="cxgDealNo"/>
        <result column="CALM_DTM" jdbcType="TIMESTAMP" property="calmDtm"/>
        <result column="DIS_CODE" jdbcType="VARCHAR" property="disCode"/>
        <result column="TX_PMT_FLAG" jdbcType="VARCHAR" property="txPmtFlag"/>
        <result column="PMT_DT" jdbcType="VARCHAR" property="pmtDt"/>
       <result column="IS_VOL_TANSFER" jdbcType="VARCHAR" property="isVolTansfer"/>
       <result column="MERGE_SUBMIT_FLAG" jdbcType="VARCHAR" property="mergeSubmitFlag"/>
       <result column="MAIN_DEAL_ORDER_NO" jdbcType="VARCHAR" property="mainDealOrderNo"/>
       <result column="CURRENCY" jdbcType="VARCHAR" property="currency"/>
       <result column="HIGH_FUND_INV_PLAN_FLAG" jdbcType="VARCHAR" property="highFundInvPlanFlag"/>
       <result column="TX_CHANNEL" jdbcType="VARCHAR" property="txChannel"/>
       <result column="NET_APP_AMT" jdbcType="DECIMAL" property="netAppAmt" />
       <result column="stage_flag" jdbcType="VARCHAR" property="stageFlag" />
       <result column="continuance_flag" jdbcType="VARCHAR" property="continuanceFlag" />
       <result column="TRANSFER_PRICE" jdbcType="DECIMAL" property="transferPrice" />
       <result column="IS_NO_TRADE_TRANSFER" jdbcType="VARCHAR" property="isNoTradeTransfer"/>
       <result column="IS_HK_PRODUCT" jdbcType="VARCHAR" property="isHkProduct" />
       <result column="REDEEM_TYPE" jdbcType="VARCHAR" property="redeemType" />
       <result column="PREBOOKSTATE" jdbcType="VARCHAR" property="preBookState" />
       <result column="directBankAcct" jdbcType="VARCHAR" property="directBankAcct" />
  	</resultMap>

	<!-- 根据订单号查询订单信息 -->
	<select id="selectHighDealOrderDtlByDealNo" resultMap="QueryHighOrderDtlMap" parameterType="map">
		select t1.DEAL_NO,
		       t1.FUND_CODE,
		       t1.FUND_NAME,
		       t1.FUND_TYPE,
		       t1.FUND_SHARE_CLASS,
		       t1.APP_AMT,
		       t1.APP_VOL,
		       t1.REDEEM_DIRECTION,
		       t1.DISCOUNT_RATE,
		       t1.TX_APP_FLAG,
		       t1.FUND_DIV_MODE,
		       t1.M_BUSI_CODE,
		       t1.FEE,
	           t1.TX_ACCT_NO,
		       t1.ACK_AMT,
		       t1.ACK_VOL,
		       t1.ACK_DT,
		       t1.TX_ACK_FLAG,
		       t1.TA_TRADE_DT,
		       t1.MEMO,
		       t1.NAV,
		       t1.APPOINTMENT_DISCOUNT,
		       t1.ESITMATE_FEE,
		       t2.PAY_STATUS,
		       t2.ORDER_STATUS,
		       t2.CP_ACCT_NO,
               DATE_FORMAT(t3.PMT_COMPLETE_DTM, '%Y%m%d%H%i%s') as PMT_COMPLETE_DTM,
		       t2.APP_DATE,
		       t2.APP_TIME,
		       t2.PAYMENT_TYPE,
		       t1.PRODUCT_CHANNEL,
		       t1.ACHIEVEMENT_PAY,
		       t1.FIRST_BUY_FLAG,
		       t4.contract_no,
		       '2' as scale_type,
		       t2.APP_DTM,
		       t2.ADVANCE_FLAG,
		       t1.APPOINTMENT_DEALNO_TYPE,
		       t1.submit_ta_dt,
		       t1.PRE_APP_VOL,
		       t1.CXG_DEAL_NO,
		       t1.CALM_DTM,
		       t2.DIS_CODE,
		       t2.TX_CHANNEL,
		       t3.TX_PMT_FLAG,
		       t3.PMT_DT,
		       '' IS_VOL_TANSFER,
		       t1.MERGE_SUBMIT_FLAG,
		       t1.MAIN_DEAL_ORDER_NO,
		       '156' as CURRENCY,
		       t1.HIGH_FUND_INV_PLAN_FLAG,
               t1.STAGE_FLAG,
               t1.CONTINUANCE_FLAG,
               t1.TRANSFER_PRICE,
               t1.IS_NO_TRADE_TRANSFER,
               '0' as IS_HK_PRODUCT,
               t1.SUBS_AMT,
               t1.NET_APP_AMT,
               null as DIRECT_PAYMENT_TYPE,
               null as DIRECT_REDEEM_DIRECTION,
               null as REDEEM_TYPE,
               t2.PROTOCOL_TYPE,
               null as PREBOOKSTATE,
               null as directBankAcct
		 from HIGH_DEAL_ORDER_DTL t1
            left join deal_order t2 on t1.DEAL_NO = t2.DEAL_NO
            left join payment_order t3 on t1.DEAL_NO = t3.DEAL_NO
            left join simu_fund_check_order t4 on t1.deal_dtl_no = t4.deal_dtl_no
		 where t1.DEAL_NO = #{dealNo,jdbcType=VARCHAR}
		 union all
		 select cm.appserialno    as deal_no,
		       cm.fundcode       as fund_code,
		       cm.fundname       as fund_name,
		       null              as fund_type,
		       null              as fund_share_class,
		       cm.appamt         as app_amt,
		       cm.appvol         as app_vol,
		       null              as REDEEM_DIRECTION,
		       cm.discrateofcomm as DISCOUNT_RATE,
		       '0'               as tx_app_flag,
		       cm.divmode        as FUND_DIV_MODE,
		       CONCAT('1', cm.busicode) AS m_busi_code,
		       cm.FEE            as fee,
			   cm.txacctno as TX_ACCT_NO,
		       cm.ACKAMT         as ACK_AMT,
		       cm.ACKVOL         as ACK_VOL,
				CASE
				WHEN cm.orderstate = '2' THEN cm.tradedt
				WHEN cm.orderstate = '3' THEN cm.tradedt
				ELSE NULL
				END AS ACK_DT,
				CASE
				WHEN cm.orderstate = '1' THEN '1'
				WHEN cm.orderstate = '2' THEN '3'
				WHEN cm.orderstate = '3' THEN '4'
				ELSE '0'
				END AS TX_ACK_FLAG,
		       cm.tradedt        as TA_TRADE_DT,
		       null              as MEMO,
               cm.NAV              as NAV,
		       null              as APPOINTMENT_DISCOUNT,
		       cm.FEE            as ESITMATE_FEE,
               cm.paystate  as PAY_STATUS,
               cm.orderstate  as ORDER_STATUS,
		       null              as CP_ACCT_NO,
		       ifnull(cm.paydtm,CONCAT(cm.paydt,'000000')) as PMT_COMPLETE_DTM,
               cm.APP_DT         as APP_DATE,
               cm.APP_TIME       as APP_TIME,
		       null              as PAYMENT_TYPE,
		       '3'               as PRODUCT_CHANNEL,
		       null              as ACHIEVEMENT_PAY,
		       NULL              as FIRST_BUY_FLAG,
		       null              as CONTRACT_NO,
		        '1'              as scale_type,
              STR_TO_DATE(cm.tradedt ,'%Y%m%d') as APP_DTM,
		      ''                 as ADVANCE_FLAG,
		      ''				 as APPOINTMENT_DEALNO_TYPE,
		      cm.tradedt         as SUBMIT_TA_DT,
		      cm.appvol         as PRE_APP_VOL,
              '' as CXG_DEAL_NO,
              null as CALM_DTM,
              '' as DIS_CODE,
              '' as TX_CHANNEL,
              '' as TX_PMT_FLAG,
              '' as PMT_DT,
               cm.IS_VOL_TANSFER,
		      null as MERGE_SUBMIT_FLAG,
		      null as MAIN_DEAL_ORDER_NO,
		      cm.CURRENCY,
		      null as HIGH_FUND_INV_PLAN_FLAG,
              null as STAGE_FLAG,
              null as CONTINUANCE_FLAG,
              cm.TRANSFER_PRICE,
              cm.IS_NO_TRADE_TRANSFER,
              cm.IS_HK_PRODUCT,
              null as  SUBS_AMT,
              cm.appamt  as NET_APP_AMT,
              cm.PAY_TYPE as DIRECT_PAYMENT_TYPE,
              cm.REDEEM_DIRECTION as DIRECT_REDEEM_DIRECTION,
              cm.REDEEM_TYPE as REDEEM_TYPE,
              null as PROTOCOL_TYPE,
              cm.PREBOOKSTATE,
              cm.BANKACCT  as directBankAcct
		  from cm_custtrade_direct cm
		  where cm.appserialno = #{dealNo,jdbcType=VARCHAR}
	</select>

	<!-- 根据订主单号查询合并上报订单列表 -->
	<select id="selectMergeSubmitOrderDtlByMainDealNo" resultMap="QueryHighOrderDtlMap" parameterType="map">
		select t1.DEAL_NO,
		       t1.FUND_CODE,
		       t1.FUND_NAME,
		       t1.FUND_TYPE,
		       t1.FUND_SHARE_CLASS,
		       t1.APP_AMT,
		       t1.NET_APP_AMT,
		       t1.APP_VOL,
		       t1.REDEEM_DIRECTION,
		       t1.DISCOUNT_RATE,
		       t1.TX_APP_FLAG,
		       t1.FUND_DIV_MODE,
		       t1.M_BUSI_CODE,
		       t1.FEE,
		       t1.ACK_AMT,
		       t1.ACK_VOL,
		       t1.ACK_DT,
		       t1.TX_ACK_FLAG,
		       t1.TA_TRADE_DT,
		       t1.MEMO,
		       t1.NAV,
		       t1.APPOINTMENT_DISCOUNT,
		       t1.ESITMATE_FEE,
		       t2.PAY_STATUS,
		       t2.ORDER_STATUS,
		       t2.CP_ACCT_NO,
               DATE_FORMAT(t3.PMT_COMPLETE_DTM, '%Y%m%d%H%i%s')  as PMT_COMPLETE_DTM,
		       t2.APP_DATE,
		       t2.APP_TIME,
		       t2.PAYMENT_TYPE,
		       t1.PRODUCT_CHANNEL,
		       t1.ACHIEVEMENT_PAY,
		       t1.FIRST_BUY_FLAG,
		       t4.contract_no,
		       '2' as scale_type,
		       t2.APP_DTM,
		       t2.ADVANCE_FLAG,
		       t1.APPOINTMENT_DEALNO_TYPE,
		       t1.submit_ta_dt,
		       t1.PRE_APP_VOL,
		       t1.CXG_DEAL_NO,
		       t1.CALM_DTM,
		       t2.DIS_CODE,
		       t3.TX_PMT_FLAG,
		       t3.PMT_DT,
		       '' IS_VOL_TANSFER,
		       t1.MERGE_SUBMIT_FLAG,
		       t1.MAIN_DEAL_ORDER_NO,
		       '156' as CURRENCY,
		       t1.HIGH_FUND_INV_PLAN_FLAG,
               t1.CONTINUANCE_FLAG,
               t1.STAGE_FLAG,
               t1.TRANSFER_PRICE,
               t1.IS_NO_TRADE_TRANSFER
		 from HIGH_DEAL_ORDER_DTL t1
      left join deal_order t2 on t1.DEAL_NO = t2.DEAL_NO
      left join payment_order t3 on t1.DEAL_NO = t3.DEAL_NO
      left join simu_fund_check_order t4 on t1.deal_dtl_no = t4.deal_dtl_no
		 where t1.MAIN_DEAL_ORDER_NO = #{mainDealNo,jdbcType=VARCHAR}
		 order by t1.DEAL_NO
	</select>

	<resultMap id="ProductTotalSalesResultMap" type="com.howbuy.tms.high.orders.dao.vo.ProductTotalSalesVo">
	    <result column="TOTAL_SALES" jdbcType="DECIMAL" property="totalSales" />
	    <result column="TOTAL_PLACES" jdbcType="DECIMAL" property="totalPlaces" />
	</resultMap>
	<!-- 查询产品销售累计金额和人数 -->
	<select id="selectTotalSalesAndPlaces" resultMap="ProductTotalSalesResultMap">
		select ifnull(sum(TOTAL_SALES),0) as TOTAL_SALES,
			count(1) as TOTAL_PLACES from(
			select t.tx_acct_no,ifnull(sum(t.app_amt),0) as TOTAL_SALES
			from high_deal_order_dtl t,deal_order a,payment_order p
			<where>
				t.fund_code = #{fundCode,jdbcType=VARCHAR}
				and t.tx_app_flag = '0'
				and p.tx_pmt_flag = '2'
				and t.deal_no = a.deal_no
				and t.deal_no = p.deal_no
				<if test="startDt !=null">
				 	and t.ta_trade_dt &gt;= #{startDt,jdbcType=TIMESTAMP}
				</if>
				<if test="endDt !=null">
					and t.ta_trade_dt &lt;= #{endDt,jdbcType=TIMESTAMP}
				</if>
				<if test="disCode != null">
					and t.dis_code = #{disCode,jdbcType=VARCHAR}
				</if>
				<if test="channelCode != null">
					and a.channel_code = #{channelCode,jdbcType=VARCHAR}
				</if>
				<if test="txAcctNo != null">
					and t.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
				</if>
			</where>
			group by t.tx_acct_no
		) tt
	</select>

	<!-- 查询成功使用的预约订单号 -->
	<select id="selectUsedAppointmentDealNo" parameterType="map" resultType="int">
	  select count(1)
	  from deal_order_extend d
	  where d.appointment_deal_no = #{appointmentDealNo,jdbcType=VARCHAR}
	  and (d.rec_stat = null or d.rec_stat = '0')
	</select>

	<!-- 查询用户某个产品最近一笔成功支付的订单 -->
	<select id="selectLastWithPaySuccessLimitType" parameterType="map" resultType="string">
		select limit_type
		  from (select t.limit_type as limit_type
		          from high_deal_order_dtl t
		          left join payment_order p
		            on t.deal_no = p.deal_no
		         where t.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
		           and t.dis_code = #{disCode,jdbcType=VARCHAR}
		           and t.fund_code = #{fundCode,jdbcType=VARCHAR}
		           and t.tx_app_flag = '0'
		           and t.first_buy_flag = '1'
		           and t.product_class = '3'
		           and p.tx_pmt_flag = '2'
		         order by p.pmt_complete_dtm desc
                limit 1) a
	</select>

	<select id="countIntransitTrade" parameterType="map" resultType="java.lang.Integer">
        select
        	count(1)
       	from
       		high_deal_order_dtl f
       	left join
       		deal_order d
       	on f.deal_no = d.deal_no
      where f.m_busi_code in ('1120', '1122', '1124')
        and f.tx_acct_no = #{txAcctNo,jdbcType = VARCHAR}
        and f.dis_code = #{disCode,jdbcType = VARCHAR}
        and d.cp_acct_no = #{cpAcctNo,jdbcType = VARCHAR}
        <if test="list != null and list.size() > 0 ">
            and f.fund_code in
            <foreach collection="list" index="index" item="fundCode" open="(" separator="," close=")">
                #{fundCode}
            </foreach>
        </if>
	 	<if test="protocolNo != null ">
			and d.protocol_no = #{protocolNo,jdbcType = VARCHAR}
	 	</if>
        and f.tx_app_flag = '0'
        and (f.tx_ack_flag is null or f.tx_ack_flag in ('1', '2'))
  </select>

  <!-- 查询份额迁移转入资金账号转出的在途交易 -->
  <select id="countTransferOutIntransitTrade" parameterType="java.lang.String" resultType="java.lang.Integer">
        select
          count(1)
         from
           high_deal_order_dtl f
      where f.m_busi_code = '1365'
        and f.tx_acct_no = #{txAcctNo,jdbcType = VARCHAR}
        and f.dis_code = #{disCode,jdbcType = VARCHAR}
        and f.cp_acct_no = #{cpAcctNo,jdbcType = VARCHAR}
        <if test="protocolNo != null ">
			and f.protocol_no = #{protocolNo,jdbcType = VARCHAR}
	 	</if>
        and f.tx_app_flag = '0'
        and (f.tx_ack_flag is null or f.tx_ack_flag in ('1', '2', '3'))
        and f.create_dtm &gt;= NOW() - INTERVAL 30 DAY
  </select>

  <!-- 统计用户指定产品(代销系统)的股权回款金额 -->
  <select id="countDxGqhkAmt" parameterType="map" resultType="DECIMAL">
  	 SELECT SUM(ifnull(T.ACK_AMT, 0))
  	    FROM HIGH_DEAL_ORDER_DTL T
	 WHERE  T.TX_ACCT_NO = #{txAcctNo,jdbcType=VARCHAR}
        <if test="disCodeList != null and disCodeList.size() > 0 ">
          and T.DIS_CODE  in
          <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
            #{disCode}
          </foreach>
        </if>
		AND T.FUND_CODE = #{productCode,jdbcType=VARCHAR}
		AND T.M_BUSI_CODE = '1143'
  </select>

  <!-- 统计用户指定产品(crm)的股权回款金额 -->
  <select id="countCrmGqhkAmt" parameterType="map" resultType="DECIMAL">
  	 SELECT SUM(ifnull(T.ACKAMT, 0))
      FROM CM_CUSTTRADE_DIRECT T
     WHERE T.HBONENO = #{hbOneNo,jdbcType=VARCHAR}
       AND T.FUNDCODE = #{productCode,jdbcType=VARCHAR}
       AND T.BUSICODE = '999'
  </select>
  <select id="selectDtlByDealNo" parameterType="java.lang.String" resultMap="com.howbuy.tms.high.orders.dao.mapper.HighDealOrderDtlPoAutoMapper.BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="com.howbuy.tms.high.orders.dao.mapper.HighDealOrderDtlPoAutoMapper.Base_Column_List" />
    from HIGH_DEAL_ORDER_DTL
    where DEAL_NO = #{dealNo,jdbcType=VARCHAR}
  </select>

  <!-- 订单确认标记
        0-无需确认
        1-未确认
        2-确认中
        3-部分确认
        4-确认成功
        5-确认失败

        订单申请标记
        0-申请成功；
        1-申请失败；
        2-自行撤销；
        3-强制取消
  -->
  <select id="selectDtlCheck" parameterType="java.lang.String" resultMap="com.howbuy.tms.high.orders.dao.mapper.HighDealOrderDtlPoAutoMapper.BaseResultMap">
    select
    <include refid="com.howbuy.tms.high.orders.dao.mapper.HighDealOrderDtlPoAutoMapper.Base_Column_List" />
    from HIGH_DEAL_ORDER_DTL t1
    where t1.m_busi_code in ('1120', '1122', '1124')
    and (t1.TX_ACK_FLAG in('1', '2') or t1.TX_ACK_FLAG is null)
    and t1.TX_APP_FLAG in('0')
    <if test="mBusiCode != null and mBusiCode !=''">
      and t1.M_BUSI_CODE=#{mBusiCode}
    </if>
      <if test="fundCodes != null and fundCodes.size() > 0 ">
        and t1.fund_code in
          <foreach collection="fundCodes" index="index" item="item" open="(" separator="," close=")">
            #{item}
          </foreach>
      </if>
      and t1.SUBMIT_TA_DT in
      <foreach collection="submitTaDts" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
  </select>

  <select id="selectDtlForCheck" parameterType="java.lang.String" resultMap="com.howbuy.tms.high.orders.dao.mapper.HighDealOrderDtlPoAutoMapper.BaseResultMap">
    select
    <include refid="com.howbuy.tms.high.orders.dao.mapper.HighDealOrderDtlPoAutoMapper.Base_Column_List" />
    from HIGH_DEAL_ORDER_DTL t1
    where t1.m_busi_code in ('1120', '1122', '1124')
    and (t1.TX_ACK_FLAG in('1', '2') or t1.TX_ACK_FLAG is null)
    and t1.TX_APP_FLAG in('0')
    <if test="mBusiCodeList != null and mBusiCodeList.size() > 0">
      and t1.m_busi_code in
      <foreach item="item" index="index" collection="mBusiCodeList" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="fundTypeList != null and fundTypeList.size() > 0">
      and t1.fund_type in
      <foreach item="item" index="index" collection="fundTypeList" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="fundCodes != null and fundCodes.size() > 0 ">
      and t1.fund_code in
      <foreach collection="fundCodes" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    and t1.SUBMIT_TA_DT in
    <foreach collection="submitTaDts" index="index" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

  <resultMap id="QueryHighOrderDtlForEsMap" type="com.howbuy.tms.high.orders.dao.vo.HighDealOrderDtlForEsVo">
	    <result column="DEAL_NO" jdbcType="VARCHAR" property="dealNo" />
	    <result column="FUND_CODE" jdbcType="VARCHAR" property="fundCode" />
	    <result column="FUND_NAME" jdbcType="VARCHAR" property="fundName" />
	    <result column="TX_CODE" jdbcType="VARCHAR" property="txCode" />
	    <result column="APP_DATE" jdbcType="VARCHAR" property="appDate" />
       	<result column="DIS_CODE" jdbcType="VARCHAR" property="disCode" />
	    <result column="PAY_STATUS" jdbcType="VARCHAR" property="payStatus" />
      	<result column="ORDER_STATUS" jdbcType="VARCHAR" property="orderStatus" />
      	<result column="M_BUSI_CODE" jdbcType="VARCHAR" property="mBusiCode" />
	    <result column="PRODUCT_SUB_TYPE" jdbcType="VARCHAR" property="productSubType" />
	    <result column="FUND_DIV_MODE" jdbcType="CHAR" property="fundDivMode" />
	    <result column="TA_TRADE_DT" jdbcType="VARCHAR" property="taTradeDt" />
	    <result column="APP_AMT" jdbcType="DECIMAL" property="appAmt" />
	    <result column="APP_VOL" jdbcType="DECIMAL" property="appVol" />
	    <result column="APP_RATIO" jdbcType="DECIMAL" property="appRatio" />
	    <result column="ACK_DT" jdbcType="VARCHAR" property="ackDt" />
	    <result column="APP_TIME" jdbcType="VARCHAR" property="appTime" />
       	<result column="APP_DTM" jdbcType="TIMESTAMP" property="appDtm"/>
       	<result column="MEMO" jdbcType="VARCHAR" property="memo" />
	    <result column="UPDATE_DTM" jdbcType="TIMESTAMP" property="updateDtm"/>
       	<result column="CREATE_DTM" jdbcType="TIMESTAMP" property="createDtm"/>
       	<result column="TX_ACCT_NO" jdbcType="VARCHAR" property="txAcctNo" />
	    <result column="CP_ACCT_NO" jdbcType="VARCHAR" property="cpAcctNo" />
	    <result column="ACK_AMT" jdbcType="DECIMAL" property="ackAmt" />
	    <result column="REC_STAT" jdbcType="VARCHAR" property="recStat" />
        <result column="IS_VOL_TANSFER" jdbcType="VARCHAR" property="isVolTansfer" />
        <result column="MERGE_SUBMIT_FLAG" jdbcType="VARCHAR" property="mergeSubmitFlag" />
        <result column="MAIN_DEAL_ORDER_NO" jdbcType="VARCHAR" property="mainDealOrderNo" />
        <result column="currency" jdbcType="VARCHAR" property="currency" />
        <result column="HIGH_FUND_INV_PLAN_FLAG" jdbcType="VARCHAR" property="highFundInvPlanFlag" />
        <result column="PREBOOKSTATE" jdbcType="VARCHAR" property="prebookstate" />
        <result column="TX_ACK_FLAG" jdbcType="VARCHAR" property="txAckFlag" />
        <result column="continuance_flag" jdbcType="VARCHAR" property="continuanceFlag" />
        <result column="isHkProduct" jdbcType="VARCHAR" property="isHkProduct" />
        <result column="redeem_type" jdbcType="VARCHAR" property="redeemType" />
  	</resultMap>

	<!-- 根据订单号查询订单信息 -->
	<select id="selectDtlForEsByDealNo" resultMap="QueryHighOrderDtlForEsMap" parameterType="map">
		select t1.DEAL_NO,
	       ifnull(t1.FUND_CODE,t2.product_code) as FUND_CODE,
           ifnull(t1.FUND_NAME,t2.product_name) as FUND_NAME,
	       t2.TX_CODE,
	       t2.App_Date,
	       T1.DIS_CODE,
	       T2.ORDER_STATUS,
	       t2.Pay_Status,
	       t1.m_Busi_Code,
	       t2.Product_Sub_Type,
	       t1.Fund_Div_Mode,
	       t1.Ta_Trade_Dt,
	       t1.App_Amt,
	       t1.App_Vol,
	       t2.App_Ratio,
	       t1.Ack_Dt,
	       t2.App_Time,
	       t2.App_Dtm,
	       t1.Memo,
	       t1.Update_Dtm,
	       t1.Create_Dtm,
	       t1.Tx_Acct_No,
	       t2.Cp_Acct_No,
	       t1.ack_amt,
	       '0' as REC_STAT,
	       '' as IS_VOL_TANSFER,
	       t1.MERGE_SUBMIT_FLAG,
	       t1.MAIN_DEAL_ORDER_NO,
	       '156' as currency,
	       t1.HIGH_FUND_INV_PLAN_FLAG,
           t1.TX_ACK_FLAG,
           t1.continuance_flag,
           '' as PREBOOKSTATE,
           '0' as isHkProduct,
           null as redeem_type
	  from HIGH_DEAL_ORDER_DTL t1, deal_order t2
	 where t1.DEAL_NO = #{dealNo, jdbcType = VARCHAR}
	   and t1.DEAL_NO = t2.DEAL_NO
	union all
	select cm.appserialno as deal_no,
	       cm.fundcode as fund_code,
	       cm.fundname as fund_name,
	       null as TX_CODE,
	       cm.APP_DT as APP_DATE,
	       cm.discode as DIS_CODE,
	       cm.orderstate as ORDER_STATUS,
	       cm.paystate as PAY_STATUS,
		   CONCAT('1', cm.busicode) AS m_busi_code,
	       null as product_sub_type,
	       cm.divmode as FUND_DIV_MODE,
	       cm.tradedt as TA_TRADE_DT,
	       cm.appamt as app_amt,
	       cm.appvol as app_vol,
	       null as app_ratio,
	       cm.tradedt as ACK_DT,
           cm.APP_TIME as APP_TIME,
           STR_TO_DATE(CONCAT(cm.APP_DT, cm.APP_TIME), '%Y%m%d%H%i%s') as APP_DTM,
	       null as MEMO,
           STR_TO_DATE(cm.tradedt, '%Y%m%d') as Update_Dtm,
           STR_TO_DATE(cm.tradedt, '%Y%m%d') as Create_Dtm,
	       cm.txacctno as Tx_Acct_No,
	       null as Cp_Acct_No,
	       cm.ackamt as ack_amt,
	       cm.RECSTAT as REC_STAT,
	       cm.IS_VOL_TANSFER as IS_VOL_TANSFER,
	       null as MERGE_SUBMIT_FLAG,
	       null as MAIN_DEAL_ORDER_NO,
	       cm.currency,
	       '' as HIGH_FUND_INV_PLAN_FLAG,
           '' as TX_ACK_FLAG,
           '' as continuance_flag,
           PREBOOKSTATE,
           cm.IS_HK_PRODUCT as isHkProduct,
           cm.REDEEM_TYPE as redeem_type
	  from cm_custtrade_direct cm
	 where cm.appserialno = #{dealNo, jdbcType = VARCHAR}
	</select>

  <select id="selectMargeOrderDtlForEsByMainDealNo" resultMap="QueryHighOrderDtlForEsMap" parameterType="map">
		select #{mainDealNo, jdbcType = VARCHAR} as DEAL_NO,
	       min(ifnull(t1.FUND_CODE,t2.product_code)) as FUND_CODE,
	       min(ifnull(t1.FUND_NAME,t2.product_name)) as FUND_NAME,
	       min(t2.TX_CODE) as TX_CODE,
	       min(t2.App_Date) as App_Date,
	       min(T1.DIS_CODE) as DIS_CODE,
           GROUP_CONCAT(t2.ORDER_STATUS ORDER BY t2.DEAL_NO SEPARATOR ',') AS ORDER_STATUS,
           GROUP_CONCAT(t2.PAY_STATUS ORDER BY t2.DEAL_NO SEPARATOR ',') AS PAY_STATUS,
	       min(t1.m_Busi_Code) as m_Busi_Code,
	       min(t2.Product_Sub_Type) as Product_Sub_Type,
	       min(t1.Fund_Div_Mode) as Fund_Div_Mode,
	       min(t1.Ta_Trade_Dt) as Ta_Trade_Dt,
	       sum(t1.App_Amt) as App_Amt,
	       sum(t1.App_Vol) as App_Vol,
	       sum(t2.App_Ratio) as App_Ratio,
	       min(t1.Ack_Dt) as Ack_Dt,
	       min(t2.App_Time) as App_Time,
	       min(t2.App_Dtm) as App_Dtm,
	       min(t1.Memo) as Memo,
	       min(t1.Update_Dtm) as Update_Dtm,
	       min(t1.Create_Dtm) as Create_Dtm,
	       min(t1.Tx_Acct_No) as Tx_Acct_No,
	       min(t2.Cp_Acct_No) as Cp_Acct_No,
	       sum(t1.ack_amt) as ack_amt,
	       '0' as REC_STAT,
	       '' as IS_VOL_TANSFER,
	       min(t1.MERGE_SUBMIT_FLAG) as MERGE_SUBMIT_FLAG,
	       min(t1.MAIN_DEAL_ORDER_NO) as MAIN_DEAL_ORDER_NO,
	       '156' as currency
	  from HIGH_DEAL_ORDER_DTL t1, deal_order t2
	 where t1.MAIN_DEAL_ORDER_NO = #{mainDealNo, jdbcType = VARCHAR}
	   and t1.DEAL_NO = t2.DEAL_NO
	</select>

	<resultMap id="extendLatestAckResultMap" type="com.howbuy.tms.high.orders.dao.vo.HighDealOrderDtlLatestAckVo">
		<id column="deal_no" jdbcType="VARCHAR" property="dealNo" />
	    <id column="DEAL_DTL_NO" jdbcType="VARCHAR" property="dealDtlNo" />
	    <result column="FUND_CODE" jdbcType="VARCHAR" property="fundCode" />
	    <result column="TX_ACCT_NO" jdbcType="VARCHAR" property="txAcctNo" />
	    <result column="FEE" jdbcType="DECIMAL" property="fee" />
	    <result column="ACK_AMT" jdbcType="DECIMAL" property="ackAmt" />
	    <result column="ACK_VOL" jdbcType="DECIMAL" property="ackVol" />
	    <result column="ACK_DT" jdbcType="VARCHAR" property="ackDt" />
	    <result column="SUBMIT_TA_DT" jdbcType="VARCHAR" property="submitTaDt"/>
  	</resultMap>

	<select id="selectAckDealDtl" parameterType="map" resultMap="extendLatestAckResultMap">
	  	 select deal_no,DEAL_DTL_NO,FUND_CODE, TX_ACCT_NO, FEE, ACK_AMT, ACK_VOL, ACK_DT, SUBMIT_TA_DT
		  from high_deal_order_dtl a
		 where a.tx_ack_flag in ('3', '4')
		   and a.m_busi_code in ('1120', '1122')
		   and a.tx_acct_no = #{txAcctNo,jdbcType = VARCHAR}
		   and a.fund_code in
               <foreach collection="list" index="index" item="fundCode" open="(" separator="," close=")">
                   #{fundCode}
               </foreach>
     </select>


  <select id="selectRepurchaseDeals" parameterType="map" resultMap="com.howbuy.tms.high.orders.dao.mapper.HighDealOrderDtlPoAutoMapper.BaseResultMap">
    select <include refid="com.howbuy.tms.high.orders.dao.mapper.HighDealOrderDtlPoAutoMapper.Base_Column_List"/>
    from HIGH_DEAL_ORDER_DTL
    where REPURCHASE_PROTOCOL_NO = #{repurchaseProtocolNo, jdbcType=VARCHAR}
  </select>

  <select id="selectRepurchaseDealsByNos" parameterType="map" resultMap="com.howbuy.tms.high.orders.dao.mapper.HighDealOrderDtlPoAutoMapper.BaseResultMap">
    select <include refid="com.howbuy.tms.high.orders.dao.mapper.HighDealOrderDtlPoAutoMapper.Base_Column_List"/>
    from HIGH_DEAL_ORDER_DTL
    where
    1=1
    and TX_ACCT_NO = #{txAcctNo, jdbcType=VARCHAR}
    <if test="repurchaseProtocolNoList != null and repurchaseProtocolNoList.size() > 0" >
      and REPURCHASE_PROTOCOL_NO in
      <foreach collection="repurchaseProtocolNoList" item="item" separator="," open="(" close=")">
        #{item}
      </foreach>
    </if>
  </select>

  <select id="selectRedeemOnWayVolList" parameterType="map" resultMap="com.howbuy.tms.high.orders.dao.mapper.HighDealOrderDtlPoAutoMapper.BaseResultMap">
      select fund_code, sum(app_vol) app_vol
        from (select h.fund_code, h.app_vol
                from DEAL_ORDER d
               inner join high_deal_order_dtl h
                  on d.deal_no = h.deal_no
                left join DEAL_ORDER_EXTEND e
                  on d.deal_no = e.deal_no
               where d.tx_acct_no = #{txAcctNo}
                 AND d.order_status = '1'
                 AND d.deal_type = '2'
                 AND h.m_busi_code = '1124'
                <if test="disCodeList != null and disCodeList.size() > 0 ">
                  and d.dis_code  in
                  <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                    #{disCode}
                  </foreach>
                </if>
                 AND (e.rec_stat = '0' or e.rec_stat is null)
              union all
              select fundcode fund_code, appvol app_vol
                from CM_CUSTTRADE_DIRECT t3
               where t3.txacctno = #{txAcctNo}
                  <if test="disCodeList != null and disCodeList.size() > 0 ">
                    and t3.discode  in
                    <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                      #{disCode}
                    </foreach>
                  </if>
                 and t3.ORDERSTATE = '1'
                 and t3.busicode = '124') tt
       group by fund_code
  </select>

  <select id="countDivOrderNumAfterDt" parameterType="map" resultType="int">
    select count(1)
      from high_deal_order_dtl
     where tx_acct_no = #{txAcctNo}
       and fund_code = #{fundCode}
       and ack_dt >= #{ackDt}
       and m_busi_code = '1143'
  </select>

  <select id="queryDivOrderNumAfterDt" parameterType="map" resultMap="com.howbuy.tms.high.orders.dao.mapper.HighDealOrderDtlPoAutoMapper.BaseResultMap">
    select
    <include refid="com.howbuy.tms.high.orders.dao.mapper.HighDealOrderDtlPoAutoMapper.Base_Column_List" />
    from high_deal_order_dtl
    where tx_acct_no = #{txAcctNo}
      and fund_code = #{fundCode}
      and ack_dt >= #{ackDt}
      and m_busi_code = '1143'
  </select>

  <select id="getFirstAckInfoForConsignment" parameterType="map" resultMap="com.howbuy.tms.high.orders.dao.mapper.HighDealOrderDtlPoAutoMapper.BaseResultMap">
    select * from (select
    <include refid="com.howbuy.tms.high.orders.dao.mapper.HighDealOrderDtlPoAutoMapper.Base_Column_List" />
    from HIGH_DEAL_ORDER_DTL
    where tx_acct_no = #{txAcctNo, jdbcType=VARCHAR}
    and fund_code = #{fundCode, jdbcType=VARCHAR}
    and m_busi_code in ('1120','1122')
    and TX_ACK_FLAG in ('3','4')
    order by ACK_DT limit 1) a
    </select>

  <select id="getLastAckInfoForConsignment" parameterType="map" resultMap="com.howbuy.tms.high.orders.dao.mapper.HighDealOrderDtlPoAutoMapper.BaseResultMap">
    select * from (select
    <include refid="com.howbuy.tms.high.orders.dao.mapper.HighDealOrderDtlPoAutoMapper.Base_Column_List" />
    from HIGH_DEAL_ORDER_DTL
    where tx_acct_no = #{txAcctNo}
    and fund_code = #{fundCode}
    and m_busi_code in ('1120','1122')
    and TX_ACK_FLAG in ('3','4')
    order by ACK_DT desc limit 1) a
  </select>

  <resultMap id="BalanceOrderMap" type="com.howbuy.tms.high.orders.dao.vo.BalanceOrderVo">
    <result column="orderNo" jdbcType="VARCHAR" property="orderNo"/>
    <result column="fundCode" jdbcType="VARCHAR" property="fundCode"/>
    <result column="mBusinessCode" jdbcType="VARCHAR" property="mBusinessCode"/>
    <result column="businessCode" jdbcType="VARCHAR" property="businessCode"/>
    <result column="ackVol" jdbcType="DECIMAL" property="ackVol"/>
    <result column="ackAmt" jdbcType="DECIMAL" property="ackAmt"/>
    <result column="fee" jdbcType="DECIMAL" property="fee"/>
    <result column="transferPrice" jdbcType="DECIMAL" property="transferPrice"/>
    <result column="isNoTradeTransfer" jdbcType="VARCHAR" property="isNoTradeTransfer"/>
    <result column="tradeDt" jdbcType="VARCHAR" property="tradeDt"/>
    <result column="ackDt" jdbcType="VARCHAR" property="ackDt"/>
  </resultMap>

  <select id="selectBalanceConsignmentOrderVo" parameterType="map" resultMap="BalanceOrderMap">
    select t.DEAL_DTL_NO as orderNo,
    t.FUND_CODE as fundCode,
    t.M_BUSI_CODE as mBusinessCode,
    t.ACK_AMT as ackAmt,
    t.ACK_VOL as ackVol ,
    t.ACK_DT as ackDt,
    t.FEE as fee ,
    t.TRANSFER_PRICE as transferPrice,
    t.IS_NO_TRADE_TRANSFER as isNoTradeTransfer,
    t.ACK_DT as tradeDt
    from high_deal_order_dtl t
    left join deal_order d on t.DEAL_NO=d.DEAL_NO
    where t.tx_ack_flag in ('3','4')
    and t.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
    <if test="disCodeList != null and disCodeList.size() > 0 ">
      and t.dis_code in
      <foreach collection="disCodeList" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="fundCodeList != null and fundCodeList.size() > 0 ">
      and t.fund_code in
      <foreach collection="fundCodeList" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>

  </select>

  <select id="getCxgDjZt" resultType="java.math.BigDecimal" parameterType="map">
    select ifnull(sum(a.net_app_amt),0) from high_deal_order_dtl a
    inner join payment_order b on a.deal_no=b.deal_no
    inner join deal_order c on a.deal_no=c.deal_no
    where a.tx_acct_no= #{txAcctNo,jdbcType=VARCHAR}
    and a.FUND_CODE  in
    <foreach collection="productCodeList" index="index" item="fundCode" open="(" separator="," close=")">
      #{fundCode}
    </foreach>
      and c.order_status='1'
      and  c.pay_status='4'
      and a.m_busi_code in ('1120','1122')
      and b.tx_pmt_flag='11'

    </select>

  <select id="getOnWayAgentDealDtlList" resultMap="com.howbuy.tms.high.orders.dao.mapper.HighDealOrderDtlPoAutoMapper.BaseResultMap" parameterType="com.howbuy.tms.high.orders.dao.vo.QueryDealParamVo">
    select
    t1.*
    from HIGH_DEAL_ORDER_DTL t1
    inner join DEAL_ORDER t2 on t1.DEAL_NO = t2.DEAL_NO
    where t1.M_BUSI_CODE in ('1120', '1122')
    and t2.PROTOCOL_TYPE = '4'
    and t2.ORDER_STATUS = '1'
    and t1.TX_APP_FLAG = '0' and(t1.TX_APP_FLAG is null or t1.TX_APP_FLAG in ('0', '1', '2'))
    <if test="param.fundCodeList != null and param.fundCodeList.size() > 0 ">
      and t1.FUND_CODE in
      <foreach collection="param.fundCodeList" index="index" item="fundCode" open="(" separator="," close=")">
        #{fundCode}
      </foreach>
    </if>
    <if test="param.disCodeList != null and param.disCodeList.size() > 0 ">
      and t1.DIS_CODE in
      <foreach collection="param.disCodeList" index="index" item="disCode" open="(" separator="," close=")">
        #{disCode}
      </foreach>
    </if>
    <if test="param.txAcctNo != null and param.txAcctNo != ''">
      and t1.TX_ACCT_NO=#{param.txAcctNo,jdbcType = VARCHAR}
    </if>
    <if test="param.appointId != null and param.appointId != ''">
      and t1.APPOINT_ID=#{param.appointId,jdbcType = VARCHAR}
    </if>
  </select>

  <select id="queryFirstTradeConfirm" resultMap="com.howbuy.tms.high.orders.dao.mapper.HighDealOrderDtlPoAutoMapper.BaseResultMap">
    select * from (select
    <include refid="com.howbuy.tms.high.orders.dao.mapper.HighDealOrderDtlPoAutoMapper.Base_Column_List" />
    from HIGH_DEAL_ORDER_DTL
    where tx_acct_no = #{txAcctNo, jdbcType=VARCHAR}
    and fund_code = #{productCode, jdbcType=VARCHAR}
   and ACK_DT is not null and ACK_VOL>0 order by ACK_DT asc limit 1) a
  </select>


	<update id="updateByDealDtlNo" parameterType="com.howbuy.tms.high.orders.dao.po.HighDealOrderDtlPo">
		update HIGH_DEAL_ORDER_DTL
		<set>
			<if test="dealNo != null">
				DEAL_NO = #{dealNo,jdbcType=VARCHAR},
			</if>
			<if test="fundCode != null">
				FUND_CODE = #{fundCode,jdbcType=VARCHAR},
			</if>
			<if test="fundName != null">
				FUND_NAME = #{fundName,jdbcType=VARCHAR},
			</if>
			<if test="fundType != null">
				FUND_TYPE = #{fundType,jdbcType=VARCHAR},
			</if>
			<if test="fundSubType != null">
				FUND_SUB_TYPE = #{fundSubType,jdbcType=VARCHAR},
			</if>
			<if test="fundShareClass != null">
				FUND_SHARE_CLASS = #{fundShareClass,jdbcType=CHAR},
			</if>
			<if test="appAmt != null">
				APP_AMT = #{appAmt,jdbcType=DECIMAL},
			</if>
			<if test="appVol != null">
				APP_VOL = #{appVol,jdbcType=DECIMAL},
			</if>
			<if test="redeemDirection != null">
				REDEEM_DIRECTION = #{redeemDirection,jdbcType=CHAR},
			</if>
			<if test="discountRate != null">
				DISCOUNT_RATE = #{discountRate,jdbcType=DECIMAL},
			</if>
			<if test="txAppFlag != null">
				TX_APP_FLAG = #{txAppFlag,jdbcType=CHAR},
			</if>
			<if test="riskFlag != null">
				RISK_FLAG = #{riskFlag,jdbcType=CHAR},
			</if>
			<if test="largeRedmFlag != null">
				LARGE_REDM_FLAG = #{largeRedmFlag,jdbcType=CHAR},
			</if>
			<if test="allowDt != null">
				ALLOW_DT = #{allowDt,jdbcType=VARCHAR},
			</if>
			<if test="fundDivMode != null">
				FUND_DIV_MODE = #{fundDivMode,jdbcType=CHAR},
			</if>
			<if test="mBusiCode != null">
				M_BUSI_CODE = #{mBusiCode,jdbcType=CHAR},
			</if>
			<if test="fee != null">
				FEE = #{fee,jdbcType=DECIMAL},
			</if>
			<if test="ackAmt != null">
				ACK_AMT = #{ackAmt,jdbcType=DECIMAL},
			</if>
			<if test="ackVol != null">
				ACK_VOL = #{ackVol,jdbcType=DECIMAL},
			</if>
			<if test="ackDt != null">
				ACK_DT = #{ackDt,jdbcType=VARCHAR},
			</if>
			<if test="txAckFlag != null">
				TX_ACK_FLAG = #{txAckFlag,jdbcType=CHAR},
			</if>
			<if test="taTradeDt != null">
				TA_TRADE_DT = #{taTradeDt,jdbcType=VARCHAR},
			</if>
			<if test="cancelOrderSrc != null">
				CANCEL_ORDER_SRC = #{cancelOrderSrc,jdbcType=CHAR},
			</if>
			<if test="notifySubmitFlag != null">
				NOTIFY_SUBMIT_FLAG = #{notifySubmitFlag,jdbcType=CHAR},
			</if>
			<if test="memo != null">
				MEMO = #{memo,jdbcType=VARCHAR},
			</if>
			<if test="updateDtm != null">
				UPDATE_DTM = #{updateDtm,jdbcType=TIMESTAMP},
			</if>
			<if test="createDtm != null">
				CREATE_DTM = #{createDtm,jdbcType=TIMESTAMP},
			</if>
			<if test="disCode != null">
				DIS_CODE = #{disCode,jdbcType=VARCHAR},
			</if>
			<if test="outletCode != null">
				OUTLET_CODE = #{outletCode,jdbcType=VARCHAR},
			</if>
			<if test="taCode != null">
				TA_CODE = #{taCode,jdbcType=VARCHAR},
			</if>
			<if test="txAcctNo != null">
				TX_ACCT_NO = #{txAcctNo,jdbcType=VARCHAR},
			</if>
			<if test="nav != null">
				NAV = #{nav,jdbcType=DECIMAL},
			</if>
			<if test="productChannel != null">
				PRODUCT_CHANNEL = #{productChannel,jdbcType=VARCHAR},
			</if>
			<if test="appointmentDiscount != null">
				APPOINTMENT_DISCOUNT = #{appointmentDiscount,jdbcType=DECIMAL},
			</if>
			<if test="esitmateFee != null">
				ESITMATE_FEE = #{esitmateFee,jdbcType=DECIMAL},
			</if>
			<if test="firstBuyFlag != null">
				FIRST_BUY_FLAG = #{firstBuyFlag,jdbcType=VARCHAR},
			</if>
			<if test="esignatureFlag != null">
				ESIGNATURE_FLAG = #{esignatureFlag,jdbcType=VARCHAR},
			</if>
			<if test="econtractFlag != null">
				ECONTRACT_FLAG = #{econtractFlag,jdbcType=VARCHAR},
			</if>
			<if test="unusualTransType != null">
				UNUSUAL_TRANS_TYPE = #{unusualTransType,jdbcType=VARCHAR},
			</if>
			<if test="openEndTime != null">
				OPEN_END_TIME = #{openEndTime,jdbcType=VARCHAR},
			</if>
			<if test="productClass != null">
				PRODUCT_CLASS = #{productClass,jdbcType=VARCHAR},
			</if>
			<if test="advanceAmt != null">
				ADVANCE_AMT = #{advanceAmt,jdbcType=DECIMAL},
			</if>
			<if test="limitType != null">
				LIMIT_TYPE = #{limitType,jdbcType=VARCHAR},
			</if>
			<if test="supportAdvanceFlag != null">
				SUPPORT_ADVANCE_FLAG = #{supportAdvanceFlag,jdbcType=VARCHAR},
			</if>
			<if test="calmTime != null">
				CALM_TIME = #{calmTime,jdbcType=DECIMAL},
			</if>
			<if test="interest != null">
				INTEREST = #{interest,jdbcType=DECIMAL},
			</if>
			<if test="achievementPay != null">
				ACHIEVEMENT_PAY = #{achievementPay,jdbcType=DECIMAL},
			</if>
			<if test="achievementCompen != null">
				ACHIEVEMENT_COMPEN = #{achievementCompen,jdbcType=DECIMAL},
			</if>
			<if test="volByInterest != null">
				VOL_BY_INTEREST = #{volByInterest,jdbcType=DECIMAL},
			</if>
			<if test="recuperateFee != null">
				RECUPERATE_FEE = #{recuperateFee,jdbcType=DECIMAL},
			</if>
			<if test="feeCalMode != null">
				FEE_CAL_MODE = #{feeCalMode,jdbcType=CHAR},
			</if>
			<if test="calmDtm != null">
				CALM_DTM = #{calmDtm,jdbcType=TIMESTAMP},
			</if>
			<if test="custRiskLevel != null">
				CUST_RISK_LEVEL = #{custRiskLevel,jdbcType=VARCHAR},
			</if>
			<if test="fundRiskLevel != null">
				FUND_RISK_LEVEL = #{fundRiskLevel,jdbcType=VARCHAR},
			</if>
			<if test="orderFormType != null">
				ORDER_FORM_TYPE = #{orderFormType,jdbcType=CHAR},
			</if>
			<if test="tAckVol != null">
				T_ACK_VOL = #{tAckVol,jdbcType=DECIMAL},
			</if>
			<if test="ackGrams != null">
				ACK_GRAMS = #{ackGrams,jdbcType=DECIMAL},
			</if>
			<if test="submitTaDt != null">
				SUBMIT_TA_DT = #{submitTaDt,jdbcType=VARCHAR},
			</if>
			<if test="appointmentDealNoType != null">
				APPOINTMENT_DEALNO_TYPE = #{appointmentDealNoType,jdbcType=CHAR},
			</if>
			<if test="discountModel != null">
				DISCOUNT_MODEL = #{discountModel,jdbcType=CHAR},
			</if>
			<if test="cpAcctNo != null">
				CP_ACCT_NO = #{cpAcctNo,jdbcType=VARCHAR},
			</if>
			<if test="protocolNo != null">
				PROTOCOL_NO = #{protocolNo,jdbcType=VARCHAR},
			</if>
			<if test="protocolType != null">
				PROTOCOL_TYPE = #{protocolType,jdbcType=VARCHAR},
			</if>
			<if test="bankAcct != null">
				BANK_ACCT = #{bankAcct,jdbcType=VARCHAR},
			</if>
			<if test="bankCode != null">
				BANK_CODE = #{bankCode,jdbcType=VARCHAR},
			</if>
			<if test="dualentryStatus != null">
				DUALENTRY_STATUS = #{dualentryStatus,jdbcType=VARCHAR},
			</if>
			<if test="dualentryFinishDtm != null">
				DUALENTRY_FINISH_DTM = #{dualentryFinishDtm,jdbcType=TIMESTAMP},
			</if>
			<if test="dualentryInterposeFlag != null">
				DUALENTRY_INTERPOSE_FLAG = #{dualentryInterposeFlag,jdbcType=VARCHAR},
			</if>
			<if test="callbackStatus != null">
				CALLBACK_STATUS = #{callbackStatus,jdbcType=VARCHAR},
			</if>
			<if test="callbackFinishDtm != null">
				CALLBACK_FINISH_DTM = #{callbackFinishDtm,jdbcType=TIMESTAMP},
			</if>
			<if test="callbackInterposeFlag != null">
				CALLBACK_INTERPOSE_FLAG = #{callbackInterposeFlag,jdbcType=VARCHAR},
			</if>
			<if test="calmdtmInterposeFlag != null">
				CALMDTM_INTERPOSE_FLAG = #{calmdtmInterposeFlag,jdbcType=VARCHAR},
			</if>
			<if test="assetcertificateStatus != null">
				ASSETCERTIFICATE_STATUS = #{assetcertificateStatus,jdbcType=VARCHAR},
			</if>
			<if test="assetInterposeFlag != null">
				ASSET_INTERPOSE_FLAG = #{assetInterposeFlag,jdbcType=VARCHAR},
			</if>
			<if test="retrieveDtm != null">
				RETRIEVE_DTM = #{retrieveDtm,jdbcType=TIMESTAMP},
			</if>
			<if test="appointmentDealNo != null">
				APPOINTMENT_DEAL_NO = #{appointmentDealNo,jdbcType=VARCHAR},
			</if>
			<if test="qualificationType != null">
				QUALIFICATION_TYPE = #{qualificationType,jdbcType=CHAR},
			</if>
			<if test="refundDt != null">
				REFUND_DT = #{refundDt,jdbcType=VARCHAR},
			</if>
			<if test="isRedeemExpire != null">
				IS_REDEEM_EXPIRE = #{isRedeemExpire,jdbcType=VARCHAR},
			</if>
			<if test="preExpireDate != null">
				PRE_EXPIRE_DATE = #{preExpireDate,jdbcType=VARCHAR},
			</if>
			<if test="submitTaDtInterposeFlag != null">
				SUBMITTADT_INTERPOSE_FLAG = #{submitTaDtInterposeFlag,jdbcType=VARCHAR},
			</if>
			<if test="repurchaseProtocolNo != null">
				REPURCHASE_PROTOCOL_NO = #{repurchaseProtocolNo,jdbcType=VARCHAR},
			</if>
			<if test="joinDt != null">
				JOIN_DT = #{joinDt,jdbcType=VARCHAR},
			</if>
			<if test="forceRedeemFlag != null">
				FORCE_REDEEM_FLAG = #{forceRedeemFlag,jdbcType=VARCHAR},
			</if>
			<if test="preAppVol != null">
				PRE_APP_VOL = #{preAppVol,jdbcType=DECIMAL},
			</if>
			<if test="forceRedeemMemo != null">
				FORCE_REDEEM_MEMO = #{forceRedeemMemo,jdbcType=VARCHAR},
			</if>
			<if test="cxgDealNo != null">
				CXG_DEAL_NO = #{cxgDealNo,jdbcType=VARCHAR},
			</if>
			<if test="netAppAmt != null">
				NET_APP_AMT = #{netAppAmt,jdbcType=DECIMAL},
			</if>
			<if test="appointId != null">
				APPOINT_ID = #{appointId,jdbcType=VARCHAR},
			</if>
			<if test="subsAmt != null">
				SUBS_AMT = #{subsAmt,jdbcType=DECIMAL},
			</if>
			<if test="mergeSubmitFlag != null">
				MERGE_SUBMIT_FLAG = #{mergeSubmitFlag,jdbcType=VARCHAR},
			</if>
			<if test="mainDealOrderNo != null">
				MAIN_DEAL_ORDER_NO = #{mainDealOrderNo,jdbcType=VARCHAR},
			</if>
			<if test="contractVersion != null">
				CONTRACT_VERSION = #{contractVersion,jdbcType=VARCHAR},
			</if>
			<if test="highFundInvPlanFlag != null">
				HIGH_FUND_INV_PLAN_FLAG = #{highFundInvPlanFlag,jdbcType=VARCHAR},
			</if>
			<if test="agencyFee != null">
				AGENCY_FEE = #{agencyFee,jdbcType=DECIMAL},
			</if>
			<if test="otherFee1 != null">
				OTHER_FEE1 = #{otherFee1,jdbcType=DECIMAL},
			</if>
			<if test="transferFee != null">
				TRANSFER_FEE = #{transferFee,jdbcType=DECIMAL},
			</if>
			<if test="achievPay != null">
				ACHIEV_PAY = #{achievPay,jdbcType=DECIMAL},
			</if>
			<if test="totalTransFee != null">
				TOTAL_TRANS_FEE = #{totalTransFee,jdbcType=DECIMAL},
			</if>
			<if test="adjustFlag != null">
				ADJUST_FLAG = #{adjustFlag,jdbcType=VARCHAR},
			</if>
			<if test="transDirect != null">
				TRANS_DIRECT = #{transDirect,jdbcType=VARCHAR},
			</if>
			<if test="transferReason != null">
				TRANSFER_REASON = #{transferReason,jdbcType=VARCHAR},
			</if>
			<if test="divDt != null">
				DIV_DT = #{divDt,jdbcType=VARCHAR},
			</if>
			<if test="originSerialNo != null">
				ORIGIN_SERIAL_NO = #{originSerialNo,jdbcType=VARCHAR},
			</if>
			<if test="payOutStatus != null">
				PAY_OUT_STATUS = #{payOutStatus,jdbcType=VARCHAR},
			</if>
			<if test="payOutDt != null">
				PAY_OUT_DT = #{payOutDt,jdbcType=VARCHAR},
			</if>
			<if test="continuanceFlag != null">
				CONTINUANCE_FLAG = #{continuanceFlag,jdbcType=VARCHAR},
			</if>
			<if test="stageFlag != null">
				STAGE_FLAG = #{stageFlag,jdbcType=VARCHAR},
			</if>
			<if test="transferPrice != null">
				TRANSFER_PRICE = #{transferPrice,jdbcType=DECIMAL},
			</if>
			<if test="isNoTradeTransfer != null">
				IS_NO_TRADE_TRANSFER = #{isNoTradeTransfer,jdbcType=VARCHAR},
			</if>
			<if test="taAckNo != null">
				TA_ACK_NO = #{taAckNo,jdbcType=VARCHAR},
			</if>
		</set>
		where DEAL_DTL_NO = #{dealDtlNo,jdbcType=VARCHAR}
	</update>

  <select id="selectByDealDtlNo" resultMap="BaseResultMap">
	  select
	  <include refid="com.howbuy.tms.high.orders.dao.mapper.HighDealOrderDtlPoAutoMapper.Base_Column_List" />
	  from HIGH_DEAL_ORDER_DTL
	  where deal_dtl_no = #{dealDtlNo,jdbcType=VARCHAR}
    </select>


	<select id="selectBySubmitDtOrAckDt" resultMap="BaseResultMap" parameterType="map">
		select t.deal_no,t.fund_code,t.m_busi_code,t.submit_ta_dt,t.ack_dt
		from high_deal_order_dtl t
		where t.m_busi_code in ('1124','1120','1122','1130')
		  and tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
		  and tx_ack_flag in ('3','4')
		<if test="fundCodeList != null and fundCodeList.size() > 0 ">
			and fund_code in
			<foreach collection="fundCodeList" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="endDt != null and endDt != '' ">
			and (IF(submit_ta_dt is null or length(submit_ta_dt) = 0, t.ack_dt <![CDATA[<=]]> #{endDt}, t.submit_ta_dt <![CDATA[<=]]> #{endDt}))
		</if>
		<if test=" startDt != null and startDt != '' ">
			and (IF(submit_ta_dt is null or length(submit_ta_dt) = 0, t.ack_dt >= #{startDt}, t.submit_ta_dt >=  #{startDt}))
		</if>
		and (main_deal_order_no is null or main_deal_order_no=deal_no)
		union all
		select c.appserialno as deal_no,if(c.mjjdm is not null,c.mjjdm,c.fundcode) as fund_code,CONCAT('1',c.busicode) as m_busi_code,c.NEW_TRADE_DT as submit_ta_dt,c.tradedt as ack_dt from cm_custtrade_direct c
		where c.busicode in ('124','120','122','130','12B')
		and hboneno= #{hbOneNo,jdbcType=VARCHAR}
		and recstat='0'
		<if test="fundCodeList != null and fundCodeList.size() > 0 ">
			and (
			fundcode in
			<foreach collection="fundCodeList" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
			or
			mjjdm in
			<foreach collection="fundCodeList" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
			)
		</if>
		and orderstate in('2','3')
		<if test="endDt != null and endDt != '' ">
			and (IF(NEW_TRADE_DT is null or length(NEW_TRADE_DT) = 0,tradedt <![CDATA[<=]]> #{endDt}, NEW_TRADE_DT <![CDATA[<=]]> #{endDt}))
		</if>
		<if test=" startDt != null and startDt != '' ">
			and (IF(NEW_TRADE_DT is null or length(NEW_TRADE_DT) = 0, tradedt >= #{startDt}, NEW_TRADE_DT >=  #{startDt}))
		</if>
	</select>

    <!-- 查询基金确认金额与确认份额 -->
    <select id="selectFundAckVolAndAmtInfo" parameterType="map" resultType="com.howbuy.tms.high.orders.dao.vo.FundAckVolAndAmtVo">
        SELECT 
            t.FUND_CODE as fundCode,
            t.M_BUSI_CODE as mBusiCode,
            SUM(t.ACK_AMT) as ackAmt,
            SUM(t.ACK_VOL) as ackVol
        FROM HIGH_DEAL_ORDER_DTL t
        WHERE 1=1
        <if test="fundCodeList != null and fundCodeList.size() > 0">
            AND t.FUND_CODE IN
            <foreach collection="fundCodeList" item="fundCode" open="(" close=")" separator=",">
                #{fundCode,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="ackStartDt != null and ackStartDt != ''">
            AND t.ACK_DT  <![CDATA[>=]]> #{ackStartDt,jdbcType=VARCHAR}
        </if>
        <if test="ackEndDt != null and ackEndDt != ''">
            AND t.ACK_DT  <![CDATA[<=]]> #{ackEndDt,jdbcType=VARCHAR}
        </if>
        <if test="txAckFlagList != null and txAckFlagList.size() > 0">
            AND t.TX_ACK_FLAG IN
            <foreach collection="txAckFlagList" item="txAckFlag" open="(" close=")" separator=",">
                #{txAckFlag,jdbcType=VARCHAR}
            </foreach>
        </if>
        GROUP BY t.FUND_CODE, t.M_BUSI_CODE
        ORDER BY t.FUND_CODE, t.M_BUSI_CODE
    </select>
</mapper>
