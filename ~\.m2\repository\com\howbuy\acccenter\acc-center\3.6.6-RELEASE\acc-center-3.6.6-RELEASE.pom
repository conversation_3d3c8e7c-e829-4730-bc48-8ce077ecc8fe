<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.howbuy.acccenter</groupId>
	<artifactId>acc-center</artifactId>
	<version>3.6.6-RELEASE</version>
	<packaging>pom</packaging>
	<name>acc-center</name>

	
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.3.12.RELEASE</version>
		<relativePath />
	</parent>

	<properties>
		
		<java.version>1.8</java.version>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		
		<spring.version>5.2.15.RELEASE</spring.version>
		<nacos-spring-context.version>1.1.0</nacos-spring-context.version>
		<nacos-config-spring-boot-starter.version>0.2.10</nacos-config-spring-boot-starter.version>
		<dubbo.version>2.7.15</dubbo.version>
		<sharding-sphere.version>5.1.1</sharding-sphere.version>
		<aspectj.version>1.8.13</aspectj.version>
		<druid.version>0.2.9</druid.version>
		<cglib.version>2.2.0</cglib.version>
		<mysql.version>5.1.49</mysql.version>
		<mybatis.version>3.5.5</mybatis.version>
		<org.mybatis.version>2.0.5</org.mybatis.version>
		<mybatis.plus.version>3.4.1</mybatis.plus.version>
		<pagehelper.spring.version>1.2.3</pagehelper.spring.version>
		<fastjson.version>1.1.41</fastjson.version>
		<cache.version>3.0.1-RELEASE</cache.version>
		<zkclient.version>0.1</zkclient.version>
		<hessian.version>4.0.7</hessian.version>
		<ch.qos.logback.version>1.2.3</ch.qos.logback.version>
		<commons.pool.version>1.6</commons.pool.version>
		<commons.beanutils.version>1.9.2</commons.beanutils.version>
		<junit.version>4.12</junit.version>
		<org.springframework.security.version>3.2.6.RELEASE</org.springframework.security.version>
		<commons-fileupload.version>1.2</commons-fileupload.version>
		<guava.version>30.0-jre</guava.version>
		<lombok.version>1.18.20</lombok.version>
		<org.mapstruct.version>1.4.1.Final</org.mapstruct.version>
		<dom4j.version>1.6.1</dom4j.version>
		<joda-time.version>2.3</joda-time.version>
		<message.client.version>2.3.2-RELEASE</message.client.version>
		<ccms.watcher.version>6.0.1-RELEASE</ccms.watcher.version>
		<powermock.version>2.0.2</powermock.version>
		<com.howbuy.acc-center.version>3.6.6-RELEASE</com.howbuy.acc-center.version>
		<com.howbuy.acc-common.version>3.5.9-RELEASE</com.howbuy.acc-common.version>
		<com.howbuy.howbuy-auth-facade.version>2.2.0-RELEASE</com.howbuy.howbuy-auth-facade.version>
		<org.javassist.version>3.20.0-GA</org.javassist.version>
		<com.howbuy.common-service.version>3.5.7-RELEASE</com.howbuy.common-service.version>
		<com.howbuy.common-facade.version>3.5.7-RELEASE</com.howbuy.common-facade.version>
		<com.howbuy.ftx-online-search-facade.version>1.1.32-RELEASE</com.howbuy.ftx-online-search-facade.version>
		<com.howbuy.order-center-search-client.version>3.9.77-RELEASE</com.howbuy.order-center-search-client.version>
		<com.howbuy.tms-common-client.version>4.8.59-RELEASE</com.howbuy.tms-common-client.version>
		<com.howbuy.fds-dis-console-facade.version>20220930-RELEASE</com.howbuy.fds-dis-console-facade.version>
		<com.howbuy.fin-online-facade.version>20250724-RELEASE</com.howbuy.fin-online-facade.version>
		<com.howbuy.crm-nt-client.version>1.9.6.5-RELEASE</com.howbuy.crm-nt-client.version>
		<com.howbuy.message-public-client.version>5.1.16-RELEASE</com.howbuy.message-public-client.version>
		<com.howbuy.pay-online-facade.version>20250729-RELEASE</com.howbuy.pay-online-facade.version>
		<com.howbuy.fbs-online-facade.version>3.40.5-RELEASE</com.howbuy.fbs-online-facade.version>
		<com.howbuy.fbs-online-search-facade.version>3.40.5-RELEASE</com.howbuy.fbs-online-search-facade.version>
		
		<com.howbuy.fbs-common-facade.version>3.17.0-RELEASE</com.howbuy.fbs-common-facade.version>
		<com.howbuy.param-server-facade.version>3.40.5-RELEASE</com.howbuy.param-server-facade.version>
		<com.howbuy.howbuy-boot-actuator.version>1.1.6-RELEASE</com.howbuy.howbuy-boot-actuator.version>
		<com.howbuy.howbuy-sharding-id.version>2.0.4-RELEASE</com.howbuy.howbuy-sharding-id.version>
		<xhtmlrenderer.version>1.0.0-howbuy</xhtmlrenderer.version>
		<lowagie.itext.version>2.0.8</lowagie.itext.version>
		<org.apache.pdfbox.version>3.0.0</org.apache.pdfbox.version>
		<com.howbuy.es-web-facade.version>3.5.0-RELEASE</com.howbuy.es-web-facade.version>
		<com.howbuy.howbuy-interlayer-product-client.version>3.5.48-RELEASE</com.howbuy.howbuy-interlayer-product-client.version>
		<com.belerweb.pinyin4j.version>2.5.0</com.belerweb.pinyin4j.version>
		<com.howbuy.pension-order-client.version>4.8.57-RELEASE</com.howbuy.pension-order-client.version>
		<com.howbuy.hk-acc-online-facade.version>20250625-RELEASE</com.howbuy.hk-acc-online-facade.version>
		<com.howbuy.crm-td-client.version>1.9.6.5-RELEASE</com.howbuy.crm-td-client.version>
		<com.howbuy.center-client.version>6.4.10-RELEASE</com.howbuy.center-client.version>
		<com.howbuy.high-order-center-client.version>4.8.88-RELEASE</com.howbuy.high-order-center-client.version>
		<com.howbuy.order-center-client.version>4.8.59-RELEASE</com.howbuy.order-center-client.version>
		
		<ehcache.version>1.3.0</ehcache.version>
		<jedis.version>2.9.3</jedis.version>
		<commons-pool2.version>2.4.3</commons-pool2.version>
		<commons-lang.version>2.6</commons-lang.version>
		
		<com.howbuy.howbuy-dfile.version>1.18.1-RELEASE</com.howbuy.howbuy-dfile.version>
<com.howbuy.acc-common-cache.version>3.5.9-RELEASE</com.howbuy.acc-common-cache.version>
<com.howbuy.acc-common-utils.version>3.5.9-RELEASE</com.howbuy.acc-common-utils.version>
<com.howbuy.acc-common-file.version>3.5.9-RELEASE</com.howbuy.acc-common-file.version>
</properties>

	<dependencyManagement>
		<dependencies>

			<dependency>
				<groupId>com.alibaba.boot</groupId>
				<artifactId>nacos-config-spring-boot-starter</artifactId>
				<version>${nacos-config-spring-boot-starter.version}</version>
			</dependency>

			<dependency>
				<groupId>org.apache.shardingsphere</groupId>
				<artifactId>shardingsphere-jdbc-core-spring-boot-starter</artifactId>
				<version>${sharding-sphere.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>shardingsphere-cluster-mode-repository-zookeeper-curator</artifactId>
						<groupId>org.apache.shardingsphere</groupId>
					</exclusion>
					
					<exclusion>
						<artifactId>shardingsphere-encrypt-spring-boot-starter</artifactId>
						<groupId>org.apache.shardingsphere</groupId>
					</exclusion>
					<exclusion>
						<artifactId>bcprov-jdk15on</artifactId>
						<groupId>org.bouncycastle</groupId>
					</exclusion>
				</exclusions>
			</dependency>

			<dependency>
				<groupId>org.apache.shardingsphere</groupId>
				<artifactId>shardingsphere-transaction-core</artifactId>
				<version>${sharding-sphere.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.dubbo</groupId>
				<artifactId>dubbo-remoting-zookeeper</artifactId>
				<version>${dubbo.version}</version>
				<exclusions>
					<exclusion>
						<groupId>org.slf4j</groupId>
						<artifactId>slf4j-log4j12</artifactId>
					</exclusion>
					<exclusion>
						<groupId>log4j</groupId>
						<artifactId>log4j</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.apache.dubbo</groupId>
						<artifactId>dubbo-remoting-api</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.apache.dubbo</groupId>
						<artifactId>dubbo-common</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>org.apache.dubbo</groupId>
				<artifactId>dubbo</artifactId>
				<version>${dubbo.version}</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>param-server-facade</artifactId>
				<version>${com.howbuy.param-server-facade.version}</version>
			</dependency>

			<dependency>
				<groupId>junit</groupId>
				<artifactId>junit</artifactId>
				<version>${junit.version}</version>
				<scope>test</scope>
			</dependency>

			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-test</artifactId>
				<version>${spring.version}</version>
			</dependency>

			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-context</artifactId>
				<version>${spring.version}</version>
			</dependency>

			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-context-support</artifactId>
				<version>${spring.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-tx</artifactId>
				<version>${spring.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-jdbc</artifactId>
				<version>${spring.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-jms</artifactId>
				<version>${spring.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-aspects</artifactId>
				<version>${spring.version}</version>
			</dependency>
			<dependency>
				<groupId>net.sourceforge.cglib</groupId>
				<artifactId>com.springsource.net.sf.cglib</artifactId>
				<version>${cglib.version}</version>
			</dependency>
			<dependency>
				<groupId>org.aspectj</groupId>
				<artifactId>aspectjweaver</artifactId>
				<version>${aspectj.version}</version>
			</dependency>
			<dependency>
				<groupId>org.aspectj</groupId>
				<artifactId>aspectjrt</artifactId>
				<version>${aspectj.version}</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy.acccenter</groupId>
				<artifactId>acc-center-common</artifactId>
				<version>${com.howbuy.acc-center.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.acccenter</groupId>
				<artifactId>acc-center-facade</artifactId>
				<version>${com.howbuy.acc-center.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.acccenter</groupId>
				<artifactId>acc-center-service</artifactId>
				<version>${com.howbuy.acc-center.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.acccenter</groupId>
				<artifactId>acc-center-dao</artifactId>
				<version>${com.howbuy.acc-center.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.acccenter</groupId>
				<artifactId>acc-center-alg</artifactId>
				<version>${com.howbuy.acc-center.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.common</groupId>
				<artifactId>common-service</artifactId>
				<version>${com.howbuy.common-service.version}</version>
				<exclusions>
					<exclusion>
						<groupId>org.springframework</groupId>
						<artifactId>spring-aspects</artifactId>
					</exclusion>
					<exclusion>
						<groupId>com.howbuy.pa.cache</groupId>
						<artifactId>howbuy-cache-client-trade</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.slf4j</groupId>
						<artifactId>slf4j-simple</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.howbuy.common</groupId>
				<artifactId>common-facade</artifactId>
				<version>${com.howbuy.common-facade.version}</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy.acc</groupId>
				<artifactId>acc-common-cache</artifactId>
				<version>${com.howbuy.acc-common-cache.version}</version>
				<exclusions>
					<exclusion>
						<groupId>log4j</groupId>
						<artifactId>log4j</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.slf4j</groupId>
						<artifactId>slf4j-log4j12</artifactId>
					</exclusion>
				</exclusions>
			</dependency>

			<dependency>
				<groupId>com.howbuy.acc</groupId>
				<artifactId>acc-common-utils</artifactId>
				<version>${com.howbuy.acc-common-utils.version}</version>
				<exclusions>
					<exclusion>
						<groupId>org.springframework</groupId>
						<artifactId>spring-beans</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.springframework</groupId>
						<artifactId>spring-web</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.howbuy.acc</groupId>
				<artifactId>acc-common-file</artifactId>
				<version>${com.howbuy.acc-common-file.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-auth-facade</artifactId>
				<version>${com.howbuy.howbuy-auth-facade.version}</version>
			</dependency>

			<dependency>
				<groupId>com.alibaba.druid</groupId>
				<artifactId>druid-wrapper</artifactId>
				<version>${druid.version}</version>
			</dependency>

			<dependency>
				<groupId>mysql</groupId>
				<artifactId>mysql-connector-java</artifactId>
				<version>${mysql.version}</version>
			</dependency>
			<dependency>
				<groupId>org.mybatis</groupId>
				<artifactId>mybatis</artifactId>
				<version>${mybatis.version}</version>
			</dependency>
			<dependency>
				<groupId>org.mybatis</groupId>
				<artifactId>mybatis-spring</artifactId>
				<version>${org.mybatis.version}</version>
			</dependency>
			<dependency>
				<groupId>com.baomidou</groupId>
				<artifactId>mybatis-plus-boot-starter</artifactId>
				<version>${mybatis.plus.version}</version>
			</dependency>
			<dependency>
				<groupId>com.github.pagehelper</groupId>
				<artifactId>pagehelper-spring-boot-starter</artifactId>
				<version>${pagehelper.spring.version}</version>
				<exclusions>
					<exclusion>
						<groupId>org.mybatis</groupId>
						<artifactId>mybatis</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.mybatis</groupId>
						<artifactId>mybatis-spring</artifactId>
					</exclusion>
				</exclusions>
			</dependency>

			<dependency>
				<groupId>com.caucho</groupId>
				<artifactId>hessian</artifactId>
				<version>${hessian.version}</version>
			</dependency>

			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>fastjson</artifactId>
				<version>${fastjson.version}</version>
			</dependency>

			<dependency>
				<groupId>ch.qos.logback</groupId>
				<artifactId>logback-classic</artifactId>
				<version>${ch.qos.logback.version}</version>
			</dependency>
			<dependency>
				<groupId>org.slf4j</groupId>
				<artifactId>log4j-over-slf4j</artifactId>
				<version>1.7.22</version>
			  </dependency>
			<dependency>
				<groupId>commons-pool</groupId>
				<artifactId>commons-pool</artifactId>
				<version>${commons.pool.version}</version>
			</dependency>
			<dependency>
				<groupId>commons-beanutils</groupId>
				<artifactId>commons-beanutils</artifactId>
				<version>${commons.beanutils.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.security</groupId>
				<artifactId>spring-security-core</artifactId>
				<version>${org.springframework.security.version}</version>
				<exclusions>
					<exclusion>
						<groupId>org.springframework</groupId>
						<artifactId>spring-expression</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>commons-fileupload</groupId>
				<artifactId>commons-fileupload</artifactId>
				<version>${commons-fileupload.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.guava</groupId>
				<artifactId>guava</artifactId>
				<version>${guava.version}</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>ftx-online-search-facade</artifactId>
				<version>${com.howbuy.ftx-online-search-facade.version}</version>
			</dependency>

			<dependency>
				<groupId>org.projectlombok</groupId>
				<artifactId>lombok</artifactId>
				<version>${lombok.version}</version>
				<scope>provided</scope>
			</dependency>

			<dependency>
				<groupId>com.howbuy.payonline</groupId>
				<artifactId>pay-online-facade</artifactId>
				<version>${com.howbuy.pay-online-facade.version}</version>
				<exclusions>
					<exclusion>
						<groupId>com.alibaba</groupId>
						<artifactId>dubbo</artifactId>
					</exclusion>
				</exclusions>
			</dependency>

			<dependency>
				<groupId>dom4j</groupId>
				<artifactId>dom4j</artifactId>
				<version>${dom4j.version}</version>
			</dependency>

			<dependency>
				<groupId>joda-time</groupId>
				<artifactId>joda-time</artifactId>
				<version>${joda-time.version}</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>order-center-search-client</artifactId>
				<version>${com.howbuy.order-center-search-client.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>tms-common-client</artifactId>
				<version>${com.howbuy.tms-common-client.version}</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-message-service</artifactId>
				<version>${message.client.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>slf4j-log4j12</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
				</exclusions>
			</dependency>

			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-message-rocket</artifactId>
				<version>${message.client.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>slf4j-log4j12</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
				</exclusions>
			</dependency>

			
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-ccms-watcher</artifactId>
				<version>${ccms.watcher.version}</version>
			</dependency>

			<dependency>
				<groupId>commons-io</groupId>
				<artifactId>commons-io</artifactId>
				<version>1.3.2</version>
			</dependency>

			
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>utils</artifactId>
				<version>1.0.0-SNAPSHOT</version>
				<exclusions>
					<exclusion>
						<groupId>*</groupId>
						<artifactId>*</artifactId>
					</exclusion>
				</exclusions>
			</dependency>

			
			<dependency>
				<groupId>com.howbuy.finonline</groupId>
				<artifactId>fin-online-facade</artifactId>
				<version>${com.howbuy.fin-online-facade.version}</version>
				<exclusions>
					<exclusion>
						<groupId>*</groupId>
						<artifactId>*</artifactId>
					</exclusion>
				</exclusions>
			</dependency>

			<dependency>
				<groupId>com.howbuy.fbs</groupId>
				<artifactId>fbs-online-facade</artifactId>
				<version>${com.howbuy.fbs-online-facade.version}</version>
				<exclusions>
					<exclusion>
						<groupId>com.howbuy.common</groupId>
						<artifactId>common-facade</artifactId>
					</exclusion>
					<exclusion>
						<groupId>commons-lang</groupId>
						<artifactId>commons-lang</artifactId>
					</exclusion>
					<exclusion>
						<groupId>commons-collections</groupId>
						<artifactId>commons-collections</artifactId>
					</exclusion>
					<exclusion>
						<groupId>com.howbuy.pa.framework</groupId>
						<artifactId>howbuy-framework-core</artifactId>
					</exclusion>
				</exclusions>
			</dependency>

			<dependency>
				<groupId>com.howbuy.fbs</groupId>
				<artifactId>fbs-online-search-facade</artifactId>
				<version>${com.howbuy.fbs-online-search-facade.version}</version>
				<exclusions>
					<exclusion>
						<groupId>*</groupId>
						<artifactId>*</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.howbuy.fbs</groupId>
				<artifactId>fbs-common-facade</artifactId>
				<version>${com.howbuy.fbs-common-facade.version}</version>
				<exclusions>
					<exclusion>
						<groupId>org.apache.logging.log4j</groupId>
						<artifactId>log4j-slf4j-impl</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.apache.logging.log4j</groupId>
						<artifactId>log4j-core</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.apache.logging.log4j</groupId>
						<artifactId>log4j-api</artifactId>
					</exclusion>
					<exclusion>
						<artifactId>commons-collections</artifactId>
						<groupId>commons-collections</groupId>
					</exclusion>
					<exclusion>
						<artifactId>cglib</artifactId>
						<groupId>cglib</groupId>
					</exclusion>
				</exclusions>
			</dependency>

			<dependency>
				<groupId>com.howbuy.crm</groupId>
				<artifactId>crm-nt-client</artifactId>
				<version>${com.howbuy.crm-nt-client.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>commons-lang3</artifactId>
						<groupId>org.apache.commons</groupId>
					</exclusion>
					<exclusion>
						<artifactId>httpclient</artifactId>
						<groupId>org.apache.httpcomponents</groupId>
					</exclusion>
					<exclusion>
						<artifactId>joda-time</artifactId>
						<groupId>joda-time</groupId>
					</exclusion>
					<exclusion>
						<artifactId>*</artifactId>
						<groupId>org.apache.logging.log4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>commons-codec</artifactId>
						<groupId>commons-codec</groupId>
					</exclusion>
					<exclusion>
						<artifactId>*</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>disruptor</artifactId>
						<groupId>com.lmax</groupId>
					</exclusion>
					<exclusion>
						<artifactId>lombok</artifactId>
						<groupId>org.projectlombok</groupId>
					</exclusion>
					<exclusion>
						<groupId>cn.hutool</groupId>
						<artifactId>hutool-all</artifactId>
					</exclusion>
					<exclusion>
						<groupId>javax.mail</groupId>
						<artifactId>mail</artifactId>
					</exclusion>
				</exclusions>
			</dependency>

            <dependency>
                <groupId>com.howbuy.cc.message</groupId>
                <artifactId>message-public-client</artifactId>
                <version>${com.howbuy.message-public-client.version}</version>
            </dependency>

			<dependency>
				<groupId>org.mapstruct</groupId>
				<artifactId>mapstruct</artifactId>
				<version>${org.mapstruct.version}</version>
			</dependency>

			<dependency>
				<groupId>org.javassist</groupId>
				<artifactId>javassist</artifactId>
				<version>${org.javassist.version}</version>
			</dependency>

			<dependency>
				<groupId>com.github.sgroschupf</groupId>
				<artifactId>zkclient</artifactId>
				<version>${zkclient.version}</version>
				<exclusions>
					<exclusion>
						<groupId>log4j</groupId>
						<artifactId>log4j</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.apache.zookeeper</groupId>
						<artifactId>zookeeper</artifactId>
					</exclusion>
				</exclusions>
			</dependency>

			<dependency>
				<groupId>com.alibaba.nacos</groupId>
				<artifactId>nacos-spring-context</artifactId>
				<version>${nacos-spring-context.version}</version>
				<exclusions>
					<exclusion>
						<groupId>com.fasterxml.jackson.core</groupId>
						<artifactId>jackson-databind</artifactId>
					</exclusion>
					<exclusion>
						<groupId>com.fasterxml.jackson.core</groupId>
						<artifactId>jackson-core</artifactId>
					</exclusion>
					<exclusion>
						<artifactId>slf4j-api</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>commons-logging</artifactId>
						<groupId>commons-logging</groupId>
					</exclusion>
					<exclusion>
						<groupId>commons-io</groupId>
						<artifactId>commons-io</artifactId>
					</exclusion>
					<exclusion>
						<artifactId>commons-codec</artifactId>
						<groupId>commons-codec</groupId>
					</exclusion>
				</exclusions>
			</dependency>

			<dependency>
				<groupId>com.howbuy.boot</groupId>
				<artifactId>howbuy-boot-actuator</artifactId>
				<version>${com.howbuy.howbuy-boot-actuator.version}</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy.sharding</groupId>
				<artifactId>howbuy-sharding-id</artifactId>
				<version>${com.howbuy.howbuy-sharding-id.version}</version>
			</dependency>

			<dependency>
				<groupId>org.xhtmlrenderer</groupId>
				<artifactId>core-renderer</artifactId>
				<version>${xhtmlrenderer.version}</version>
			</dependency>

			<dependency>
				<groupId>com.lowagie</groupId>
				<artifactId>itext</artifactId>
				<version>${lowagie.itext.version}</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy.es</groupId>
				<artifactId>es-web-facade</artifactId>
				<version>${com.howbuy.es-web-facade.version}</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy.interlayer</groupId>
				<artifactId>howbuy-interlayer-product-client</artifactId>
				<version>${com.howbuy.howbuy-interlayer-product-client.version}</version>
			</dependency>

			<dependency>
				<groupId>com.belerweb</groupId>
				<artifactId>pinyin4j</artifactId>
				<version>${com.belerweb.pinyin4j.version}</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>pension-order-client</artifactId>
				<version>${com.howbuy.pension-order-client.version}</version>
				<exclusions>
					<exclusion>
						<groupId>org.hibernate.validator</groupId>
						<artifactId>hibernate-validator</artifactId>
					</exclusion>
				</exclusions>
			</dependency>

			<dependency>
				<groupId>com.howbuy.hkacconline</groupId>
				<artifactId>hk-acc-online-facade</artifactId>
				<version>${com.howbuy.hk-acc-online-facade.version}</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy.crm</groupId>
				<artifactId>crm-td-client</artifactId>
				<version>${com.howbuy.crm-td-client.version}</version>
			</dependency>

			
			<dependency>
				<groupId>org.mockito</groupId>
				<artifactId>mockito-core</artifactId>
				<version>3.3.3</version>
				<scope>test</scope>
			</dependency>
			<dependency>
				<groupId>org.powermock</groupId>
				<artifactId>powermock-module-testng</artifactId>
				<version>${powermock.version}</version>
				<scope>test</scope>
			</dependency>
			<dependency>
				<groupId>org.powermock</groupId>
				<artifactId>powermock-api-mockito2</artifactId>
				<version>${powermock.version}</version>
				<scope>test</scope>
			</dependency>

			
			<dependency>
				<groupId>net.sf.ehcache</groupId>
				<artifactId>ehcache</artifactId>
				<version>${ehcache.version}</version>
			</dependency>
			<dependency>
				<groupId>redis.clients</groupId>
				<artifactId>jedis</artifactId>
				<version>${jedis.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.commons</groupId>
				<artifactId>commons-pool2</artifactId>
				<version>${commons-pool2.version}</version>
			</dependency>

			<dependency>
				<groupId>commons-lang</groupId>
				<artifactId>commons-lang</artifactId>
				<version>${commons-lang.version}</version>
			</dependency>
			
			<dependency>
				<groupId>com.howbuy.cc</groupId>
				<artifactId>center-client</artifactId>
				<version>${com.howbuy.center-client.version}</version>
				<exclusions>
					<exclusion>
						<groupId>*</groupId>
						<artifactId>*</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			
			<dependency>
				<groupId>org.apache.pdfbox</groupId>
				<artifactId>pdfbox</artifactId>
				<version>${org.apache.pdfbox.version}</version>
			</dependency>
			
			<dependency>
				<groupId>com.howbuy.dfile</groupId>
				<artifactId>howbuy-dfile-service</artifactId>
				<version>${com.howbuy.howbuy-dfile.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.dfile</groupId>
				<artifactId>howbuy-dfile-impl-local</artifactId>
				<version>${com.howbuy.howbuy-dfile.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.dfile</groupId>
				<artifactId>howbuy-dfile-impl-webdav</artifactId>
				<version>${com.howbuy.howbuy-dfile.version}</version>
			</dependency>
			
			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>high-order-center-client</artifactId>
				<version>${com.howbuy.high-order-center-client.version}</version>
			</dependency>
			
			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>order-center-client</artifactId>
				<version>${com.howbuy.order-center-client.version}</version>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<distributionManagement>
		<repository>
			<id>howbuy-release</id>
			<name>howbuy-release</name>
			<url>http://nx-java.howbuy.pa/repository/howbuy-release/</url>
		</repository>
		<snapshotRepository>
			<id>howbuy-snapshot</id>
			<name>howbuy-snapshot</name>
			<url>http://nx-java.howbuy.pa/repository/howbuy-snapshot/</url>
		</snapshotRepository>
	</distributionManagement>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.8.1</version>
				<configuration>
					<source>${java.version}</source>
					<target>${java.version}</target>
					<encoding>UTF-8</encoding>
					<annotationProcessorPaths>
						<path>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
							<version>${lombok.version}</version>
						</path>
						<path>
							<groupId>org.mapstruct</groupId>
							<artifactId>mapstruct-processor</artifactId>
							<version>${org.mapstruct.version}</version>
						</path>
					</annotationProcessorPaths>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-source-plugin</artifactId>
				<version>2.4</version>
				<executions>
					<execution>
						<id>attach-source</id>
						<phase>install</phase>
						<goals>
							<goal>jar-no-fork</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
	</project>