package com.howbuy.tms.high.orders.dao.mapper.customize;

import com.howbuy.tms.high.orders.dao.mapper.CustBooksPoAutoMapper;
import com.howbuy.tms.high.orders.dao.vo.ConfirmBalanceVo;
import com.howbuy.tms.high.orders.dao.po.CustBooksPo;
import com.howbuy.tms.high.orders.dao.vo.BalanceVo;
import com.howbuy.tms.high.orders.dao.vo.FundAcctVo;
import com.howbuy.tms.high.orders.dao.vo.QueryAcctBalanceBaseInfoParamVo;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * 去O
 */
public interface CustBooksPoMapper extends CustBooksPoAutoMapper {

    /**
     * selectCustAllBooksByTxAcctNoAndFundCode:(查询客户基金在中台的所有份额)
     *
     * @param txAcctNo
     * @param productCode
     * @return
     * <AUTHOR>
     * @date 2017年3月23日 下午3:42:02
     */
    CustBooksPo selectCustAllBooksByTxAcctNoAndFundCode(@Param("txAcctNo") String txAcctNo, @Param("productCode") String productCode, @Param("disCodeList") List<String> disCodeList);

    /**
     * selectStockBooksByTxAcctNoAndFundCode:(查询客户基金在中台的存量份额)
     *
     * @param txAcctNo
     * @param productCode
     * @return
     * <AUTHOR>
     * @date 2017年3月23日 下午3:42:02
     */
    List<CustBooksPo> selectStockBooksByTxAcctNoAndFundCode(@Param("txAcctNo") String txAcctNo, @Param("productCode") String productCode);


    List<String> selectStockBooksByTxAcctNoAndFundCodeBatch(@Param("txAcctNo") String txAcctNo, @Param("productCodes") List<String> productCodes);


    /**
     * queryThreeElementsByTxAcctNoAndFundCode:(查询客户基金的资金账号、协议号、分销代码)
     *
     * @param txAcctNo
     * @param productCode
     * @return
     * <AUTHOR>
     * @date 2017年3月23日 下午3:42:02
     */
    List<CustBooksPo> selectThreeElementsByTxAcctNoAndFundCode(@Param("txAcctNo") String txAcctNo, @Param("productCode") String productCode);

    /**
     * queryCustBooksForModifyDiv:查询某只基金在指定协议下是否有持仓
     *
     * @param txAcctNo
     * @param protocolNo
     * @param fundCode
     * @return
     * <AUTHOR>
     * @date 2017年1月23日 下午4:13:09
     */
    CustBooksPo selectCustBooksForModifyDiv(@Param("txAcctNo") String txAcctNo, @Param("protocolNo") String protocolNo, @Param("fundCode") String fundCode);

    /**
     * 根据protocolNo查询客户账本
     *
     * @param protocolNo
     * @param disCode
     * @param txAcctNo
     * @return
     */
    List<CustBooksPo> selectCustBooksByProtocolNo(@Param("txAcctNo") String txAcctNo, @Param("protocolNo") String protocolNo, @Param("disCode") String disCode);

    /**
     * selectInTransit:查询在途交易
     *
     * @param custBooksPo
     * @return
     * <AUTHOR>
     * @date 2016-10-21 下午7:10:06
     */
    int selectInTransit(CustBooksPo custBooksPo);

    /**
     * selectBalanceList:(查询客户代销产品持仓列表: 持仓大于0)
     *
     * @param txAcctNo
     * @param disCodeList
     * @param protocolType
     * @return
     * <AUTHOR>
     * @date 2017年7月12日 下午6:51:16
     */
    List<CustBooksPo> selectBalanceList(@Param("txAcctNo") String txAcctNo,
                                        @Param("disCodeList") List<String> disCodeList,
                                        @Param("protocolType") String protocolType,
                                        @Param("productCode") String productCode);

    /**
     * selectBalanceWithLockPeriod:查询客户产品持仓信息(带锁定期资产)
     *
     * @param txAcctNo      交易账号
     * @param disCodeList   分销机构号列表
     * @param fundCodeList   产品代码
     * @param balanceStatus 持仓状态,兼容老逻辑,注意,字段不传也是查持仓的,持仓状态,0:不持仓,1:持仓,,2:不管是否持仓
     * @return List<BalanceVo>
     */
    List<BalanceVo> selectBalanceWithLockPeriod(@Param("list") List<String> disCodeList, @Param("txAcctNo") String txAcctNo, @Param("fundCodeList") List<String> fundCodeList, @Param("balanceStatus") String balanceStatus);

    /**
     * selectNetBuyAmount:(查询代销累计购买净金额)
     *
     * @param txAcctNo
     * @param disCode
     * @param fundCodeList
     * @return
     * <AUTHOR>
     * @date 2017年11月16日 下午2:13:50
     */
    List<BalanceVo> selectNetBuyAmountDX(@Param("txAcctNo") String txAcctNo, @Param("disCode") String disCode, @Param("list") List<String> fundCodeList);

    /**
     * selectNetBuyAmountZX:((查询直销累计购买净金额))
     *
     * @param hbOneNo
     * @param fundCodeList
     * @return
     * <AUTHOR>
     * @date 2017年11月17日 上午9:22:31
     */
    List<BalanceVo> selectNetBuyAmountZX(@Param("hbOneNo") String hbOneNo, @Param("list") List<String> fundCodeList);

    /**
     * @param txAcctNo
     * @param hbOneNo
     * @param fundCodeList
     * @param disCodeList
     * @return java.util.List<com.howbuy.tms.high.orders.dao.vo.BalanceVo>
     * @Description (查询累计购买净金额)
     * <AUTHOR>
     * @Date 2018/10/26 12:05
     **/
    List<BalanceVo> selectNetBuyAmount(@Param("txAcctNo") String txAcctNo, @Param("hbOneNo") String hbOneNo,
                                       @Param("list") List<String> fundCodeList, @Param("disCodeList") List<String> disCodeList);


    /**
     * selectBalanceDtl:查询基金持仓明细
     *
     * @param txAcctNo    交易账号
     * @param disCode     分销机构号
     * @param productCode 产品代码
     * @param protocolNo  协议号
     * @param cpAcctNo    资金账号
     * @param taTradeDt   TA交易日
     * @return List<BalanceDtlVo>
     * <AUTHOR>
     * @date 2017年4月12日 下午3:21:43
     */
    List<BalanceVo> selectBalanceDtl(@Param("disCode") String disCode, @Param("txAcctNo") String txAcctNo, @Param("protocolNo") String protocolNo,
                                     @Param("productCode") String productCode, @Param("cpAcctNo") String cpAcctNo, @Param("taTradeDt") String taTradeDt);


    /**
     * 查询代销确认份额
     *
     * @param paramVo 入参
     */
    List<BalanceVo> queryConfirmBalanceBaseInfo(@Param("paramVo") QueryAcctBalanceBaseInfoParamVo paramVo);

    List<ConfirmBalanceVo> queryAgentConfirmBalance(@Param("txAcctNo") String txAcctNo, @Param("fundCode") String fundCode, @Param("disCodeList") List<String> disCodeList);

    /**
     * selectHisCpAcctNo:(查询历史购买的资金账号)
     *
     * @param txAcctNo
     * @param disCode
     * @param fundCode
     * @param taCode
     * @return
     * <AUTHOR>
     * @date 2017年7月5日 下午3:12:40
     */
    List<String> selectHisCpAcctNo(@Param("txAcctNo") String txAcctNo, @Param("disCode") String disCode, @Param("fundCode") String fundCode, @Param("taCode") String taCode);

    /**
     * selectBuyOnWayAmt:(查询买入在途总金额)
     *
     * @param disCodeList
     * @param txAcctNo
     * @return
     * <AUTHOR>
     * @date 2017年7月12日 上午11:03:09
     */
    BigDecimal selectBuyOnWayAmt(@Param("disCodeList") List<String> disCodeList, @Param("txAcctNo") String txAcctNo);

    /**
     * selectRdmOnWayBalance:(查询赎回在途资产: 产品维度)
     *
     * @param disCode
     * @param txAcctNo
     * @return
     * <AUTHOR>
     * @date 2017年7月12日 上午11:09:54
     */
    List<CustBooksPo> selectRdmOnWayBalance(@Param("disCode") String disCode, @Param("txAcctNo") String txAcctNo);

    /**
     * selectAllBonusAmt:(查询用户指定产品所有分红金额的累计)
     *
     * @param disCode
     * @param txAcctNo
     * @return
     * <AUTHOR>
     * @date 2017年12月19日 下午9:39:17
     */
    BigDecimal selectAllBonusAmt(@Param("disCode") String disCode, @Param("txAcctNo") String txAcctNo, @Param("productCode") String productCode);

    /**
     * selectByProtocolNoAndCpAcctNo:根据交易账号、协议号、资金账号、产品代码、分销机构号、份额类型查询持仓信息
     *
     * @param txAcctNo       交易账号
     * @param protocolNo     协议号
     * @param protocolType   协议类型
     * @param cpAcctNo       资金账号
     * @param productCode    产品代码
     * @param disCode        分销机构号
     * @param fundShareClass 份额类型
     * @return CustBooksPo
     * <AUTHOR>
     * @date 2017年3月17日 下午2:56:17
     */
    CustBooksPo selectByProtocolNoAndCpAcctNo(@Param("txAcctNo") String txAcctNo, @Param("protocolNo") String protocolNo, @Param("cpAcctNo") String cpAcctNo,
                                              @Param("productCode") String productCode, @Param("disCode") String disCode, @Param("fundShareClass") String fundShareClass, @Param("protocolType") String protocolType);

    /**
     * countCustTotalBalance:汇总持仓份额(银行卡维度)
     *
     * @param txAcctNo
     * @param disCode
     * @param cpAcctNo
     * @param productChannel
     * @return
     * <AUTHOR>
     * @date 2018年5月7日 下午4:54:48
     */
    BigDecimal countCustTotalBalance(@Param("txAcctNo") String txAcctNo, @Param("disCode") String disCode, @Param("cpAcctNo") String cpAcctNo,
                                     @Param("productChannel") String productChannel);

    /**
     * getCustBalanceDetail:查询基金持仓
     *
     * @param txAcctNo
     * @param disCode
     * @param cpAcctNo
     * @param productChannel
     * @return
     * <AUTHOR>
     * @date 2018年5月7日 下午4:54:48
     */
    List<CustBooksPo> getCustBalanceDetail(@Param("txAcctNo") String txAcctNo, @Param("disCode") String disCode, @Param("list") List<String> fundCodes,
                                           @Param("cpAcctNo") String cpAcctNo, @Param("productChannel") String productChannel);

    /**
     * selectUnconfirmedVol:查询未确认份额
     *
     * @param txAcctNo
     * @param fundCode
     * @return
     * <AUTHOR>
     * @date 2018年7月6日 上午10:30:05
     */
    List<CustBooksPo> selectUnconfirmedVol(@Param("disCodeList") List<String> disCodeList, @Param("txAcctNo") String txAcctNo, @Param("fundCode") String fundCode);

    /**
     * selectUnconfirmedVolByCpAcctNo:根据银行卡汇总未确认份额
     *
     * @param disCodeList
     * @param txAcctNo
     * @param fundCode
     * @param cpAcctNo
     * @return
     * <AUTHOR>
     * @date 2018年7月18日 下午4:16:04
     */
    List<CustBooksPo> selectUnconfirmedVolByCpAcctNo(@Param("disCodeList") List<String> disCodeList, @Param("txAcctNo") String txAcctNo,
                                                     @Param("fundCode") String fundCode, @Param("cpAcctNo") String cpAcctNo);

    /**
     * @param txAcctNo
     * @param fundCodes
     * @return java.util.List<com.howbuy.tms.high.orders.dao.po.CustBooksPo>
     * @Description 根据产品代码集合查询客户持仓
     * <AUTHOR>
     * @Date 2019/6/3 16:45
     **/
    List<CustBooksPo> selectCustBooksByTxAcctNoOrFundCodes(@Param("txAcctNo") String txAcctNo, @Param("fundCodes") List<String> fundCodes);

    /**
     * 查询持仓明细，若有子账本，按子账本展示
     *
     * @param disCodeList
     * @param txAcctNo
     * @param productCode
     * @return java.util.List<com.howbuy.tms.high.orders.dao.vo.BalanceVo>
     * @author: huaqiang.liu
     * @date: 2021/7/7 16:54
     * @since JDK 1.8
     */
    List<BalanceVo> selectBalanceDtlWithOpenDt(@Param("disCodeList") List<String> disCodeList, @Param("txAcctNo") String txAcctNo, @Param("productCode") String productCode);

    /**
     * selectBalanceDtl:查询基金持仓明细
     *
     * @param txAcctNo    交易账号
     * @param disCodeList 分销机构号列表
     * @param productCode 产品代码
     * @param protocolNo  协议号
     * @param cpAcctNo    资金账号
     * @param taTradeDt   TA交易日
     * @return List<BalanceDtlVo>
     * <AUTHOR>
     * @date 2022年1月4日 下午3:21:43
     */
    List<BalanceVo> selectBalanceDtlByDisCodeList(@Param("disCodeList") List<String> disCodeList, @Param("txAcctNo") String txAcctNo, @Param("protocolNo") String protocolNo,
                                                  @Param("productCode") String productCode, @Param("cpAcctNo") String cpAcctNo, @Param("taTradeDt") String taTradeDt);

    /**
     * selectConsignmentOnWayAmt:(查询代销在途金额)
     *
     * @param disCodeList
     * @param txAcctNo
     * @return
     * <AUTHOR> @date
     */
    List<CustBooksPo> selectConsignmentOnWayAmt(@Param("disCodeList") List<String> disCodeList, @Param("txAcctNo") String txAcctNo);



    int getUnAckNum(@Param("txAcctNo") String txAcctNo, @Param("productCode") String productCode);

    /**
     * 千禧年产品查询累计购买净金额直销，仅查询120、130、122
     *
     * @param hbOneNo
     * @param fundCodeList
     * @return
     */
    List<BalanceVo> selectNetBuyAmountQX(@Param("hbOneNo") String hbOneNo, @Param("list") List<String> fundCodeList);

    List<String> selectStockBooksByTxAcctNoAndDisCode(@Param("txAcctNo") String txAcctNo, @Param("disCode") String disCode);

    /**
     * 查询好臻产品用户信息
     */
    List<FundAcctVo> selectHzBalanceByFundCode(@Param("fundCode") String fundCode, @Param("txAcctNo") String txAcctNo);

    int isIntransitForCloseAct(@Param("txAcctNo")String txAcctNo, @Param("disCode")String disCode, @Param("cpAcctNos")List<String> cpAcctNos, @Param("protocolNo")String protocolNo);

    List<BalanceVo> getHighCustBalanceDetail(@Param("txAcctNo")String txAcctNo, @Param("disCode")String disCode, @Param("fundCode")String fundCode, @Param("cpAcctNos")List<String> cpAcctNos);

    List<String> queryBalanceTxAcctNoByFundCode(@Param("fundCode")String fundCode);

    List<String> queryBalanceTxAcctNoByPage(@Param("offset")int offset,@Param("pageSize") int pageSize);

    List<String> queryUnConfirmedOrderFundCode(@Param("unBalanceFundCodeSet")Set<String> unBalanceFundCodeSet, @Param("txAcctNo")String txAcctNo);
}