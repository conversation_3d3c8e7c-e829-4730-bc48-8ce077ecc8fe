package com.howbuy.tms.high.orders.service.repository;

import com.howbuy.tms.high.orders.dao.mapper.customize.CmCusttradeDirectPoMapper;
import com.howbuy.tms.high.orders.dao.po.CmCusttradeDirectPo;
import com.howbuy.tms.high.orders.dao.vo.AckDealOrderInfo;
import com.howbuy.tms.high.orders.dao.vo.BalanceOrderVo;
import com.howbuy.tms.high.orders.dao.vo.QueryAcctBalanceBaseInfoParamVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class CmCusttradeDirectRepository {
    @Autowired
    private CmCusttradeDirectPoMapper cmCusttradeDirectPoMapper;

    public List<BalanceOrderVo> selectBalanceDirectOrderVo(String hbOneNo, List<String> fundCodeList, List<String> disCodeList) {
        return cmCusttradeDirectPoMapper.selectBalanceDirectOrderVo(hbOneNo, fundCodeList, disCodeList);
    }

    public List<CmCusttradeDirectPo> getOnWayDirectBalance(QueryAcctBalanceBaseInfoParamVo paramVo) {
        return cmCusttradeDirectPoMapper.getOnWayDirectBalance(paramVo);
    }

    public CmCusttradeDirectPo getFirstAckInfoForDirect(String hbOneNo, String productCode) {
        return cmCusttradeDirectPoMapper.getFirstAckInfoForDirect(hbOneNo, productCode);
    }

    public CmCusttradeDirectPo getLastAckInfoForDirect(String hbOneNo, String productCode) {
        return cmCusttradeDirectPoMapper.getLastAckInfoForDirect(hbOneNo, productCode);
    }

    public List<AckDealOrderInfo> selectAckDealDtl(String hbOneNo,  List<String> fundCodeList) {
        return cmCusttradeDirectPoMapper.selectAckDealDtl(hbOneNo, fundCodeList);
    }


    public List<CmCusttradeDirectPo> selectDirectOnWayAmt(List<String> disCodeList, String txAcctNo) {
        return cmCusttradeDirectPoMapper.selectDirectOnWayAmt(disCodeList, txAcctNo);
    }
}
