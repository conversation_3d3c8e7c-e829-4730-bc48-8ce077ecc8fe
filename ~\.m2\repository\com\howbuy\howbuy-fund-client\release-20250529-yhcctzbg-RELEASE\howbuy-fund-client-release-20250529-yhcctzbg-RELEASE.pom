<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0"><modelVersion>4.0.0</modelVersion>
    <groupId>com.howbuy</groupId>
    <artifactId>howbuy-fund-client</artifactId>
    <version>release-20250529-yhcctzbg-RELEASE</version>
    <properties>
        <com.howbuy.howbuy-persistence.version>release-20250425-hw2.9-RELEASE</com.howbuy.howbuy-persistence.version>
        <com.howbuy.howbuy-fund-dto.version>release-20240705-ccpzjy-RELEASE</com.howbuy.howbuy-fund-dto.version>
    </properties>
    <name>howbuy-fund-client</name>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-infrastructure</artifactId>
                <version>1.0.0-SNAPSHOT</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.msgpack</groupId>
            <artifactId>msgpack-core</artifactId>
            <version>0.8.16</version>
        </dependency>
        <dependency>
            <groupId>org.msgpack</groupId>
            <artifactId>jackson-dataformat-msgpack</artifactId>
            <version>0.8.16</version>
        </dependency>
        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>howbuy-fund-dto</artifactId>
            <version>${com.howbuy.howbuy-fund-dto.version}</version>
        </dependency>
        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>howbuy-persistence</artifactId>
            <version>${com.howbuy.howbuy-persistence.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.howbuy</groupId>
                    <artifactId>utils</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.vdurmont</groupId>
                    <artifactId>emoji-java</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>utils</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>1.6.4</version>
        </dependency>
        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
            <version>3.2.1</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>2.6.1</version>
        </dependency>
        <!-- Apache Commons -->
        <dependency>
            <groupId>com.howbuy.fp</groupId>
            <artifactId>howbuy-comcalculate</artifactId>
            <version>1.1.5</version>
        </dependency>
        <!-- Apache Commons -->
        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <version>2.6</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.58</version>
        </dependency>
        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
            <version>2.0.1.Final</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.2</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>


    <build>
        <plugins>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>2.3.2</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-dependency-plugin</artifactId>
                <version>3.1.1</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.2.1</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <phase>package</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <defaultGoal>install</defaultGoal>
    </build>
    <profiles>
        <profile>
            <id>hudson-profile</id>
            <repositories>
                <repository>
                    <id>howbuy-nexus</id>
                    <name>howbuy nexus</name>
                    <url>http://192.168.208.22:8081/nexus/content/groups/public/</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                        <updatePolicy>interval:10</updatePolicy>
                    </snapshots>
                </repository>
            </repositories>
            <build>
                <plugins>
                    <!-- 打包源码 -->
                    <plugin>
                        <artifactId>maven-source-plugin</artifactId>
                        <version>2.1</version>
                        <configuration>
                            <attach>true</attach>
                        </configuration>
                        <executions>
                            <execution>
                                <phase>compile</phase>
                                <goals>
                                    <goal>jar</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>

                </plugins>
            </build>
        </profile>
        <profile>
            <id>javadoc</id>
            <build>
                <plugins>
                    <!--生成javadoc-->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-javadoc-plugin</artifactId>
                        <version>3.1.0</version>
                        <executions>
                            <execution>
                                <id>attach-javadocs</id>
                                <goals>
                                    <goal>jar</goal>
                                </goals>
                            </execution>
                        </executions>
                        <configuration>
                            <show>private</show>
                            <doclint>none</doclint>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
    <distributionManagement>
        <repository>
            <id>howbuy-release</id>
            <name>howbuy-release</name>
            <url>http://nx-java.howbuy.pa/repository/howbuy-release/</url>
        </repository>
        <snapshotRepository>
            <id>howbuy-snapshot</id>
            <name>howbuy-snapshot</name>
            <url>http://nx-java.howbuy.pa/repository/howbuy-snapshot/</url>
        </snapshotRepository>
    </distributionManagement>
</project>