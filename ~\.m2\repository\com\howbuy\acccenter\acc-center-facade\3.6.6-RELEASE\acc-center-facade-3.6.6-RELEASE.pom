<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.howbuy.acccenter</groupId>
		<artifactId>acc-center</artifactId>
		<version>3.6.6-RELEASE</version>
		<relativePath>../pom.xml</relativePath>
	</parent>

	<artifactId>acc-center-facade</artifactId>
	<packaging>jar</packaging>
	<name>acc-center-facade</name>


	<dependencies>
		<dependency>
			<groupId>com.howbuy.common</groupId>
			<artifactId>common-facade</artifactId>
		</dependency>

		<dependency>
			<groupId>com.howbuy.acccenter</groupId>
			<artifactId>acc-center-common</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.freemarker</groupId>
					<artifactId>freemarker</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.xhtmlrenderer</groupId>
					<artifactId>core-renderer</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.lowagie</groupId>
					<artifactId>itext</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.belerweb</groupId>
					<artifactId>pinyin4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.howbuy</groupId>
					<artifactId>howbuy-cachemanagement</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.howbuy</groupId>
					<artifactId>howbuy-ccms-watcher</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
		</dependency>

		<dependency>
			<groupId>commons-lang</groupId>
			<artifactId>commons-lang</artifactId>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<!-- 打包源码 -->
			<plugin>
				<artifactId>maven-source-plugin</artifactId>
				<version>2.1</version>
				<configuration>
					<attach>true</attach>
				</configuration>
				<executions>
					<execution>
						<phase>compile</phase>
						<goals>
							<goal>jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>2.3.2</version>
				<configuration>
					<source>1.6</source>
					<target>1.6</target>
					<encoding>UTF-8</encoding>
				</configuration>
			</plugin>
		</plugins>
	</build>

</project>