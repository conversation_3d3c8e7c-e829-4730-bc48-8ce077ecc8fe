package com.howbuy.tms.high.orders.dao.mapper.customize;

import com.howbuy.tms.high.orders.dao.mapper.CmCusttradeDirectPoAutoMapper;
import com.howbuy.tms.high.orders.dao.po.CmCustFundDirectPo;
import com.howbuy.tms.high.orders.dao.po.CmCusttradeDirectPo;
import com.howbuy.tms.high.orders.dao.vo.AckDealOrderInfo;
import com.howbuy.tms.high.orders.dao.vo.BalanceOrderVo;
import com.howbuy.tms.high.orders.dao.vo.QueryAcctBalanceBaseInfoParamVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 去O
 */
public interface CmCusttradeDirectPoMapper extends CmCusttradeDirectPoAutoMapper {

    /**
     * 查询第一笔
     *
     * @param hbOneNo
     * @param productCode
     * @return
     */
    CmCusttradeDirectPo getFirstAckInfoForDirect(@Param("hbOneNo") String hbOneNo, @Param("productCode") String productCode);

    /**
     * selectBuyOnWayAmt:(查询直销在途金额)
     *
     * @param disCodeList
     * @param txAcctNo
     * @return
     * <AUTHOR> @date
     */
    List<CmCusttradeDirectPo> selectDirectOnWayAmt(@Param("disCodeList") List<String> disCodeList, @Param("txAcctNo") String txAcctNo);


    /**
     * 查询最新一笔
     *
     * @param hbOneNo
     * @param productCode
     * @return
     */
    CmCusttradeDirectPo getLastAckInfoForDirect(@Param("hbOneNo") String hbOneNo, @Param("productCode") String productCode);

    /**
     * 查询持仓订单信息
     *
     * @param hbOneNo      一账通账号
     * @param fundCodeList 产品编码
     * @return 订单信息
     */
    List<BalanceOrderVo> selectBalanceDirectOrderVo(@Param("hbOneNo") String hbOneNo,
                                                    @Param("fundCodeList") List<String> fundCodeList,
                                                    @Param("disCodeList") List<String> disCodeList);

    List<CmCusttradeDirectPo> getOnWayDirectBalance(@Param("paramVo") QueryAcctBalanceBaseInfoParamVo paramVo);

    List<AckDealOrderInfo> selectAckDealDtl(@Param("hbOneNo")String hbOneNo,  @Param("fundCodeList")List<String> fundCodeList);
}