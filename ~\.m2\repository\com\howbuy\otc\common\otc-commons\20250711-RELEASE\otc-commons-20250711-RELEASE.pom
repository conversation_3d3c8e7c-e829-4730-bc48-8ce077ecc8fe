<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.howbuy.otc.common</groupId>
  <artifactId>otc-commons</artifactId>
  <version>20250711-RELEASE</version>
  <packaging>pom</packaging>
  
  	<properties>
		<project.reporting.outputEncoding>utf-8</project.reporting.outputEncoding>
		<com.howbuy.otc-commons.version>20250711-RELEASE</com.howbuy.otc-commons.version>
		<com.howbuy.otc-common.version>20250711-RELEASE</com.howbuy.otc-common.version>
</properties>

	<distributionManagement>
		<repository>
			<id>howbuy-release</id>
			<name>howbuy-release</name>
			<url>http://nx-java.howbuy.pa/repository/howbuy-release/</url>
		</repository>
		<snapshotRepository>
			<id>howbuy-snapshot</id>
			<name>howbuy-snapshot</name>
			<url>http://nx-java.howbuy.pa/repository/howbuy-snapshot/</url>
		</snapshotRepository>
	</distributionManagement>
	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.1</version>
				<configuration>
					<source>1.8</source>
					<target>1.8</target>
					<encoding>UTF8</encoding> 
				</configuration>
			</plugin>
		</plugins>
	</build>
	
	</project>