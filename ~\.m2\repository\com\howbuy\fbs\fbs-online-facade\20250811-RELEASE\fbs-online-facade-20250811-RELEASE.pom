<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.howbuy.fbs</groupId>
		<artifactId>fbs-online</artifactId>
		<version>20250811-RELEASE</version>
		<relativePath>../pom.xml</relativePath>
	</parent>

	<artifactId>fbs-online-facade</artifactId>
	<packaging>jar</packaging>
	<name>fbs-online-facade</name>

	<dependencies>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<scope>provided</scope>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.3</version>
				<configuration>
					<source>1.7</source>
					<target>1.7</target>
					<encoding>UTF-8</encoding>
					<compilerArgument>-XDignore.symbol.file</compilerArgument>
				</configuration>
			</plugin>
		</plugins>
	</build>


</project>